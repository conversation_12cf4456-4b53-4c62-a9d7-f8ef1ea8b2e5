GET /api/v1/prequalification

GET /api/v1/prequalification?as=pic

GET /api/v1/prequalification?as=btb

GET /api/v1/prequalification/:prequalificationId

GET /api/v1/prequalification/:prequalificationId/vendor

PUT /api/v1/prequalification/:prequalificationId

POST /api/v1/prequalification/:prequalificationId/submit
POST /api/v1/prequalification/:prequalificationId/approve
POST /api/v1/prequalification/:prequalificationId/sendback


GET /api/v1/prequalification/:prequalificationId/document/:document_code

GET /api/v1/prequalification/:prequalificationId/vendor/:vendorId
PUT /api/v1/prequalification/:prequalificationId/vendor/:vendorId

POST /api/v1/prequalification/:prequalificationId/vendor/:vendorId/approve
POST /api/v1/prequalification/:prequalificationId/vendor/:vendorId/sendback

GET /api/v1/prequalification/:prequalificationId/meeting

PUT /api/v1/prequalification/:prequalificationId/meeting

GET /api/v1/prequalification/:prequalificationId/evaluation

PUT /api/v1/prequalification/:prequalificationId/evaluation

GET /api/v1/prequalification/:prequalificationId/vendor/:vendorId/evaluation

POST /api/v1/prequalification/:prequalificationId/vendor/:vendorId/evaluation/submit

GET /api/v1/prequalification/:prequalificationId/vendor/:vendorId/clarification

POST /api/v1/prequalification/:prequalificationId/vendor/:vendorId/clarification/submit





POST /login

GET /prequalification

GET /prequalification/:prequalificationId

GET /prequalification/:prequalificationId/registration

POST /prequalification/:prequalificationId/registration

GET /prequalification/:prequalificationId/requirement

POST /prequalification/:prequalificationId/requirement

GET /prequalification/:prequalificationId/submission

POST /prequalification/:prequalificationId/submission

GET /prequalification/:prequalificationId/evaluation

POST /prequalification/:prequalificationId/evaluation

GET /prequalification/:prequalificationId/clarification

POST /prequalification/:prequalificationId/clarification

GET /experience

POST /experience

PUT /experience/:experienceId

DELETE /experience/:experienceId

https://gitlab.com/mgtteamproject/eproc/eproc-be/-/merge_requests/212/diffs?commit_id=4cadaa528b75aa44a577c4a133aca141bba1c463




vendor pq get one header response

{
    id: "string",
    title: "Purchase Wellhead and Installation Services for HCML M's Development Wells",
    tenderCode: "GA210301",
    generalScopeOfWorks:
    "Husky‐CNOOC Madura Limited (HCML) membutuhkan penyediaan Wellhead untuk sumur pengembangan, termasuk layanan engineering. Pembelian Wellhead dilakukan berdasarkan Call Off.",
    pqType: "ANNOUNCEMENT",
    pqRegistrationDate: "2024-10-12T07:00:00.000Z",
    businessClass: "LARGE" as "SMALL" | "MEDIUM" | "LARGE",
    businessField: ["4659", "9100", "2824", "2511"],
    businessLicense: "SIUP/IUT/IUI/TDI/NIB Berbasis Risiko",
    localContentLevel: 10,
    eastJavaProve: "NIB Berbasis Risiko",
}

interface Person {
  name: string;
  age: number;
  email: string;
  address: string;
}

type BasicInfo = Pick<Person, 'name' | 'age'>;

// BasicInfo is { name: string; age: number; }

interface Person {
  name: string;
  age: number;
  email: string;
}

type PersonWithoutEmail = Omit<Person, 'email'>;

// PersonWithoutEmail is { name: string; age: number; }



•⁠  ⁠my pq, samain kaya pq info, data pq_template (vendor) V V
•⁠  ⁠vendor pq get all, pqtemplate di root, baru items (k3s) V V
•⁠  ⁠bug approval pas sequence ke 5 tau2 berubah jadi phase registration (k3s requirement approval) V V
•⁠  ⁠registration date (trigger dari vendor submit), evaluation date (trigger dari approval) V V
•⁠  ⁠additional feature, kalau next approval id nya sama, sekaligus approve juga next sequence nya ?
•⁠  ⁠bug approval kalau ada yg ke skip, array nya kosong (k3s requirement) V V



pq registration rule
if commodity "services" TDKN field not required
if "ownerEstimateValue" > $1m document domicile not required

PQ Requirement, validasi bila 0 - 1jt bisa nasional tapi harus justification bila >1jt otomatis nasional. Bila … perusahaan asing bisa ikut


KD NPt kalo ga ada vendor harus isi bukti pengalaman?
form vendor submission field bukti status bendera di figma belum ada

rumus upp value (USD)
// proc_plan_header totalValueEstimation
[ 
  { "value": 820000, "currency": "USD" },
  { "value": 2000000000, "currency": "IDR" }
]

820_000 + (2_000_000_000 / 10_000)

contoh data email template
procplanUppHeaderGetAll
https://eproc.kangservice.cloud/employee/procurement-plan/request 

goto detail email 
procplanUppHeaderGetOne
https://eproc.kangservice.cloud/employee/procurement-plan/request/upp?section=70212000


email reminder saat sendback ke submitter


response get UPP detail (GET/api/v1/procplan/upp/section/${sectionId}?year=${year} GET /api/v1/procplan/upp/header/${documentId}?year=${year})

API Submit UPP (POST /api/v1/procplan/upp/header/${data.procPlanHeader.id}/submit)

API Save UPP Detail (POST /api/v1/procplan/upp/detail 
PUT /api/v1/procplan/upp/detail/${data.id})

API Delete UPP Detail (DELETE /api/v1/procplan/upp/detail/${item.id}) V

cookie:
console.log(JSON.stringify(getJSONName("Gj74vHWLyI1c-c6WrFzXdTdB8C1cvU_FTGphPoHvJOAA5I00W9BeQMSe-zTtLjG8q2fQs1TwsLIjiQTE6MnVybcCN9ZuiIGNhUQ3r37kIuk"), null, 2));
console.log(JSON.stringify(getJSONName("Gj74vHWLyI1c-c6WrFzXdS2Q1qnXvfIPD0YKO1WS5ZDNpydmbdPA9yUMkACNmCjNV-QhbhtEWbfS-upQiJUNR6wg_Vj7tdwd29-k-U1AdzX2IhqTtuIFYxp2SjOqUB-N"), null, 2));

requestfor == section termasuk dia sendiri
rbtb == department

UPP List
eligibility
read only kalo beda section

requestfor bisa tampil bila masih satu department
requestfor hanya tampil yg non contractor
requestfor select upp bila supervisor user kosong tidak tampil di select

UPP Budget Information bila value nya > Rp.5m / $500.000 tech mom & wpnb otomatis ke ceklis, tapi wpnb bisa di uncheck

requisition insurance requirement user dapat mengisi, dan bagian insurance dapat mengupdate

Question:
1. Data ditable _supervisor priority yg mana
2. Ms login solusi untk user ali mustofa
3. kebutuhan guide untuk rule pengisian form (UPP Budget Information)
4. login vendor CIVD
  login vendor check data vendor dari koneksi db vera "vendor_account.vendor_infoId" = "vendor_info.id", 
  check spda dari data CIVD berdasarkan vendor NPWP. 
  bila spda habis tidak bisa ikut PQ
  compare password https://www.npmjs.com/package/bcryptjs https://www.npmjs.com/package/@types/bcryptjs


******** - Brown Field Engineering & Construction -> supervisor ke ******** Asset Integrity & Inspection
IP public di kubet buat di allow di aws

thomas goldy supervisorPositionId = ********

update vendor CIVD getSusunanPengurus saat vendor buka halaman pq process
email reminder sudah di approve untuk requestfor 
email reminder untuk rbtb
bug email prisa ke kirim ke mailtrap

(V) Request for: seluruh personil di Department yang sama (kecuali Kontraktor).
(V) Back to Back / Delegate: seluruh personil di Department yang sama termasuk Kontraktor.
(P) Reminder belum berjalan, baik untuk reviu dan approval maupun for information.
(V) Tidak ada tombol action (Send Back, Approve, Reject).
(P) Data susunan pengurus pada CIVD akan ditarik pada saat Vendor buka halaman PQ.
(V) bila tidak ada atasan/head langsung ke pak oky (head pbr). Apabila Personil pada Request For tidak memiliki atasan (Head / Sr. Head) maka proses akan dilanjutkan ke PBR.

hapus salahsatu section pak aries
add approval template group baru di db untuk UPP
revert thomas goldy ke atasan posisi sebelumnya

postgresql://readonly_user:P%40ssw0rd01%40123@localhost:5433/vera?statusColor=686B6F&env=production&name=E-Proc%20Vera&tLSMode=0&usePrivateKey=false&safeModeLevel=1&advancedSafeModeLevel=1&driverVersion=0&lazyload=true


atasan oky eldyagusta -> listiani dewi -> SMVP

person.id kemungkinan di edit manual?

screen dashboard procurement plan jumlah approval "waiting for review : 1 section"
image email di jgn dari url external tapi di embeded di emailnya
email title format "Prisa - {Title}"

Image dijadikan satu dengan body email
outlook client test email
Spesifik:

Judul: PRISA - Approval for Procurement Plan
Isi email: Approval for Procurement Plan, Dear Mr./Mrs. Oky Eldyagusta, The following Procurement Plan require your review and approval: Department - Section - Quantity - Value (USD) - Link, Thank you for your attention and cooperation. Best regards, PBR Department

Umum:

Judul: PRISA - Approval List
Isi email: Approval List, Dear Mr./Mrs. Oky Eldyagusta, The following list require your review and approval: Description - Department - Section - Quantity - Value (USD) - Link, Thank you for your attention and cooperation. Best regards, PBR Department


(V) upp list pagination >10 ga ke load  
email notification sendback
(Pending konfirmasi rule sesama section bisa action) UPP list button action saat sendback di disable di user head

(Pending konfirmasi rule sesama section bisa action) Tombol Edit dan Hapus hanya untuk Creator, Request For, Back to Back dan PBR (Vendor Management).
(V) Tombol submit hanya ada di creator.
(V) Ada tombol Send Back dan Approve ada di setiap Reviewer atau Approver.
(V) Ketika di send back, proses akan Kembali ke creator.
Notifikasi email untuk Sent Back.
Notifikasi email Sent Back: The following Procurement Plan has been sent back
dokumen APP yg di send back hilang mulai proses lagi di UPP
APP sendback jadi UPP status draft. Ketika di send back dan proses ada VM akan Kembali ke creator sebagai UPP.
Ketika di send back dan proses sudah melewati VM akan Kembali ke VM sebagai APP.


TODO email
procplan upp when submit and approve:
- notification for request for user / first approval in list (V)
- reminder for next approval user exists (V)
- reminder when upp approved and start app (V)

procplan upp when send back:
- notification for submitter user (V)
- notification for request for user (V)

procplan app when submit and approve:
- notification request for user upp / first approval upp in list if app request for exists ()
- reminder for next approval user exists (V)

procplan app vendor management when send back:
- notification for submiter user (V)
- notification for request for user (V)

procplan upp when send back:
- notification for vendor management user? ()


section undefined kalo pak oky sebagai HEAD pengganti (approval procplan UPP). eror yang dari 3 department tadi (legal, relations dan HR & GA)

# ************** IP yg teregister di aws utk email

akses db vera dari development / kangservice:
- pake proxy (https://www.npmjs.com/package/express-http-proxy)
- IP kangservice whitelist untuk akses db vera :
  - ************** 
  - **************

duration days save di approval_history

APP approval bila user tidak punya manager bagaimana?

department:
- Project MDA-MBH-MDK
- Subsurface
tidak tampil di KANG AN karena tidak ada manager


Procplan filter tidak usah ditambah 1 tahunnya
highlight tahun lebih terbaca

VM - Pramadiathalla
Oky - Head PBR
user Planning, Bidding & Reporting hanya	dapat read only list APP

APP approval bila head kosong langsung ke manager

sync jam 9-12
delegation:
diakses dari menu navigasi approval 
delegation_id = position_id


CREATE OR REPLACE VIEW
  "public"."todaydelegation" AS
SELECT
  a.id,
  COALESCE(a."delegationId", a."actingId") AS delegationid,
  pos.name AS positiondelegator,
  COALESCE(p1.name, a.id) AS delegatorname,
  p2.name AS delegationname,
  p2.email AS delegationemail,
  a."startDate",
  a."endDate",
  a.remark
FROM
  authority a
  LEFT JOIN person p1 ON a.id = p1."positionId"
  LEFT JOIN person p2 ON COALESCE(a."delegationId", a."actingId") = p2.id
  LEFT JOIN "position" pos ON a.id = pos.id;


requisition Additional Procurement Plan Checklist, field explain => "reason", value can empty text like "remarks"
requisition Additional Procurement Plan Checklist, field Incidental operational needs have additional free text
Direct Appointment Checklist tidak bisa di close bila tidak di checklist semua
Duration (days)	menggunakan hari kerja, exclude hari libur


