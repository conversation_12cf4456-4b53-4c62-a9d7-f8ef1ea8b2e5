pqVendorGetAll         GET /pq/:pqId/vendor
pqDocumentGetAll       GET /pq/:pqId/document/:document_code



pqGetAll               GET /pq


REQUIREMENT (has approval)

pqRequirementGetOne    GET /pq/:pqId/requirement
pqRequirementUpdate    PUT /pq/:pqId/requirement

pqRequirementSubmit   POST /pq/:pqId/requirement/submit
pqRequirementApprove  POST /pq/:pqId/requirement/approve
pqRequirementSendback POST /pq/:pqId/requirement/sendback



REGISTRATION (just simple approval)

pqRegistrationGetOne    GET /pq/:pqId/registration/vendor/:vendorId
pqRegistrationUpdate    PUT /pq/:pqId/registration/vendor/:vendorId

pqRegistrationApprove   GET /pq/:pqId/registration/vendor/:vendorId/approve
pqRegistrationSendback  PUT /pq/:pqId/registration/vendor/:vendorId/sendback



MEETING ()

pqMeetingGetOne         GET /pq/:pqId/meeting
pqMeetingUpdate         PUT /pq/:pqId/meeting



EVALUATION (has approval)

pqEvaluationGetOne      GET /pq/:pqId/evaluation/vendor/:vendorId
pqEvaluationUpdate      PUT /pq/:pqId/evaluation/vendor/:vendorId

pqEvaluationApprove     GET /pq/:pqId/evaluation/vendor/:vendorId/approve
pqEvaluationSendback    PUT /pq/:pqId/evaluation/vendor/:vendorId/sendback



CLARIFICATION (has approval)

pqClarificationGetOne   GET /pq/:pqId/clarification/vendor/:vendorId
pqClarificationUpdate   PUT /pq/:pqId/clarification/vendor/:vendorId

pqClarificationApprove  GET /pq/:pqId/clarification/vendor/:vendorId/approve
pqClarificationSendback PUT /pq/:pqId/clarification/vendor/:vendorId/sendback



=====

pq_requirement_getone
pq_requirement_update
pq_requirement_submit
pq_requirement_approve
pq_requirement_sendback

pq_registration_getone
pq_registration_update
pq_registration_approve
pq_registration_sendback

pq_meeting_getone
pq_meeting_update

pq_evaluation_getone
pq_evaluation_update
pq_evaluation_approve
pq_evaluation_sendback

pq_clarification_approve
pq_clarification_getone
pq_clarification_sendback
pq_clarification_update

pq_document

pq_getall

pq_vendor_getall

---

vendor_pq_registration_getone
vendor_pq_registration_submit

vendor_pq_requirement_getone
vendor_pq_requirement_submit

vendor_pq_submission_getone
vendor_pq_submission_submit

vendor_pq_evaluation_getone
vendor_pq_evaluation_submit

vendor_pq_clarification_getone
vendor_pq_clarification_submit

vendor_pq_getall

vendor_pq_login



===============================================

businessField & businessLicense new format become businessFields:
{
  "businessLicense": [
    {
      "license": "SIUJPTL",
      "fields": [
        {
          "id": "Z40H1K66T525T9UU",
          "documentName": "SIUJPTL",
          "categoryCode": "A",
          "clasification": "KONSULTASI DALAM BIDANG INSTALASI PENYEDIAAN TENAGA LISTRIK"
        },
        {
          "id": "RH5676LUN5VMM9TS",
          "documentName": "SIUJPTL",
          "categoryCode": "A01",
          "clasification": "PEMBANGKITAN TENAGA LISTRIK"
        },
        {
          "id": "4E555B995OTT1I9A",
          "documentName": "SIUJPTL",
          "categoryCode": "A0101",
          "clasification": "PEMBANGKIT LISTRIK TENAGA UAP"
        }
      ]
    }
  ]
}