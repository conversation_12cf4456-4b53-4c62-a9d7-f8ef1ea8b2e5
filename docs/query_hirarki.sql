MEMETAKAN SEMUA ROW USER -- masih error
--------------------------
WITH RECURSIVE cte("positionId", "name", "supervisorPositionId", depth, path) AS (
   SELECT 
      tn."positionId",
      tn."name",
      tn."supervisorPositionId",
      1::INT AS depth,
      tn."positionId"::TEXT AS path 
   FROM "user" as tn 
   WHERE tn."supervisorPositionId" = ''

   UNION ALL

   SELECT 
      c."positionId",
      c."name",
      c."supervisorPositionId",
      p.depth + 1 AS depth,
      (p.path || '->' || c."positionId"::TEXT) 
   FROM cte AS p, "user" AS c 
   WHERE c."supervisorPositionId" = p."positionId"
)
SELECT * 
FROM cte as n

CARI SEMUA CHILD DARI USER
-----------------------------
WITH RECURSIVE cte("positionId", "name", "supervisorPositionId", "depth", "path") AS (
   SELECT 
      tn."positionId",
      tn."name",
      tn."supervisorPositionId",
      1::INT AS "depth",
      tn."positionId"::TEXT AS "path" 
   FROM "user" AS tn 
   WHERE tn."positionId" = '80020000'
UNION ALL                   
   SELECT c."positionId", 
      c."name", 
      c."supervisorPositionId", 
      p."depth" + 1 AS "depth", 
      (p."path" || '->' || c."positionId"::TEXT) 
   FROM cte AS p, "user" AS c 
   WHERE c."supervisorPositionId" = p."positionId"
)                                                                
SELECT * FROM cte AS n;

CARI SEMUA CHILD DARI USER DENGAN BATAS KEDALAMAN
----------------------------------------------------
WITH RECURSIVE cte("positionId", "name", "supervisorPositionId", "depth", "path") AS (
   SELECT 
      tn."positionId",
      tn."name",
      tn."supervisorPositionId",
      1::INT AS "depth",
      tn."positionId"::TEXT AS "path" 
   FROM "user" AS tn 
   WHERE tn."positionId" = '80020000'
UNION ALL                   
   SELECT c."positionId", 
      c."name", 
      c."supervisorPositionId", 
      p."depth" + 1 AS "depth", 
      (p."path" || '->' || c."positionId"::TEXT) 
   FROM cte AS p, "user" AS c 
   WHERE c."supervisorPositionId" = p."positionId" AND p."depth" < 2
)                                               
SELECT * FROM cte AS n;

CARI SEMUA ATASAN USER
-------------------------
WITH RECURSIVE cte("positionId", "name", "supervisorPositionId", "depth", "path") AS (
   SELECT 
      tn."positionId", 
      tn."name", 
      tn."supervisorPositionId", 
      1::INT AS "depth", 
      tn."positionId"::TEXT AS "path" 
   FROM "user" AS tn 
   WHERE tn."positionId" = '80190163'
UNION ALL
   SELECT 
      p."positionId", 
      p."name", 
      p."supervisorPositionId", 
      c."depth" + 1 AS "depth", 
      (c."path" || '->' || p."positionId"::TEXT) 
   FROM cte AS c, "user" AS p 
   WHERE c."supervisorPositionId" = p."positionId"
)
SELECT * FROM cte AS n;