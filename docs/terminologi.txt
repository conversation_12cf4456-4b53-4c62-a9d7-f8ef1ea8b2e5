General Manager :
  - department_id same as section_id
  - the name of the department and section is "Executive"
  - department code is empty
  - user who doesn't have a supervisor (supervisor_position_id === position_id or supervisor_position_id === empty)

Senior Manager or Vice President :
  - department_id same as section_id
  - the name of the department and section is "Executive"
  - department code is empty
  - position name start with "VP" or "Sr Manager"
  - supervisor_position_id is a General Manager

Manager :
  - department_id same as section_id
  - position name start with "Manager"
  - supervisor_position_id is a General Manager

Senior Head :
  - ?

Head :
  - position name start with "Head" 
  - has a members

Staff :
  - doesn't have a members ?

Executive user :
  - General Manager and Senior Manager or Vice President

Structural User :
  - Anyone that has supervisor
  - General Manager, Senior Manager, Vice President, Manager, Senior Head, Head, Staff

there are 2 type user in approval_template_list :
  - user defined with role : will be translated to position_id in approval_list
  - users defined with id's : specific user with id and can be multiple

there are 2 type user in approval_list :
  - user defined with position.id
  - users defined with id's for "or" approval

creator :
  - basically anyone who can login the application

requesfor :
  - can be the creator it self as long as satisfies all the below rules
  - a user that his/her section is same as the selected section
  - user which his/her role is mentioned in the first item of approval_template_list 
  - should fail as requestFor :
    - user from other section
    - executive user 

rbtb :
  - users in the same department of requesfor
  - should fail as rbtb :
    - executive user 

UPP create :
  - creator

UPP update / delete / submit :
  - the one who is defined as UPP creator in each details
  - the one who is defined as requesfor in each details
  - anyone who is defined as rbtb's user in each details

UPP submit auto approve :
  - only if there is no other approver after submit

UPP sendback / approve:
  - anyone in approval_list except the user in first item to avoid potential requestFor can approve/submit

UPP view :
  - any non executive user in the same department
  - executive user : can see all sections in all department under him/her
  - any user who defined by position_id or user_id in approval_list (undefined case)
  


if (daValue GREATER_THAN) {
  add SCM after Manager Procurement 
}

if (commodity is GOODS) {
  add Formalities after Finance
}

if (oeValue GREATER_THAN OR commodity is GOODS and contractType is FirmCommitment) {
  add LCL after Finance
}

if (isUseInsurance) {
  add Staff Legal after Staff HSSE
  add Manager Legal after Manager HSSE
}