-- -------------------------------------------------------------
-- TablePlus 6.0.0(550)
--
-- https://tableplus.com/
--
-- Database: db_eproc
-- Generation Time: 2024-10-03 11:14:15.9720
-- -------------------------------------------------------------


-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.

-- Table Definition
CREATE TABLE "public"."user" (
    "name" text NOT NULL,
    "email" text NOT NULL,
    "id" varchar(20) NOT NULL,
    "supervisorPositionId" varchar(20) NOT NULL,
    "positionId" varchar(20),
    "sectionId" varchar(20),
    "departmentId" varchar(20),
    CONSTRAINT "FK_4d82cd0086d2380c0bb4a852c61" FOREIGN KEY ("sectionId") REFERENCES "public"."section"("id"),
    CONSTRAINT "FK_3d6915a33798152a079997cad28" FOREIGN KEY ("departmentId") REFERENCES "public"."department"("id"),
    CONSTRAINT "FK_93af21ecba4fa43c4c63d2456cd" FOREIGN KEY ("positionId") REFERENCES "public"."position"("id"),
    PRIMARY KEY ("id")
);

-- Indices
CREATE UNIQUE INDEX "PK_cace4a159ff9f2512dd42373760" ON public."user" USING btree (id);

INSERT INTO "public"."user" ("name", "email", "id", "supervisorPositionId", "positionId", "sectionId", "departmentId") VALUES
('HAMIM TOHARI', '<EMAIL>', '80121002', '80000020', '80190141', '70080000', '70080000'),
('ABDURRAHMAN NURHADI', '<EMAIL>', '80121003', '80190110', '80019099', '70265000', '70260000'),
('IRENA SULIANTO', '<EMAIL>', '80121004', '80023010', '80020006', '70211000', '70210000'),
('NATALIA BRILIANTI', '<EMAIL>', '80121041', '80019230', '80019167', '70085000', '70080000'),
('INDRA SUMANTRI', '<EMAIL>', '80121042', '80130002', '80019168', '70133000', '70130000'),
('FENNY ISKANDAR', '<EMAIL>', '80121043', '80050005', '80019076', '70052000', '70050000'),
('ANINDYA BUDI', '<EMAIL>', '80121044', '80190152', '80019070', '70031200', '70030000'),
('AAP PURNAMA', '<EMAIL>', '80121046', '80050000', '80050005', '70052000', '70050000'),
('LISTIANI DEWI', '<EMAIL>', '80121049', '80000000', '80020000', '73000000', '73000000'),
('WAHYUDIN SUNARYA', '<EMAIL>', '80131062', '80000000', '80000020', '72000000', '72000000'),
('FAISAL KAMASE', '<EMAIL>', '80131069', '80190148', '80060017', '70061210', '70060000'),
('YUSVIARI YUWONO', '<EMAIL>', '80131075', '80190144', '80019164', '70078000', '70070000'),
('YONNA WULANDARI', '<EMAIL>', '80131077', '80030005', '80030007', '70032000', '70030000'),
('HAFIZ ERNOPUTRA', '<EMAIL>', '80131081', '80020006', '80019031', '70211000', '70210000'),
('SARAS SUSETYO', '<EMAIL>', '80131082', '80050000', '80050015', '70056000', '70050000'),
('SELI HERYAWATI', '<EMAIL>', '80131083', '80020002', '80019100', '70028000', '70020000'),
('BARNE RUSLI', '<EMAIL>', '80131084', '80130000', '80130018', '70135000', '70130000'),
('AWAL KAHLIL', '<EMAIL>', '80131088', '80023010', '80020036', '70212000', '70210000'),
('HENDRA HIMAWAN', '<EMAIL>', '80131089', '80130002', '80130024', '70133000', '70130000'),
('ADHELINE DEVIYANTI', '<EMAIL>', '80131090', '80030032', '80230019', '70031000', '70280000'),
('ALI ALIYUDDIN', '<EMAIL>', '80131095', '80190141', '80019139', '70089000', '70080000'),
('RIEKA YULIANTI', '<EMAIL>', '80131101', '80020006', '80019060', '70211000', '70210000'),
('ANTONIUS HIMAWAN', '<EMAIL>', '80141096', '80130018', '80019078', '70135000', '70130000'),
('ARVIN MINTARDJA', '<EMAIL>', '80141102', '80090000', '80019111', '70090000', '70090000'),
('TIARA LUCIANA', '<EMAIL>', '80141103', '80030032', '80210024', '70031000', '70280000'),
('RIZKY FERDIANSYAH', '<EMAIL>', '80141105', '80130004', '80019077', '70134000', '70130000'),
('KRISHNA MURTI', '<EMAIL>', '80141110', '80000020', '80019156', '70250000', '70250000'),
('ADISTI RACHMA', '<EMAIL>', '80141111', '80000020', '80150015', '70150000', '70150000'),
('ARIES APRIANSYAH', '<EMAIL>', '80141112', '80000000', '80019145', '70330000', '70330000'),
('MAWAN WICAKSONO', '<EMAIL>', '80141114', '80190151', '80019003', '70061100', '70060000'),
('SARTIKA SYAHRUL', '<EMAIL>', '80141117', '80190146', '80019232', '70067000', '70060000'),
('PRODIA KEMALA', '<EMAIL>', '80141118', '80150022', '80019075', '70250000', '70250000'),
('ANDIKA PRAYOGA', '<EMAIL>', '80141120', '80190151', '80060008', '70061100', '70060000'),
('ULIAN ASMARA', '<EMAIL>', '80141121', '80000020', '80000003', '72000000', '72000000'),
('ARIF BUDIMAN', '<EMAIL>', '80141122', '80140010', '80019162', '70145000', '70140000'),
('ERICK PRIM', '<EMAIL>', '80141123', '80130002', '80019176', '70133000', '70130000'),
('WENDY DARMAN', '<EMAIL>', '80151129', '80020000', '80020002', '70020000', '70020000'),
('OKKY YUDITYA', '<EMAIL>', '80151130', '80130000', '80130002', '70133000', '70130000'),
('YOS SUDARSO', '<EMAIL>', '80151132', '80000009', '80160000', '70160000', '70160000'),
('AZHALI EDWIN', '<EMAIL>', '80151133', '80130000', '80130004', '70134000', '70130000'),
('KIAN HAN', '<EMAIL>', '80151134', '80000000', '80000007', '74000000', '74000000'),
('VICKRY ROWI', '<EMAIL>', '80151135', '80130000', '80130002', '70133000', '70130000'),
('DAMERIA HUTAPEA', '<EMAIL>', '80151136', '80190132', '80030026', '70035000', '70280000'),
('SONI WIDODO', '<EMAIL>', '80151137', '80060000', '80190151', '70061100', '70060000'),
('ROFIATI FARYANTO', '<EMAIL>', '80151141', '80030005', '80019160', '70032000', '70030000'),
('URSULA FAJARRENANINGTYAS', '<EMAIL>', '80151142', '80140000', '80140001', '70142000', '70140000'),
('INDRA ZULIVANDAMA', '<EMAIL>', '80151143', '80030000', '80030005', '70032000', '70030000'),
('NANI MARSANTI', '<EMAIL>', '80151144', '80000001', '80040001', '70040000', '70040000'),
('FRANS ADI', '<EMAIL>', '80151146', '80000021', '80190117', '70270000', '70270000'),
('PARIK SIRUMAPEA', '<EMAIL>', '80151148', '80060000', '80190148', '70061200', '70060000'),
('MEUTIA ADRIANI', '<EMAIL>', '80151150', '80000000', '80030022', '70000000', '70000000'),
('ANTON HILMAN', '<EMAIL>', '80151153', '80130002', '80130013', '70133000', '70130000'),
('RATNASARI DARWIS', '<EMAIL>', '80151154', '80020000', '80023010', '70210000', '70210000'),
('TARA INDIRA', '<EMAIL>', '80151155', '80000000', '80070000', '70070000', '70070000'),
('BOBBY HARSAWAN', '<EMAIL>', '80151156', '80140000', '80140010', '70145000', '70140000'),
('RAYMOND WIJAYA', '<EMAIL>', '80151158', '80070000', '80190144', '70078000', '70070000'),
('SURYO BIROWO', '<EMAIL>', '80151159', '80000021', '80230001', '70230000', '70230000'),
('SARAH PARDEDE', '<EMAIL>', '80151161', '80019100', '80019101', '70028000', '70020000'),
('HANNA RUBINO', '<EMAIL>', '80151163', '80000009', '80190174', '76010000', '76010000'),
('FATHULLAH MAHBUB', '<EMAIL>', '80151164', '80190132', '80030032', '70031000', '70280000'),
('ERNI SALIM', '<EMAIL>', '80151167', '80190144', '80019072', '70078000', '70070000'),
('EKA HERMANADI', '<EMAIL>', '80151169', '80000021', '80190117', '70270000', '70270000'),
('BRIGITA MAHARDIKA', '<EMAIL>', '80151171', '80040001', '80019097', '70044000', '70040000'),
('SONY HERNANTYO', '<EMAIL>', '80151173', '80060000', '80190146', '70067000', '70060000'),
('WAHIDIN RASMIN', '<EMAIL>', '80151174', '80190134', '80019045', '70290000', '70290000'),
('ARIE ANDRIYAN', '<EMAIL>', '80151179', '80120000', '80190173', '70123000', '70120000'),
('SHUBHAN ZULKARNAIN', '<EMAIL>', '80151182', '80140002', '80140006', '70141000', '70140000'),
('RHESA ARVANDITYA', '<EMAIL>', '80151183', '80020002', '80190139', '70021100', '70020000'),
('ZEISHA KALAHARI', '<EMAIL>', '80151184', '80020036', '80020007', '70212000', '70210000'),
('SANGGAM HUTAGALUNG', '<EMAIL>', '80151185', '80090000', '80019114', '70093000', '70090000'),
('ROCKYANTO SASABONE', '<EMAIL>', '80151186', '80000000', '80060000', '70060000', '70060000'),
('JONNY PASARIBU', '<EMAIL>', '80151187', '80000000', '80000009', '76000000', '76000000'),
('SULUNG ANGGORO', '<EMAIL>', '80151189', '80150015', '80019177', '70150000', '70150000'),
('WIDI ARIANTO', '<EMAIL>', '80151191', '80000021', '80190110', '70260000', '70260000'),
('YOVI MANOVA', '<EMAIL>', '80151196', '80090000', '80019059', '70092000', '70090000'),
('SATRIYO PANGARSO', '<EMAIL>', '80151198', '80230018', '80019009', '70231000', '70230000'),
('WISNU WARDHANA', '<EMAIL>', '80151199', '80060000', '80060004', '70062000', '70060000'),
('FAHROZI IBRAHIM', '<EMAIL>', '80151200', '80150007', '80019022', '70084000', '70080000'),
('SUPRIANTO KAMIJAYA', '<EMAIL>', '80151201', '80230001', '80170001', '70233000', '70230000'),
('CANDRA BUSTAMI', '<EMAIL>', '80161210', '80230001', '80170003', '70234000', '70230000'),
('FELA WARDANA', '<EMAIL>', '80161213', '80190117', '80230020', '70232000', '70270000'),
('JEFFRAY GULTOM', '<EMAIL>', '80161215', '80190146', '80019231', '70067000', '70060000'),
('YOGIE RAMDHANI', '<EMAIL>', '80161216', '80170007', '80019157', '70233100', '70230000'),
('TULUS SAJIWO', '<EMAIL>', '80161218', '80170001', '80170005', '70233100', '70230000'),
('DEVI LODEWIK', '<EMAIL>', '80161220', '80230004', '80230014', '70232100', '70270000'),
('TOHADI WARMA', '<EMAIL>', '80161221', '80170005', '80190023', '70233100', '70230000'),
('OZY MUHIDIN', '<EMAIL>', '80161222', '80190134', '80020019', '70290000', '70290000'),
('BUANA TAMPUBOLON', '<EMAIL>', '80161223', '80170010', '80190020', '70233200', '70230000'),
('EDI HARTANA', '<EMAIL>', '80161232', '80170007', '80190025', '70233100', '70230000'),
('FADLI MASWIZAR', '<EMAIL>', '80161233', '80170007', '80019158', '70233100', '70230000'),
('SOPAN BUDIONO', '<EMAIL>', '80161234', '80060000', '80110000', '70066000', '70060000'),
('RACHMAN AVRIAN', '<EMAIL>', '80161235', '80170010', '80190026', '70233200', '70230000'),
('RUDI NOERMANSAH', '<EMAIL>', '80161236', '80170010', '80190027', '70233200', '70230000'),
('OGY JAYA', '<EMAIL>', '80161239', '80190148', '80060014', '70061210', '70060000'),
('AZIS PRAWIRA', '<EMAIL>', '80161241', '80190148', '80190170', '70061210', '70060000'),
('ARDI CAHYADI', '<EMAIL>', '80161243', '80230001', '80170001', '70233000', '70230000'),
('SYAEPUL USMAN', '<EMAIL>', '80161244', '80170011', '80019159', '70233200', '70230000'),
('ELKANA RUDY', '<EMAIL>', '80161245', '80170001', '80170010', '70233200', '70230000'),
('HESA INDRAWAN', '<EMAIL>', '80161246', '80019116', '80019229', '70061210', '70060000'),
('DENDI DESTRIANTORO', '<EMAIL>', '80171247', '80190117', '80230002', '70232000', '70270000'),
('OKY SUMADI', '<EMAIL>', '80171249', '80190148', '80190171', '70061210', '70060000'),
('ILMAM SANGGOMARO', '<EMAIL>', '80171250', '80190148', '80060015', '70061210', '70060000'),
('ARIEF LEKSONO', '<EMAIL>', '80171251', '80170011', '80190010', '70233200', '70230000'),
('PERDAMAIAN SITOMPUL', '<EMAIL>', '80171252', '80170005', '80190011', '70233100', '70230000'),
('REZA KUMBARA', '<EMAIL>', '80171261', '80070000', '80051000', '70077000', '70070000'),
('SILVIEA KOESTIAWAN', '<EMAIL>', '80171262', '80019099', '80019030', '70265000', '70260000'),
('FIANTI RAMADHANI', '<EMAIL>', '80171263', '80190117', '80190130', '70272000', '70270000'),
('ABU HANIFAH', '<EMAIL>', '80171264', '80000020', '80090000', '70090000', '70090000'),
('MUKHAMAD GOZALI', '<EMAIL>', '80171267', '80170010', '80019084', '70233200', '70230000'),
('TRI HARYANTO', '<EMAIL>', '80171268', '80170007', '80019082', '70233100', '70230000'),
('MOHAMMAD TAUFIQURROHMAN', '<EMAIL>', '80171269', '80170005', '80019096', '70233100', '70230000'),
('SYAIFUL ASWI', '<EMAIL>', '80171270', '80170007', '80019083', '70233100', '70230000'),
('CHOMAIS MUNTAZHAR', '<EMAIL>', '80171274', '80190110', '80190111', '70261000', '70260000'),
('ARIS SURYANTO', '<EMAIL>', '80171276', '80000020', '80150022', '70250000', '70250000'),
('ZAKI ZULKARNAIN', '<EMAIL>', '80171277', '80020002', '80020004', '70021100', '70020000'),
('BIMO ARIYO', '<EMAIL>', '80171278', '80020002', '80020027', '70021100', '70020000'),
('YOHANES SUDRAJAT', '<EMAIL>', '80171281', '80050015', '80190182', '70056000', '70050000'),
('YANOOR YUSACKARIM', '<EMAIL>', '80171283', '80230002', '80190118', '70271000', '70270000'),
('RYAN RADITIA', '<EMAIL>', '80171284', '80190156', '80019069', '70043000', '70040000'),
('HERMAWAN JERIASAPUTRO', '<EMAIL>', '80171286', '80230005', '80230008', '70232100', '70270000'),
('ASHRI FALAHUDDIN', '<EMAIL>', '80181292', '80170001', '80170011', '70233200', '70230000'),
('AMY SUWANDI', '<EMAIL>', '80181294', '80170007', '80170018', '70233100', '70230000'),
('YOHANES INDRASATWIKA', '<EMAIL>', '80181295', '80130002', '80130009', '70133000', '70130000'),
('RANDA ROMEL', '<EMAIL>', '80181300', '80190151', '80060008', '70061100', '70060000'),
('ARIELLA ELYSIA', '<EMAIL>', '80181301', '80051000', '80019019', '70077000', '70070000'),
('SUGENG MULYONO', '<EMAIL>', '80181302', '80019114', '80019025', '70093000', '70090000'),
('OKY ELDYAGUSTA', '<EMAIL>', '80181303', '80020000', '80190134', '70290000', '70290000'),
('PERKASA SINAGABARIANG', '<EMAIL>', '80181304', '80000000', '80000021', '75000000', '75000000'),
('TRI WIBOWO', '<EMAIL>', '80181305', '80230005', '80230015', '70232100', '70270000'),
('RAHADIAN KUSUMA', '<EMAIL>', '80181306', '80230005', '80019067', '70232100', '70270000'),
('ATRIA HERWIBOWO', '<EMAIL>', '80181307', '80230001', '80230018', '70231000', '70230000'),
('CHALIDIN ABUBAKAR', '<EMAIL>', '80181308', '80110000', '80110003', '70066000', '70060000'),
('BAMBANG HARYANTO', '<EMAIL>', '80181309', '80230001', '80170002', '70233000', '70230000'),
('NOVAN NURSYAMSA', '<EMAIL>', '80181310', '80190113', '80190186', '70262000', '76010000'),
('JANUAR PRATAMA', '<EMAIL>', '80181311', '80230005', '80230016', '70232100', '70270000'),
('SUDIROYONO SARWOKO', '<EMAIL>', '80181313', '80019124', '80019130', '70261100', '70260000'),
('ADITYA NURAHMAN', '<EMAIL>', '80181315', '80190132', '80019053', '70037000', '70280000'),
('WIRA KUSUMA', '<EMAIL>', '80181316', '80000001', '80190132', '70280000', '70280000'),
('YALA PRAKASA', '<EMAIL>', '80181317', '80030000', '80190152', '70031200', '70030000'),
('RENDHY ARDIAN', '<EMAIL>', '80181318', '80000001', '80030000', '70030000', '70030000'),
('DEARDO OKTOVIAN', '<EMAIL>', '80181319', '80230020', '80190119', '70271000', '70270000'),
('MOHAMMAD RIEZQOH', '<EMAIL>', '80181320', '80040001', '80190156', '70043000', '70040000'),
('ELLY YOUNG', '<EMAIL>', '80181321', '80051000', '80019165', '70077000', '70070000'),
('PRASETYO UTOMO', '<EMAIL>', '80181322', '80190174', '80190113', '70262000', '76010000'),
('ADITYA PRATAMA', '<EMAIL>', '80181323', '80020002', '80019121', '70021000', '70020000'),
('YULIA KARNAIN', '<EMAIL>', '80181325', '80190136', '80019032', '70291000', '70210000'),
('FAJRI SALEH', '<EMAIL>', '80181326', '80023010', '80190136', '70291000', '70210000'),
('ANDHINI SUMONO', '<EMAIL>', '80181327', '80150015', '80019119', '70154000', '70150000'),
('RUSLI ZULFIKAR', '<EMAIL>', '80181330', '80110000', '80019029', '70066000', '70060000'),
('FAJAR PRASAJA', '<EMAIL>', '80191332', '80170003', '80170014', '70234000', '70230000'),
('WISNU PRASEDYOKO', '<EMAIL>', '80191334', '80019109', '80019109', '70070000', '70070000'),
('AKHMAD AGUSTIAN', '<EMAIL>', '80191335', '80190152', '80019199', '70031200', '70030000'),
('JOSHUA HASIBUAN', '<EMAIL>', '80191336', '80030005', '80019180', '70032000', '70030000'),
('DIRZI ZAIDAN', '<EMAIL>', '80191339', '80000009', '80050000', '70050000', '70050000'),
('ANDREAS KRISBAYU', '<EMAIL>', '80191342', '80190146', '80190147', '70067000', '70060000'),
('DELILAH PASARIBU', '<EMAIL>', '80191343', '80190144', '80019071', '70078000', '70070000'),
('ASLAM ABUBAKAR', '<EMAIL>', '80201344', '80160000', '80050011', '70160000', '70160000'),
('ANGELA ARTHARI', '<EMAIL>', '80201345', '80030026', '80019024', '70035000', '70280000'),
('ANGGA NURHAKIM', '<EMAIL>', '80201347', '80030032', '80190155', '70031000', '70280000'),
('FATMAWATI NASUTION', '<EMAIL>', '80201348', '80019073', '80019073', '70123000', '70120000'),
('ISMALYADI UMARAN', '<EMAIL>', '80201350', '80120000', '80019014', '70120000', '70120000'),
('MONANG TAMPUBOLON', '<EMAIL>', '80201352', '80000000', '80120000', '70120000', '70120000'),
('LIU XIAOPO', '<EMAIL>', '80209031', '80000007', '80140000', '70140000', '70140000'),
('WANG JIMEI', '<EMAIL>', '80209032', '80130000', '80130008', '70130000', '70130000'),
('ADNAN SUKSMANADHY', '<EMAIL>', '80211353', '80150022', '80019038', '70250000', '70250000'),
('TRI WHARDANA', '<EMAIL>', '80211354', '80160000', '80050022', '70160000', '70160000'),
('JONA JOHARI', '<EMAIL>', '80211355', '80190174', '80190177', '70055000', '76010000'),
('AHYA ANSORI', '<EMAIL>', '80211356', '80170011', '80190006', '70233200', '70230000'),
('NEFERTETE SURYANDARI', '<EMAIL>', '80211357', '80050001', '80190180', '70082000', '70050000'),
('AHMAD LUTHFI', '<EMAIL>', '80211360', '80190113', '80019233', '70262000', '76010000'),
('EDI PURWANTO', '<EMAIL>', '80211361', '80190111', '80019041', '70261000', '70260000'),
('SUGITO WADI', '<EMAIL>', '80211363', '80019049', '80019048', '70055000', '76010000'),
('REDHATA RANGKUTI', '<EMAIL>', '80211364', '80170002', '80170007', '70233100', '70230000'),
('YANUAR ZULET', '<EMAIL>', '80211366', '80190111', '80019051', '70261000', '70260000'),
('SAMUEL SINAGA', '<EMAIL>', '80211367', '80140001', '80140011', '70142000', '70140000'),
('BIMO PAMUNGKAS', '<EMAIL>', '80211368', '80140018', '80019052', '70146000', '70140000'),
('MARIYANI MARHARETTA', '<EMAIL>', '80211369', '80030032', '80140009', '70031000', '70280000'),
('IE PERMATA', '<EMAIL>', '80211370', '80090000', '80190157', '70090000', '70090000'),
('FATHIMAH AZZAHRA', '<EMAIL>', '80211371', '80190152', '80030003', '70031200', '70030000'),
('PUTU PARWATHA', '<EMAIL>', '80211372', '80150022', '80019054', '70250000', '70250000'),
('DESSY ASTUTI', '<EMAIL>', '80211373', '80190156', '80040006', '70043000', '70040000'),
('AZHARI SENTANU', '<EMAIL>', '80211374', '80190142', '80019166', '70155000', '70150000'),
('DUSTIN TAYLOR', '<EMAIL>', '80219034', '80019049', '80019042', '70055000', '76010000'),
('QIU LUOFEI', '<EMAIL>', '80219035', '80050000', '80050001', '70082000', '70050000'),
('KANG AN', '<EMAIL>', '80219036', '80000000', '80000000', '70000000', '70000000'),
('BAI WEIHAO', '<EMAIL>', '80219037', '80140000', '80140018', '70146000', '70140000'),
('LAKSMI ANGGRIANI', '<EMAIL>', '80221375', '80160000', '80019056', '70160000', '70160000'),
('DAMIANUS FERNANDO', '<EMAIL>', '80221376', '80230002', '80230004', '70232100', '70270000'),
('LILY SASTRIYANTI', '<EMAIL>', '80221377', '80020002', '80020002', '70020000', '70020000'),
('SHERLY SUWARDI', '<EMAIL>', '80221378', '80130018', '80130017', '70135000', '70130000'),
('ILHAM RASPATI', '<EMAIL>', '80221379', '80019114', '80019013', '70093000', '70090000'),
('GUS SHUFIANTO', '<EMAIL>', '80221380', '80170005', '80019061', '70233100', '70230000'),
('MUDJIONO WAHID', '<EMAIL>', '80221381', '80230004', '80230011', '70232100', '70270000'),
('MAHA MERU', '<EMAIL>', '80221382', '80190113', '80019062', '70262000', '76010000'),
('DWI ARYANTI', '<EMAIL>', '80221383', '80190134', '80020020', '70023000', '70290000'),
('SATRIA MUKHLIS', '<EMAIL>', '80221384', '80190130', '80019064', '70272000', '70270000'),
('BELLARIA EKAPUTRI', '<EMAIL>', '80221385', '80190146', '80060012', '70067000', '70060000'),
('TRIO SUMANTRI', '<EMAIL>', '80221386', '80060000', '80110001', '70066000', '70060000'),
('ADE ADRIANDI', '<EMAIL>', '80221387', '80230005', '80230012', '70232100', '70270000'),
('HENDRU RAHMAT', '<EMAIL>', '80221388', '80170011', '80019066', '70233200', '70230000'),
('DEDEN SANUSI', '<EMAIL>', '80221389', '80230004', '80230007', '70232100', '70270000'),
('DEDE HIDAYAT', '<EMAIL>', '80221390', '80230004', '80230013', '70232100', '70270000'),
('ANDRIA RAHMAWATI', '<EMAIL>', '80221391', '80051000', '80070005', '70077000', '70070000'),
('SARLIN RAMBALANGI', '<EMAIL>', '80221393', '80230004', '80019093', '70232100', '70270000'),
('ALAN PUTRA', '<EMAIL>', '80221394', '80230005', '80019085', '70232100', '70270000'),
('FARHANURRAHMAN RAMADHAN', '<EMAIL>', '80221396', '80230005', '80019086', '70232100', '70270000'),
('NABIL MUHYIYUDDIN', '<EMAIL>', '80221397', '80230005', '80019087', '70232100', '70270000'),
('ROBIATUL INSANI', '<EMAIL>', '80221398', '80019099', '80019090', '70265000', '70260000'),
('SAIFUL BACHRI', '<EMAIL>', '80221399', '80230005', '80019088', '70232100', '70270000'),
('SALMAN PRADHANA', '<EMAIL>', '80221400', '80170011', '80190101', '70233200', '70230000'),
('YOUSAK NYUSWANTORO', '<EMAIL>', '80221401', '80170010', '80019089', '70233200', '70230000'),
('ERFAN YUDIANTO', '<EMAIL>', '80221402', '80190130', '80190131', '70272000', '70270000'),
('DIMAS MULYADI', '<EMAIL>', '80221403', '80230020', '80230005', '70232100', '70270000'),
('EKO ADI', '<EMAIL>', '80221404', '80190118', '80190124', '70271000', '70270000'),
('PUTRI SARI', '<EMAIL>', '80221405', '80190148', '80019095', '70061200', '70060000'),
('DWI RAMADHAWIANTO', '<EMAIL>', '80221406', '80019119', '80019094', '70154000', '70150000'),
('RIZKI PUTRI', '<EMAIL>', '80221407', '80190173', '80120002', '70123000', '70120000'),
('ZHANG ZHIHUI', '<EMAIL>', '80229038', '80000001', '80019181', '71000000', '71000000'),
('LI JIE', '<EMAIL>', '80229039', '80000000', '80000001', '71000000', '71000000'),
('PUTU DHARMA', '<EMAIL>', '80231408', '80190148', '80060018', '70061210', '70060000'),
('HERI KRISTIYONO', '<EMAIL>', '80231409', '80190148', '80019098', '70061210', '70060000'),
('AMIEN YUDHISTIRA', '<EMAIL>', '80231410', '80190119', '80190125', '70271000', '70270000'),
('ANDHIKA DANISWARA', '<EMAIL>', '80231411', '80140010', '80019055', '70145000', '70140000'),
('CHRISTINA GUNAWAN', '<EMAIL>', '80231412', '80019053', '80019106', '70037000', '70280000'),
('FENNY ABFRITA', '<EMAIL>', '80231413', '80030005', '80019105', '70032000', '70030000'),
('PARIYADI RUSDI', '<EMAIL>', '80231414', '80190118', '80190122', '70271000', '70270000'),
('FARRA FANSURY', '<EMAIL>', '80231415', '80019097', '80019104', '70044000', '70040000'),
('ZAHRA PUTRI', '<EMAIL>', '80231416', '80190134', '80019103', '70023000', '70290000'),
('SALEH UMAR', '<EMAIL>', '80231417', '80190139', '80190140', '70021100', '70020000'),
('ALIF BASMALAH', '<EMAIL>', '80231418', '80170011', '80190107', '70233200', '70230000'),
('ELEN RUBIANZAH', '<EMAIL>', '80231419', '80190119', '80190123', '70271000', '70270000'),
('MUHAMMAD SHOLEH', '<EMAIL>', '80231420', '80190118', '80190128', '70271000', '70270000'),
('AKBAR DARWIS', '<EMAIL>', '80231421', '80190118', '80190129', '70271000', '70270000'),
('DEVIN EDRIC', '<EMAIL>', '80231422', '80030005', '80030038', '70032000', '70030000'),
('REZA FERDIAN', '<EMAIL>', '80231423', '80190139', '80019122', '70021100', '70020000'),
('BETA ANGGINI', '<EMAIL>', '80231424', '80190141', '80150007', '70084000', '70080000'),
('YAHYA PUTRA', '<EMAIL>', '80231425', '80170003', '80170008', '70234000', '70230000'),
('NATA ROMANTIKA', '<EMAIL>', '80231426', '80190111', '80019126', '70261000', '70260000'),
('RIZALDY ALDILAH', '<EMAIL>', '80231427', '80170010', '80190009', '70233200', '70230000'),
('RAFI FAKHRI', '<EMAIL>', '80231429', '80130002', '80130022', '70133000', '70130000'),
('BUNGA ANANTARINI', '<EMAIL>', '80231430', '80190152', '80019153', '70031200', '70030000'),
('PUTRI SYAHNIVA', '<EMAIL>', '80231431', '80019053', '80019110', '70037000', '70280000'),
('WEIN ANGGIAT', '<EMAIL>', '80231433', '80140018', '80140005', '70146000', '70140000'),
('DANI SUMARDANI', '<EMAIL>', '80231434', '80190174', '80019131', '70056000', '76010000'),
('ENCIN CHAMIM', '<EMAIL>', '80231435', '80190174', '80019155', '70056000', '76010000'),
('ELA HERAWATI', '<EMAIL>', '80231436', '80060004', '80019113', '70062000', '70060000'),
('DITTA MURTI', '<EMAIL>', '80231437', '80190151', '80019117', '70061100', '70060000'),
('YUWONO WINARNO', '<EMAIL>', '80231438', '80140010', '80019112', '70145000', '70140000'),
('YOGIE PRATAMA', '<EMAIL>', '80231439', '80030026', '80019152', '70035000', '70280000'),
('SHINDY UNTARI', '<EMAIL>', '80231440', '80019230', '80019169', '70085000', '70080000'),
('QIDRAN AMALI', '<EMAIL>', '80231441', '80020036', '80190165', '70212000', '70210000'),
('VIANTO PUJINAUFAL', '<EMAIL>', '80231442', '80190136', '80190160', '70291000', '70210000'),
('MUHAMMAD HIDAYAT', '<EMAIL>', '80231443', '80190113', '80019171', '70262000', '76010000'),
('VISKA GINANJAR', '<EMAIL>', '80231444', '80019170', '80019170', '70133000', '70130000'),
('MONIKA MAHARANI', '<EMAIL>', '80231445', '80019172', '80019172', '70230000', '70230000'),
('OKTIAWAN TRIWAHYUDI', '<EMAIL>', '80231447', '80019121', '80019174', '70021000', '70020000'),
('ANGGA NUGRAHA', '<EMAIL>', '80231449', '80019139', '80019136', '70089000', '70080000'),
('ANDARU PUTRA', '<EMAIL>', '80231450', '80030032', '80019175', '70031000', '70280000'),
('SAIFUL HIDAYAT', '<EMAIL>', '80231451', '80070019', '80070015', '70076000', '70070000'),
('DIAN OKTAWIJAYANTI', '<EMAIL>', '80231452', '80140010', '80019178', '70145000', '70140000'),
('LI YANHUI', '<EMAIL>', '80239042', '80000009', '80019128', '76000000', '76000000'),
('LU DAN', '<EMAIL>', '80239043', '80050005', '80019179', '70052000', '70050000'),
('GHIFFARI HERDINURSYAH', '<EMAIL>', '80241453', '80019097', '80019039', '70044000', '70040000'),
('FAIRUZ ATHALLAH', '<EMAIL>', '80241454', '80140001', '80019185', '70142000', '70140000'),
('EKA HERMANADI', '<EMAIL>', '80241455', '80190117', '80019195', '70270000', '70270000'),
('INA MELIA', '<EMAIL>', '80241456', '80190139', '80019186', '70021100', '70020000'),
('DANI SUMARDANI', '<EMAIL>', '80241457', '80019124', '80019131', '70261100', '70260000'),
('ENCIN CHAMIM', '<EMAIL>', '80241458', '80019124', '80019155', '70261100', '70260000'),
('RAFI FAKHRI', '<EMAIL>', '80241459', '80130002', '80130022', '70133000', '70130000'),
('WISNU SEJATI', '<EMAIL>', '80241460', '80070019', '80190168', '70076000', '70070000'),
('TAUFIK UDASMARA', '<EMAIL>', '80241461', '80150007', '80019036', '70084000', '70080000'),
('DIAZ HARTORO', '<EMAIL>', '80241462', '80030005', '80019200', '70032000', '70030000'),
('ANDRI NOVIYANTO', '<EMAIL>', '80241463', '80019059', '80019216', '70092000', '70090000'),
('DONNA ARYATI', '<EMAIL>', '80241464', '80019099', '80019125', '70265000', '70260000'),
('ANAIS SAPHIRA', '<EMAIL>', '80241465', '80000009', '80000002', '76000000', '76000000'),
('MUHAMMAD ELFAN', '<EMAIL>', '80241466', '80190142', '80019044', '70155000', '70150000'),
('MAHTUBAH SAGINO', '<EMAIL>', '80241467', '80190152', '80030040', '70031200', '70030000'),
('MONIKA MAHARANI', '<EMAIL>', '80241468', '80230001', '80019172', '70230000', '70230000'),
('VISKA GINANJAR', '<EMAIL>', '80241469', '80130002', '80019170', '70133000', '70130000'),
('THOMAS GOLDY', '<EMAIL>', '80241470', '80019049', '80019173', '70055000', '76010000'),
('PRADITA PUTRI', '<EMAIL>', '80241471', '80150015', '80019119', '70154000', '70150000'),
('ARIE AVIANA', '<EMAIL>', '80241472', '80019053', '80019203', '70037000', '70280000'),
('MOHAMMAD MAKHRUS', '<EMAIL>', '80241473', '80019049', '80019140', '70055000', '76010000'),
('RANA DINAH NURTAHANY', '<EMAIL>', '80241474', '80019100', '80019102', '70028000', '70020000'),
('MA QIANG', '<EMAIL>', '80249044', '80000021', '80019182', '75000000', '75000000'),
('SUTINO SUTINO', '<EMAIL>', '89153001', '80120000', '80121001', '70121000', '70120000'),
('ANDRI IRAWAN', '<EMAIL>', '89153003', '80030000', '80033009', '70033000', '70030000'),
('AGUNG BEKTI', '<EMAIL>', '89153004', '80070000', '80071010', '70071000', '70070000'),
('ANAIS SAPHIRA', '<EMAIL>', '89153005', '80228018', '80228018', '70053000', '70050000'),
('YODI KURNIAWAN', '<EMAIL>', '89153006', '80090000', '80091005', '70091000', '70090000'),
('RESKA FADHILLAH', '<EMAIL>', '89153007', '80070000', '80071011', '70071000', '70070000'),
('ASEP HIDAYAT', '<EMAIL>', '89153011', '80210026', '80228008', '70058000', '76010000'),
('MUHAMMAD TAUFIK', '<EMAIL>', '89153016', '80023010', '80024007', '70024000', '70210000'),
('TEGUH RIYANTO', '<EMAIL>', '89153017', '80060000', '80141004', '70064000', '70060000'),
('RICAT PALINDO', '<EMAIL>', '89153027', '80090000', '80091006', '70091000', '70090000'),
('DEDY YUSUF', '', '89153048', '80071033', '80071033', '70071000', '70070000'),
('TEDI MULYADI', '', '89153051', '80070000', '80071019', '70071000', '70070000'),
('ASELIH ASELIH', '', '89153052', '80071017', '80071017', '70071000', '70070000'),
('FAHMIRULLAH DWIPUTRA', '<EMAIL>', '89153056', '80070000', '80071004', '70071000', '70070000'),
('AGUS SYAFAAT', '<EMAIL>', '89153061', '80060000', '80111005', '70111000', '70060000'),
('BUNGA PRASTIKA', '<EMAIL>', '89153062', '80060000', '80111019', '70111000', '70060000'),
('ENDI SUWARNO', '<EMAIL>', '89153063', '80060000', '80064027', '70064000', '70060000'),
('RONI PRASETYO', '<EMAIL>', '89153065', '80060000', '80111050', '70111000', '70060000'),
('SLAMET TRIONO', '<EMAIL>', '89153066', '80060000', '80111054', '70111000', '70060000'),
('MOCHAMAD HUDAN HAKIKI', '<EMAIL>', '89153069', '80060000', '80111048', '70111000', '70060000'),
('BAMBANG SUSENO', '<EMAIL>', '89153072', '80060000', '80111045', '70111000', '70060000'),
('DEDI CANDRA PURNA', '<EMAIL>', '89153073', '80060000', '80111034', '70111000', '70060000'),
('DARMAWAN SAPUTRA', '<EMAIL>', '89153076', '80060000', '80111077', '70111000', '70060000'),
('DIRHAMZAH DIRHAMZAH', '<EMAIL>', '89153077', '80060000', '80111024', '70111000', '70060000'),
('BUDI UTOMO', '<EMAIL>', '89153079', '80060000', '80111060', '70111000', '70060000'),
('SUPARLI CITRA', '<EMAIL>', '89153082', '80060000', '80111002', '70111000', '70060000'),
('SLAMET RIYADI', '<EMAIL>', '89153083', '80060000', '80111053', '70111000', '70060000'),
('ALI MUSTOFA', '<EMAIL>', '89153085', '80060000', '80111016', '70111000', '70060000'),
('RONAL RONAL', '<EMAIL>', '89153086', '80060000', '80111070', '70111000', '70060000'),
('ALI MUSTOFA', '', '89153087', '80060000', '80111017', '70111000', '70060000'),
('MUSLIMIN MUSLIMIN', '<EMAIL>', '89153088', '80060000', '80111056', '70111000', '70060000'),
('YUZADI BASIR', '<EMAIL>', '89153090', '80190174', '80053003', '70058000', '76010000'),
('ANDHIKAPATI PARIPURNA', '<EMAIL>', '89153103', '80190174', '80053020', '70058000', '76010000'),
('PRIHANDOKO PRIHANDOKO', '<EMAIL>', '89153112', '80060000', '80111046', '70111000', '70060000'),
('REZA ARDILLES MONINGKA', '<EMAIL>', '89153229', '80020002', '80024005', '70026000', '70020000'),
('RAJAB GUFRILZAR', '<EMAIL>', '89153442', '80020002', '80024066', '70026000', '70020000'),
('FAUZAN SAMBUDI', '<EMAIL>', '89153552', '80060000', '80111028', '70111000', '70060000'),
('MOHLAS MOHLAS', '<EMAIL>', '89163575', '80060000', '80111013', '70111000', '70060000'),
('SYAIFUL ROZUKI', '<EMAIL>', '89163594', '80060000', '80111058', '70111000', '70060000'),
('PERLI PRAMBANA', '<EMAIL>', '89163595', '80060000', '80111021', '70111000', '70060000'),
('SYAHRIL RATU PRAWIRA NEGARA', '<EMAIL>', '89163693', '80060000', '80111072', '70111000', '70060000'),
('ZUHAL ZUHAL', '<EMAIL>', '89163819', '80060000', '80053011', '70064000', '70060000'),
('AFRINITA HEIDYANTI', '<EMAIL>', '89163820', '80190174', '80053018', '70058000', '76010000'),
('ANASTASIA YANTI RUSMANINGRUM', '<EMAIL>', '89163943', '80190174', '80161006', '70058000', '76010000'),
('MOH. FAIZOL', '<EMAIL>', '89163962', '80060000', '80111012', '70111000', '70060000'),
('MOH. TOSIN', '<EMAIL>', '89163963', '80060000', '80111014', '70111000', '70060000'),
('MARSUKI MARSUKI', '<EMAIL>', '89163964', '80060000', '80111030', '70111000', '70060000'),
('FAUSAN FAUSAN', '<EMAIL>', '89163965', '80060000', '80111033', '70111000', '70060000'),
('DWI BAGUS SUPRIANTO', '', '89163966', '80111017', '80111017', '70111000', '70060000'),
('MUHLIS MUHLIS', '<EMAIL>', '89163968', '80060000', '80111071', '70111000', '70060000'),
('JAENAL ABIDIN', '<EMAIL>', '89164006', '80060000', '80111007', '70111000', '70060000'),
('GALIH TRISULO', '<EMAIL>', '89174050', '80020002', '80024001', '70026000', '70020000'),
('AGUNG PRIYO SEMBODO', '<EMAIL>', '89174056', '80230001', '80171103', '70171000', '70230000'),
('HARMOKO HARMOKO', '', '89174058', '80171001', '80171001', '70171000', '70230000'),
('EDY SUPONCO', '', '89174059', '80171002', '80171002', '70171000', '70230000'),
('SAID BOY AMRULLAH', '', '89174060', '80171003', '80171003', '70171000', '70230000'),
('GILANG KENCONO AJI', '', '89174061', '80171004', '80171004', '70171000', '70230000'),
('ACHMAD ARBANI HILMAN', '', '89174062', '80171005', '80171005', '70171000', '70230000'),
('MUHAMMAD UMAR NABAWI', '', '89174063', '80171006', '80171006', '70171000', '70230000'),
('MUNJANI MUNJANI', '', '89174064', '80171007', '80171007', '70171000', '70230000'),
('ARMAN JAYA', '', '89174065', '80171008', '80171008', '70171000', '70230000'),
('AGUS EFENDI', '', '89174066', '80171009', '80171009', '70171000', '70230000'),
('MARJATIM MARJATIM', '', '89174067', '80171010', '80171010', '70171000', '70230000'),
('DWI ENGGAR WIDI SAPTANA', '', '89174069', '80171012', '80171012', '70171000', '70230000'),
('ERICKSON ERICKSON', '', '89174070', '80171013', '80171013', '70171000', '70230000'),
('JIMMI AGUSTIN', '', '89174071', '80171014', '80171014', '70171000', '70230000'),
('AFENDA MUSRA ARFAN', '', '89174072', '80171015', '80171015', '70171000', '70230000'),
('IQBAL PERDANA SAPUTRA', '', '89174073', '80171016', '80171016', '70171000', '70230000'),
('JOKO PURWANTO', '', '89174074', '80171017', '80171017', '70171000', '70230000'),
('FANDI CANDRA RISKIAWAN', '', '89174075', '80171018', '80171018', '70171000', '70230000'),
('MARKUS KABE', '', '89174076', '80171019', '80171019', '70171000', '70230000'),
('DEDDY SETYO PRIBADI', '', '89174077', '80171020', '80171020', '70171000', '70230000'),
('DIKI HIDAYAT', '', '89174078', '80171021', '80171021', '70171000', '70230000'),
('DODI ABDULLOH', '', '89174079', '80171022', '80171022', '70171000', '70230000'),
('HAMDAN ABDUL BASIT', '', '89174080', '80171023', '80171023', '70171000', '70230000'),
('AHMAD IGUH TRILEKSONO', '', '89174081', '80171024', '80171024', '70171000', '70230000'),
('ARYO MURBANTORO', '', '89174084', '80171027', '80171027', '70171000', '70230000'),
('DJUNAIDI AMIEN ABDILLAH', '', '89174085', '80171028', '80171028', '70171000', '70230000'),
('DANY SETIAWAN', '', '89174087', '80171030', '80171030', '70171000', '70230000'),
('ISWAN HUDI', '', '89174088', '80171031', '80171031', '70171000', '70230000'),
('DEMAS PRIMA TASTAFTIYAN', '', '89174089', '80171032', '80171032', '70171000', '70230000'),
('RADEN ARYA SANJAYA', '', '89174090', '80171033', '80171033', '70171000', '70230000'),
('ARIEF SOEGANDA', '', '89174091', '80171034', '80171034', '70171000', '70230000'),
('NANANG ZULKIFLY', '', '89174092', '80171035', '80171035', '70171000', '70230000'),
('ADI MUNTHE', '', '89174097', '80171040', '80171040', '70171000', '70230000'),
('ASUH RIFQI PRIHANDANA', '', '89174098', '80171041', '80171041', '70171000', '70230000'),
('APRI DWI SUSANTO', '', '89174099', '80171042', '80171042', '70171000', '70230000'),
('YUSUF RADIMAN TANUA', '', '89174100', '80171043', '80171043', '70171000', '70230000'),
('RENDITYO JOHANI SAPUTRA', '', '89174101', '80171044', '80171044', '70171000', '70230000'),
('WAHYU ARIMURTI WARDHANA', '', '89174102', '80171045', '80171045', '70171000', '70230000'),
('KETHUT PRANOWO', '', '89174103', '80171046', '80171046', '70171000', '70230000'),
('JERRYANTO PURBA', '', '89174104', '80171047', '80171047', '70171000', '70230000'),
('AGUSTINUS TERONG KROVAN', '', '89174105', '80171048', '80171048', '70171000', '70230000'),
('JOHAN AMRI', '', '89174106', '80171049', '80171049', '70171000', '70230000'),
('RICKY HAVIANTO', '', '89174107', '80171050', '80171050', '70171000', '70230000'),
('LAURENTIUS ELOK WIDYANTORO', '', '89174108', '80171051', '80171051', '70171000', '70230000'),
('TOMMY TOGAR MANURUNG', '', '89174109', '80171052', '80171052', '70171000', '70230000'),
('ROCHMAN NOVARIO FACHRURI SULIS', '', '89174110', '80171053', '80171053', '70171000', '70230000'),
('SETIA NURUL HIDAYANTO', '', '89174111', '80171054', '80171054', '70171000', '70230000'),
('ARIP HIDAYAT', '', '89174112', '80171055', '80171055', '70171000', '70230000'),
('JAUHARI ADAM', '', '89174113', '80171056', '80171056', '70171000', '70230000'),
('SLAMET HARIYONO', '', '89174114', '80171057', '80171057', '70171000', '70230000'),
('WELLY ARIO TARIGAN', '', '89174115', '80171058', '80171058', '70171000', '70230000'),
('MERIZALDI MERIZALDI', '', '89174116', '80171059', '80171059', '70171000', '70230000'),
('UDHI ARIFIANTO', '', '89174117', '80171060', '80171060', '70171000', '70230000'),
('FERI TRIYANTO', '', '89174118', '80171061', '80171061', '70171000', '70230000'),
('HERI SUTANTO', '', '89174119', '80171062', '80171062', '70171000', '70230000'),
('DENI HERTANTO', '', '89174120', '80171063', '80171063', '70171000', '70230000'),
('MOCHAMAD AGUS SETIAWAN WAHID', '', '89174121', '80171064', '80171064', '70171000', '70230000'),
('ADI HENDRAWAN', '', '89174122', '80171065', '80171065', '70171000', '70230000'),
('HANIEF HUSNA AMALI', '', '89174123', '80171066', '80171066', '70171000', '70230000'),
('AJI WICAKSONO', '', '89174124', '80171067', '80171067', '70171000', '70230000'),
('ASHADI HIDAYAT', '', '89174125', '80171068', '80171068', '70171000', '70230000'),
('WAHIB WAHIB', '', '89174126', '80171069', '80171069', '70171000', '70230000'),
('VILISIA YOSI IRAWAN', '', '89174127', '80171070', '80171070', '70171000', '70230000'),
('SUWISTOYO SUWISTOYO', '', '89174129', '80171094', '80171094', '70171000', '70230000'),
('MOHAMAD RASYID RIDHA', '', '89174130', '80171093', '80171093', '70171000', '70230000'),
('ARIES SIGIT PRASETYO', '', '89174131', '80171092', '80171092', '70171000', '70230000'),
('GIYANTO GIYANTO', '', '89174132', '80171091', '80171091', '70171000', '70230000'),
('BUHAULI FREDDY LEONARDO', '', '89174133', '80171090', '80171090', '70171000', '70230000'),
('SUHARTO SISWO', '', '89174134', '80171089', '80171089', '70171000', '70230000'),
('ROBERTUS DAVID MITEDEDE', '', '89174135', '80171088', '80171088', '70171000', '70230000'),
('PONDA PONDA', '', '89174136', '80171087', '80171087', '70171000', '70230000'),
('IRCHAM IRCHAM', '', '89174137', '80171086', '80171086', '70171000', '70230000'),
('YUDHA EKO PRIYANTO', '', '89174138', '80171085', '80171085', '70171000', '70230000'),
('WAWAN SETIYOBUDI', '', '89174139', '80171084', '80171084', '70171000', '70230000'),
('IIP ASDORI', '', '89174140', '80171083', '80171083', '70171000', '70230000'),
('BONAR AGUSTE SRI', '', '89174141', '80171082', '80171082', '70171000', '70230000'),
('WISMA TEJA KUSUMA', '', '89174142', '80171081', '80171081', '70171000', '70230000'),
('ADHIA PRENATA PUTRA HUZA', '', '89174143', '80171080', '80171080', '70171000', '70230000'),
('SYAMSUDIN NUR', '', '89174144', '80171079', '80171079', '70171000', '70230000'),
('REYNDI REYNDI', '', '89174145', '80171078', '80171078', '70171000', '70230000'),
('IBNU ARIFIN LUKMAN', '', '89174146', '80171077', '80171077', '70171000', '70230000'),
('HERI TRI SUSANTO', '', '89174147', '80171076', '80171076', '70171000', '70230000'),
('FAJRI JULISYAH TANNAWI', '', '89174148', '80171075', '80171075', '70171000', '70230000'),
('SAMUJI ARIF FIYANTO', '', '89174149', '80171074', '80171074', '70171000', '70230000'),
('SUGENG SUGENG', '', '89174150', '80171073', '80171073', '70171000', '70230000'),
('ADI AKMAL', '', '89174151', '80171072', '80171072', '70171000', '70230000'),
('MIDARYATNO MIDARYATNO', '', '89174152', '80171071', '80171071', '70171000', '70230000'),
('JUSTINUS ARIJO TEDY SUHARTOKO', '', '89174155', '80171095', '80171095', '70171000', '70230000'),
('FATAHUL ROHIM', '<EMAIL>', '89174158', '80060000', '80111065', '70111000', '70060000'),
('M. NADIRIN', '<EMAIL>', '89174159', '80060000', '80111057', '70111000', '70060000'),
('MUHAMMAD ZIAK ULHAQ', '<EMAIL>', '89174161', '80060000', '80111074', '70111000', '70060000'),
('FARUK FARUK', '<EMAIL>', '89174164', '80230001', '80171116', '70171000', '70230000'),
('RUJITO RUJITO', '<EMAIL>', '89174165', '80230001', '80171115', '70171000', '70230000'),
('PUNGKAS PANDU WIJAYA', '<EMAIL>', '89174166', '80060000', '80111047', '70111000', '70060000'),
('YANCE DAPOT PANANGIAN GULTOM', '<EMAIL>', '89174167', '80230001', '80171096', '70171000', '70230000'),
('UMEIR AL BARRA ADIWIGANDA', '<EMAIL>', '89174179', '80230001', '80171140', '70171000', '70230000'),
('NENDI FACHRULI', '<EMAIL>', '89174180', '80230001', '80171100', '70171000', '70230000'),
('BRAM K.A. AZIZ SURBAKTI', '<EMAIL>', '89174188', '80230001', '80171125', '70171000', '70230000'),
('ERIK KURNIAWAN', '', '89174191', '80171098', '80171098', '70171000', '70230000'),
('SUWONDO SUWONDO', '<EMAIL>', '89174197', '80060000', '80111069', '70111000', '70060000'),
('AGUS TONO', '<EMAIL>', '89174198', '80060000', '80111022', '70111000', '70060000'),
('ALI LUTFI', '', '89174199', '80111020', '80111020', '70111000', '70060000'),
('DEDY FRAKSI YANTO', '<EMAIL>', '89174200', '80060000', '80111023', '70111000', '70060000'),
('YANA SUPRIYATNA', '<EMAIL>', '89174218', '80230001', '80171110', '70171000', '70230000'),
('IDA NUR SURYANINGSIH', '<EMAIL>', '89174220', '80230001', '80171179', '70171000', '70230000'),
('ANTON PUTRANTO', '<EMAIL>', '89174225', '80230001', '80171105', '70171000', '70230000'),
('MOHAMAD YASIN', '<EMAIL>', '89174226', '80230001', '80171106', '70171000', '70230000'),
('HERI JUNAEDI', '<EMAIL>', '89174227', '80230001', '80171107', '70171000', '70230000'),
('NURSEAN NGUSMAN', '<EMAIL>', '89174229', '80190174', '80228038', '70058000', '76010000'),
('FAROUK YOEDYIANTO', '<EMAIL>', '89174247', '80230001', '80171111', '70171000', '70230000'),
('SIGIT KURNIAWAN HERYANTO', '<EMAIL>', '89174248', '80230001', '80171112', '70171000', '70230000'),
('MOHAMMAD SOBHAN', '<EMAIL>', '89174249', '80230001', '80171113', '70171000', '70230000'),
('SARBINI SARBINI', '<EMAIL>', '89174250', '80230001', '80171114', '70171000', '70230000'),
('OJAK HALOMOAN SINAGA', '<EMAIL>', '89174251', '80060000', '80111059', '70111000', '70060000'),
('HERUL SALAM', '<EMAIL>', '89174285', '80020002', '80024006', '70026000', '70020000'),
('SUGIANTO SUGIANTO', '<EMAIL>', '89174295', '80230001', '80171117', '70171000', '70230000'),
('AGUNG PRASETIA', '<EMAIL>', '89174296', '80230001', '80171118', '70171000', '70230000'),
('FITRIYAH FITRIYAH', '<EMAIL>', '89174297', '80020002', '80024047', '70026000', '70020000'),
('ENDANG RUSMANA', '<EMAIL>', '89174300', '80230001', '80171119', '70171000', '70230000'),
('MOH MISBAKH', '<EMAIL>', '89174312', '80230001', '80171131', '70171000', '70230000'),
('SUHUD WAHYUDI', '<EMAIL>', '89174313', '80230001', '80171132', '70171000', '70230000'),
('SUWANTO SUWANTO', '<EMAIL>', '89174314', '80230001', '80171133', '70171000', '70230000'),
('MAHMUDI MAHMUDI', '<EMAIL>', '89174315', '80230001', '80171134', '70171000', '70230000'),
('NOVIADI NOVIADI', '<EMAIL>', '89174316', '80230001', '80171135', '70171000', '70230000'),
('SALMAN AL FARISI', '<EMAIL>', '89174328', '80020002', '80024014', '70026000', '70020000'),
('SOBIHUL ASRORI', '<EMAIL>', '89174371', '80070000', '80071008', '70071000', '70070000'),
('MUCHAMAD SUYANTO', '<EMAIL>', '89174372', '80070000', '80071006', '70071000', '70070000'),
('M. ARIS', '<EMAIL>', '89174373', '80070000', '80071005', '70071000', '70070000'),
('UMAR FARUQ', '<EMAIL>', '89174374', '80070000', '80071009', '70071000', '70070000'),
('YUDI SETIADI', '<EMAIL>', '89174375', '80230001', '80171146', '70171000', '70230000'),
('BOIMIN BOIMIN', '<EMAIL>', '89174381', '80230001', '80171147', '70171000', '70230000'),
('DARMANSYAH DARMANSYAH', '', '89174384', '80111004', '80111004', '70111000', '70060000'),
('VIA WIDYANURI', '<EMAIL>', '89174399', '80070000', '80071012', '70071000', '70070000'),
('VERA PARAMITHA', '<EMAIL>', '89174415', '80190117', '80271001', '70279000', '70270000'),
('ABDUS SOMAD', '<EMAIL>', '89174434', '80230001', '80171149', '70171000', '70230000'),
('AGUNG HERYADI', '<EMAIL>', '89174450', '80060000', '80111063', '70111000', '70060000'),
('ROCHIM AKBAR', '<EMAIL>', '89174451', '80230001', '80171038', '70171000', '70230000'),
('KOSIM ABDULLA', '<EMAIL>', '89174463', '80070000', '80071059', '70071000', '70070000'),
('FIRMAN FIRMAN', '<EMAIL>', '89184480', '80230001', '80171142', '70171000', '70230000'),
('IRVAN SYAIFULLAH', '<EMAIL>', '89184497', '80230001', '80171025', '70171000', '70230000'),
('SIGIT SETIAWAN', '<EMAIL>', '89184515', '80020002', '80024081', '70024000', '70210000'),
('MOHAMAD SYAFRAI', '<EMAIL>', '89184565', '80020002', '80024078', '70024000', '70210000'),
('IRVAN TRIANDRIANTO', '<EMAIL>', '89184657', '80020002', '80024079', '70024000', '70210000'),
('FAISIL FAISIL', '<EMAIL>', '89184658', '80020002', '80024002', '70024000', '70210000'),
('JANSEN BASTIANUS WODA', '<EMAIL>', '89184660', '80020002', '80024054', '70024000', '70210000'),
('IMAM SOFYAN', '<EMAIL>', '89184661', '80020002', '80024018', '70024000', '70210000'),
('MULYANTO MULYANTO', '<EMAIL>', '89184662', '80020002', '80024017', '70024000', '70210000'),
('MUSLIMIN MUSLIMIN', '<EMAIL>', '89184663', '80020002', '80024016', '70024000', '70210000'),
('MOH. JAILANI', '<EMAIL>', '89184664', '80020002', '80024035', '70024000', '70210000'),
('MOH. SOIM', '<EMAIL>', '89184665', '80020002', '80024037', '70024000', '70210000'),
('MANSUR MANSUR', '<EMAIL>', '89184667', '80020002', '80024083', '70024000', '70210000'),
('NURHADI ZAKARIA', '<EMAIL>', '89184668', '80020002', '80024084', '70024000', '70210000'),
('MUJI HARIYANTO', '<EMAIL>', '89184669', '80020002', '80024085', '70024000', '70210000'),
('SUKENDRO SUKENDRO', '<EMAIL>', '89184670', '80020002', '80024086', '70024000', '70210000'),
('MUDARI MUDARI', '<EMAIL>', '89184671', '80020002', '80024087', '70024000', '70210000'),
('ABDUL FATTA YASIN', '<EMAIL>', '89184672', '80060000', '80111018', '70111000', '70060000'),
('ACHMAD RIFAI', '<EMAIL>', '89184673', '80020002', '80024089', '70024000', '70210000'),
('MUHAMMAD TOHARI', '<EMAIL>', '89184676', '80020002', '80024092', '70024000', '70210000'),
('SOGOL SUHARDIANTO', '<EMAIL>', '89184687', '80230001', '80171160', '70171000', '70230000'),
('YUDI MASYUDI', '<EMAIL>', '89184701', '80090000', '80091032', '70091000', '70090000'),
('BURHANI BURHANI', '<EMAIL>', '89184704', '80190174', '80161009', '70058000', '76010000'),
('AHMAD SUMAJI', '<EMAIL>', '89184705', '80190174', '80053002', '70058000', '76010000'),
('MUHAMMAD ELFAN', '', '89184850', '80151001', '80151001', '70151000', '70150000');
INSERT INTO "public"."user" ("name", "email", "id", "supervisorPositionId", "positionId", "sectionId", "departmentId") VALUES
('AKBAR SAIFOUR RIDZALL', '<EMAIL>', '89184889', '80060000', '80111089', '70111000', '70060000'),
('RIZKI DERMAWAN', '<EMAIL>', '89184890', '80060000', '80111049', '70111000', '70060000'),
('RANTO RANTO', '<EMAIL>', '89184927', '80070000', '80071003', '70071000', '70070000'),
('ISWAHYUDI ISWAHYUDI', '<EMAIL>', '89184928', '80070000', '80071002', '70071000', '70070000'),
('FIRZA RIZQULLOH', '', '89184953', '80090000', '80091010', '70091000', '70090000'),
('RICKY KURNIAWAN', '', '89184956', '80090000', '80091020', '70091000', '70090000'),
('ERLANGGA RIZKY WYDIANTO', '', '89184959', '80090000', '80091004', '70091000', '70090000'),
('GILANG PALITO', '<EMAIL>', '89184966', '80090000', '80091012', '70091000', '70090000'),
('ANANDA LARASATI', '<EMAIL>', '89184971', '80190134', '80024004', '70292000', '70290000'),
('ADINDA JAZZICA', '<EMAIL>', '89184972', '80150015', '80151004', '70151000', '70150000'),
('GURITNO YUDHO WIBOWO', '', '89184994', '80090000', '80091026', '70091000', '70090000'),
('NAFARIN NAFARIN', '<EMAIL>', '89185012', '80230001', '80171139', '70171000', '70230000'),
('HENDRI KO', '<EMAIL>', '89185013', '80230001', '80171148', '70171000', '70230000'),
('AGUS SUTRISNO', '', '89185194', '80060000', '80064023', '70064000', '70060000'),
('WIRAWAN PRABOWO', '', '89185195', '80060000', '80064024', '70064000', '70060000'),
('PANDU PURWOKO', '', '89185196', '80060000', '80064022', '70064000', '70060000'),
('RIDHAHAYATI RIDHAHAYATI', '<EMAIL>', '89195335', '80090000', '80091034', '70091000', '70090000'),
('ENDAH FATHONAH', '', '89195336', '80090000', '80091035', '70091000', '70090000'),
('NURUL HARYATIA', '', '89195337', '80090000', '80091033', '70091000', '70090000'),
('BENNY SYAILENDRA SILALAHI', '<EMAIL>', '89195340', '80060000', '80111035', '70111000', '70060000'),
('BERNANDA SETIAWAN', '<EMAIL>', '89195342', '80190174', '80161003', '70058000', '76010000'),
('FAISAL HAFIZD', '<EMAIL>', '89195363', '80090000', '80091037', '70091000', '70090000'),
('BAGAS MAULANA PRADIPA', '', '89195393', '80090000', '80091038', '70091000', '70090000'),
('ULIL ALBAB', '', '89195394', '80090000', '80091039', '70091000', '70090000'),
('SABARUDDIN ASPIL', '<EMAIL>', '89195494', '80190174', '80053001', '70058000', '76010000'),
('ANDRI NOVIYANTO', '', '89195553', '80090000', '80091001', '70091000', '70090000'),
('DANIEL ADRIANSYAH', '', '89195556', '80060000', '80111063', '70111000', '70060000'),
('LOSO LOSO', '<EMAIL>', '89195557', '80060000', '80111042', '70111000', '70060000'),
('DHANU ADRIANSYAH', '<EMAIL>', '89195575', '80090000', '80091019', '70091000', '70090000'),
('HERMAN HERMAN', '', '89195576', '80090000', '80091011', '70091000', '70090000'),
('ANISA INDARTO', '<EMAIL>', '89195591', '80060000', '80064025', '70064000', '70060000'),
('SAEROJI SAEROJI', '<EMAIL>', '89195749', '80111032', '80111032', '70111000', '70060000'),
('REZA ALFA YAMANY', '', '89205826', '80020002', '80024020', '70026000', '70020000'),
('TEGUH BUDI SANTOSO', '<EMAIL>', '89205885', '80020002', '80024107', '70026000', '70020000'),
('ALI DAMANHURI', '<EMAIL>', '89206020', '80060000', '80111062', '70111000', '70060000'),
('ENDANG WACHYUDI', '', '89206021', '80060000', '80111006', '70111000', '70060000'),
('HERY SUSILO', '<EMAIL>', '89206022', '80060000', '80111001', '70111000', '70060000'),
('KHAERUDIN KHAERUDIN', '<EMAIL>', '89206365', '80090000', '80091002', '70091000', '70090000'),
('SANDY TRIATMOJO NUGROHO', '<EMAIL>', '89206366', '80090000', '80091003', '70091000', '70090000'),
('AHMAD SOFAN NURDIANSAH', '<EMAIL>', '89206367', '80090000', '80091007', '70091000', '70090000'),
('MUHAMMAD ATHOK ILLAH', '<EMAIL>', '89206388', '80060000', '80111068', '70111000', '70060000'),
('SUTRA IRAWAN', '<EMAIL>', '89206394', '80060000', '80111043', '70111000', '70060000'),
('DIONYSIUS RICHO KRISMA KUSCHAHYO', '<EMAIL>', '89206399', '80060000', '80111088', '70111000', '70060000'),
('FERI DARLIAN', '<EMAIL>', '89206400', '80060000', '80111086', '70111000', '70060000'),
('CITRA LEONY', '<EMAIL>', '89206417', '80070000', '80071013', '70071000', '70070000'),
('HIMNI RUSYDI', '<EMAIL>', '89206430', '80060000', '80111067', '70111000', '70060000'),
('FAKHRI FAKHRI', '', '89216528', '80190174', '80053010', '70058000', '76010000'),
('GIODIFI RAHMATULLAH', '', '89216559', '80111026', '80111026', '70111000', '70060000'),
('RHEZA KUSUMAH', '<EMAIL>', '89216604', '80070000', '80071014', '70071000', '70070000'),
('MOHAMMAD MAKHRUS', '', '89216613', '80161005', '80161005', '70058000', '76010000'),
('IKHSAN SAPUTRA', '<EMAIL>', '89216623', '80060000', '80064012', '70064000', '70060000'),
('AGUS WARDIMAN', '<EMAIL>', '89216625', '80190174', '80161001', '70058000', '76010000'),
('HANNISA YANUAR UTAMA', '<EMAIL>', '89216687', '80060000', '80111006', '70111000', '70060000'),
('SLAMET RIYANTO', '', '89216695', '80140000', '80143003', '70143000', '70140000'),
('HERWIANTO YUDHISTIRA', '', '89216709', '80020002', '80024046', '70026000', '70020000'),
('RAMOS RAMOS', '', '89216718', '80020002', '80024015', '70026000', '70020000'),
('WANG GUODONG', '<EMAIL>', '89216808', '80000012', '80000012', '70002000', '70000000'),
('DONNA ARYATI', '', '89217000', '80190110', '80228001', '70263000', '70260000'),
('KEREN LINGGI', '<EMAIL>', '89217021', '80060000', '80111087', '70111000', '70060000'),
('SONDANG IMACULATA', '<EMAIL>', '89217091', '80190174', '80053161', '70058000', '76010000'),
('LUCIA DWI', '<EMAIL>', '89217153', '80140000', '80143012', '70143000', '70140000'),
('BRILLYAN ANGGA FRADHIKA', '<EMAIL>', '89217163', '80060000', '80111025', '70111000', '70060000'),
('M. LUKMAN HAKIM', '', '89217164', '80060000', '80111008', '70111000', '70060000'),
('MUHAMMAD MAULANA FIRMAN', '', '89217165', '80060000', '80111009', '70111000', '70060000'),
('MUHAMMAD RIZA IRVANSYAH', '<EMAIL>', '89217166', '80060000', '80111015', '70111000', '70060000'),
('SETYA WINATA WIGUNG PRATAMA', '<EMAIL>', '89217167', '80060000', '80111051', '70111000', '70060000'),
('TRISNA MAULANA', '<EMAIL>', '89217168', '80060000', '80111052', '70111000', '70060000'),
('AHMAT AZIS', '<EMAIL>', '89217169', '80060000', '80111073', '70111000', '70060000'),
('ARMAN PRASETYO', '<EMAIL>', '89217170', '80060000', '80111029', '70111000', '70060000'),
('BAGAS SAPUTRO', '', '89217171', '80060000', '80111018', '70111000', '70060000'),
('DEFRI FIRMAN MAULANA', '<EMAIL>', '89217172', '80060000', '80111038', '70111000', '70060000'),
('DITO NOVANDA RAHMAN', '<EMAIL>', '89217173', '80060000', '80111011', '70111000', '70060000'),
('GUNTUR LEO CRISTIONO', '<EMAIL>', '89217174', '80060000', '80111037', '70111000', '70060000'),
('RACHMAT IRVAN KHADAFI', '', '89217175', '80060000', '80111025', '70111000', '70060000'),
('FAISAL RAFLI ARDIANSYAH', '<EMAIL>', '89217176', '80060000', '80111040', '70111000', '70060000'),
('GALIH BASKORO', '<EMAIL>', '89217179', '80060000', '80111027', '70111000', '70060000'),
('JAMILLATIF JAMILLATIF', '', '89217202', '80140000', '80143013', '70143000', '70140000'),
('YONAS YAHYA', '<EMAIL>', '89217237', '80020002', '80024020', '70026000', '70020000'),
('AWAN SANTOSO', '<EMAIL>', '89217352', '80060000', '80064004', '70064000', '70060000'),
('FATCHANUDIN FATCHANUDIN', '<EMAIL>', '89227469', '80160000', '80161010', '70161000', '70160000'),
('MA QIANG', '', '89227551', '80000015', '80000015', '70002000', '70000000'),
('MUHAMMAD PRAMADIATHALLA', '<EMAIL>', '89227557', '80190134', '80024099', '70292000', '70290000'),
('EVI TRIE HARDIANI', '<EMAIL>', '89227603', '80090000', '80091009', '70091000', '70090000'),
('TAUFIK UDASMARA', '', '89227643', '80050000', '80053004', '70053000', '70050000'),
('MIFTAHUS SYURUR', '<EMAIL>', '89227720', '80060000', '80111036', '70111000', '70060000'),
('PARAMITHA SETYOASTUTI', '<EMAIL>', '89227738', '80230001', '80171011', '70171000', '70230000'),
('TYAS TITANINGRUM', '', '89227757', '80060000', '80111037', '70111000', '70060000'),
('MOHAMMAD SYAMSUL MUARIF', '<EMAIL>', '89227758', '80060000', '80111064', '70111000', '70060000'),
('BASA LAKSANA', '<EMAIL>', '89227781', '80060000', '80064009', '70064000', '70060000'),
('TATYANA PUTRI ARININGRUM', '<EMAIL>', '89227870', '80090000', '80091017', '70091000', '70090000'),
('RUHUL BAYAN', '<EMAIL>', '89227912', '80190110', '80228013', '70058000', '76010000'),
('JIMI ALEXANDER', '<EMAIL>', '89228033', '80060000', '80064010', '70064000', '70060000'),
('RIZKY GILANG BAGUS ALFIAN', '<EMAIL>', '89228037', '80060000', '80111061', '70111000', '70060000'),
('BAMBANG SUDIANTO', '<EMAIL>', '89228085', '80060000', '80111041', '70111000', '70060000'),
('HARDIAN AFFANDY', '<EMAIL>', '89228164', '80090000', '80091013', '70091000', '70090000'),
('INNE SHEFIRA', '<EMAIL>', '89228268', '80070000', '80071015', '70071000', '70070000'),
('DWINANDANA PRAFITRARTO', '<EMAIL>', '89228295', '80160000', '80161004', '70160000', '70160000'),
('ACHMAD SETIAWAN', '<EMAIL>', '89228427', '80020002', '80024108', '70026000', '70020000'),
('QOBID WADUDU', '<EMAIL>', '89228428', '80150016', '80086003', '70086000', '70080000'),
('FERI FERDIANSYAH', '<EMAIL>', '89228429', '80150016', '80086004', '70086000', '70080000'),
('ALIYATIN ALIYATIN', '<EMAIL>', '89228430', '80150016', '80086005', '70086000', '70080000'),
('SYAMSUDDIN SYAMSUDDIN', '<EMAIL>', '89228520', '80210026', '80228056', '70058000', '76010000'),
('CHRISTIAN SIMANJUNTAK', '<EMAIL>', '89228822', '80190134', '80024011', '70292000', '70290000'),
('ARWAN BANI KARNADI', '<EMAIL>', '89228943', '80020002', '80024104', '70026000', '70020000'),
('MAHDI ARIYANTO', '<EMAIL>', '89228990', '80020002', '80024105', '70026000', '70020000'),
('DWI RATNA PRAWESTRI', '<EMAIL>', '89229360', '80060000', '80111031', '70111000', '70060000'),
('KUKUH RACHMAD SETYAWAN', '<EMAIL>', '89229396', '80060000', '80111055', '70111000', '70060000'),
('GATRUH DWI UNTARA', '<EMAIL>', '89229424', '80060000', '80111078', '70111000', '70060000'),
('RIDHA ADHA', '<EMAIL>', '89229478', '80060004', '80064001', '70064000', '70060000'),
('MAHTUBAH MAHTUBAH', '', '89230113', '80143002', '80143002', '70143000', '70140000'),
('FARAH RAIHANAH', '', '89230190', '80033002', '80033002', '70033000', '70030000'),
('WAHYU RIESKY PRATAMA', '<EMAIL>', '89230411', '80020002', '80024013', '70026000', '70020000'),
('HANA LAURA', '', '89230492', '80190110', '80141001', '70263000', '70260000'),
('RANA NURTAHANY', '<EMAIL>', '89230512', '80020002', '80024021', '70026000', '70020000'),
('IIF FAKHRUDIN', '', '89230605', '80060000', '80064006', '70064000', '70060000'),
('MAELANI MAELANI', '<EMAIL>', '89230676', '80030000', '80033001', '70033000', '70030000'),
('ARIE AVIANA', '', '89230953', '80036001', '80036001', '70036000', '70280000'),
('FARAH FEBRIANA', '<EMAIL>', '89230984', '80190110', '80228002', '70263000', '70260000'),
('BAMBANG PRIYAMBODO', '<EMAIL>', '89230997', '80060004', '80064007', '70064000', '70060000'),
('SATYA WIRAPUTRA', '<EMAIL>', '89230998', '80060000', '80064008', '70064000', '70060000'),
('HARI PRAYOGO', '<EMAIL>', '89230999', '80060000', '80064011', '70064000', '70060000'),
('HEDI KASMANTO', '<EMAIL>', '89231007', '80090000', '80091014', '70091000', '70090000'),
('SHERENA SUSANTO', '<EMAIL>', '89231020', '80070000', '80071007', '70071000', '70070000'),
('FIRZA RIZQULLAH', '<EMAIL>', '89231085', '80090000', '80091004', '70091000', '70090000'),
('ADIAN SATRIO JATI', '<EMAIL>', '89231086', '80090000', '80091008', '70091000', '70090000'),
('NABILA ZAHRA', '<EMAIL>', '89231087', '80090000', '80091010', '70091000', '70090000'),
('GURITNO YUDHO WIBOWO', '<EMAIL>', '89231088', '80090000', '80091011', '70091000', '70090000'),
('MEIDY ARISTYO', '<EMAIL>', '89231110', '80090000', '80091015', '70091000', '70090000'),
('RIDHA HAYATI', '<EMAIL>', '89231112', '80090000', '80091018', '70091000', '70090000'),
('DEAN ARIEL', '', '89231185', '80036002', '80036002', '70036000', '70280000'),
('VIDY DACOSTA', '<EMAIL>', '89231191', '80060000', '80111044', '70111000', '70060000'),
('INDIRA ADELIA', '<EMAIL>', '89231253', '80190117', '80271002', '70279000', '70270000'),
('RIZKI YULIDAR', '<EMAIL>', '89231263', '80090000', '80091020', '70091000', '70090000'),
('ANGELICA ATANNIA', '<EMAIL>', '89231314', '80230001', '80171029', '70171000', '70230000'),
('HERMAN HERMAN', '<EMAIL>', '89231315', '80090000', '80091016', '70091000', '70090000'),
('AHMAD MUSTHOFA', '<EMAIL>', '89231346', '80190174', '80228066', '70058000', '76010000'),
('CHOIRUL FANANI', '<EMAIL>', '89231357', '80060000', '80111009', '70111000', '70060000'),
('CARMILLA YUSTIKASARI', '<EMAIL>', '89239564', '80020002', '80024103', '70026000', '70020000'),
('FERI ANDIANTO', '<EMAIL>', '89241383', '80190110', '80228003', '70263000', '70260000'),
('FITRIA PARANDANGI', '<EMAIL>', '89241384', '80190110', '80141001', '70263000', '70260000'),
('DYAH ANINDITA', '<EMAIL>', '89241385', '80190174', '80053007', '70058000', '76010000'),
('GRISELDA MELANIA YAHYA', '<EMAIL>', '89241388', '80070000', '80071016', '70071000', '70070000'),
('MUHAMMAD AZIZI', '<EMAIL>', '89241404', '80030000', '80033003', '70033000', '70030000'),
('FAKHRI FAHRUDIN', '<EMAIL>', '89241411', '80190174', '80053010', '70058000', '76010000'),
('UTARI AYU DAMAYANTI', '<EMAIL>', '89241414', '80023010', '80024009', '70024000', '70210000'),
('ZHAN LUJIA', '<EMAIL>', '89241463', '80000015', '80000015', '70002000', '70000000'),
('GALIH PUTU WIJAYA', '<EMAIL>', '89241484', '80190110', '80228004', '70263000', '70260000'),
('ANISA PERMANA', '<EMAIL>', '89241504', '80150016', '80086001', '70086000', '70080000'),
('ZHANG FENG', '<EMAIL>', '89241518', '80000016', '80000016', '70002000', '70000000'),
('LI GUIZAI', '<EMAIL>', '89241519', '80000018', '80000018', '70002000', '70000000'),
('GAO BOYU', '<EMAIL>', '89241528', '80000017', '80000017', '70002000', '70000000'),
('AMINOL WAHET', '<EMAIL>', '89241554', '80060000', '80111010', '70111000', '70060000'),
('AMIRRULLOH HAKIM', '<EMAIL>', '89241650', '80150016', '80086006', '70086000', '70080000'),
('XU JINCHENG', '<EMAIL>', '89241678', '80000019', '80000019', '70002000', '70000000'),
('FATANINDA KESUMAPUTRI', '<EMAIL>', '89241701', '80190174', '80228019', '70058000', '76010000'),
('NOVI EKO PRASETYO', '<EMAIL>', '89241702', '80060000', '80111039', '70111000', '70060000'),
('ANTHON CASSANDRA TRIANTO', '<EMAIL>', '89241703', '80190110', '80228001', '70263000', '70260000'),
('AFIF TENTRY', '<EMAIL>', '89241707', '80060000', '80111085', '70111000', '70060000'),
('AFIF TENTRY', '<EMAIL>', '89241722', '80060000', '80111075', '70111000', '70060000'),
('DEAN ARIEL', '<EMAIL>', '89241740', '80030000', '80033004', '70033000', '70030000'),
('MUHAMMAD RIZKY', '<EMAIL>', '89241760', '80060000', '80111076', '70111000', '70060000'),
('KUS HENDRAWAN MUIZ', '<EMAIL>', '89241782', '80090000', '80091021', '70091000', '70090000'),
('OSCAR EKO PRASETYO', '<EMAIL>', '89241809', '80060000', '80111079', '70111000', '70060000');
