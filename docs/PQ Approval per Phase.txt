PQ Approval per Phase

["REQUIREMENT", "REGISTRATION", "EVALUATION", "CLARIFICATION"]

"REQUIREMENT" (alternative) : 1. Staff / Sr. Staff Procurement 2. Staff / Sr. Staff User 3. Head / Sr. Head Procurement 4. Head / Sr. Head User 5. Manager Procurement

"REGISTRATION" / REGISTRATION EVALUATION : 1. Staff PBR (Bidding Officer)

"EVALUATION" : 1. Staff Procurement 2. Head / Sr. Head of Procurement 3. User Manager 4. Procurement Manager

"CLARIFICATION" : 1.



data flow pergantian phase prequalification
1. prequalification_template -> currentPhase mendefinisikan phase yg berjalan sekarang
2. prequalification_template -> phase{Requirement, Registation, Evaluation, Submission, Clarification} -> status mendefinisikan phase tersebut sudah approve atau belum

submission evaluation Approval
no 2 hilang dari approval Requirement

bila next approval user nya sama langsung auto approve

pq clarification approval example (SA2410001):
1. RIEKA_YULIANTI
2. OKY_ELDYAGUSTA
3. LISTIANI_DEWI (SMVP) ?
4. IRENA SULIANTO