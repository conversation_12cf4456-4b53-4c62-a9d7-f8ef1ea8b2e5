# @url=https://eproc.kangservice.cloud/api
# @url=https://eproc-stg.hcml.co.id/api
@url=http://localhost:3000

@recordId=2C8XH8CRZW77ZI5P
@journeyId=MDGBG0QHKIPLZ3CG

# http://localhost:3000/api/recording/record/C80EAIPAJAJ2Q01E/download

### DELETE_ALL_DATA #################################
DELETE {{url}}/deletealldata
Content-Type: application/json

########################################################################
########################################################################
########################################################################
########################################################################

### GET_RECORDING_STATUS
GET {{url}}/recording/status

### SET_RECORDING_STATUS_ON
POST {{url}}/recording/status/enabled

### SET_RECORDING_STATUS_OFF
POST {{url}}/recording/status/disabled

### GET_ALL
GET {{url}}/recording/record
  # ?ids=
  # ?descriptionLike=
  # ?funcNameLike=
  # ?requestType=
  # ?isSuccess=
  # ?page=
  # ?size=


### GET_ONE
GET {{url}}/recording/record/{{recordId}}


### REPLAY_RECORDING
POST {{url}}/recording/record/{{recordId}}/replay
Content-Type: application/json

### DOWNLOAD_RECORDING
GET {{url}}/recording/record/{{recordId}}/download
Content-Type: application/json


### UPDATE_RECORDING
PUT {{url}}/recording/record/{{recordId}}
Content-Type: application/json

{ "description": "test", "input": "" }


### DELETE_ALL_RECORDING
DELETE {{url}}/recording/record


### DELETE_ONE_RECORDING
DELETE {{url}}/recording/record/{{recordId}}


### CLONE
POST {{url}}/recording/record/{{recordId}}/clone
Content-Type: application/json

{ "description": "login sebagai yulia", "input": "" }


### CREATE_JOURNEY 1
POST {{url}}/recording/journey    
Content-Type: application/json

{
  "description": "scenario sampai requisition",
  "recordIds": [
		"7263ODHVV274KCPI",
		"GCOVUIV121S9HWDW",
		"UYF15L6EKQI5ZV7X",
		"308JN9MEG698X5UB",
		"BZLV5KUCRUMGV8I2",
		"6LBA7N5A2Y599PC8",
		"PXJORTHDZ9VS6AFF",
		"UR0ZV3XQ6GE8OE0V",
		"8JCQW56N46CKXAON",
		"3Y87NSL4QJPPGT3R",
		"7RI8BVGK2FGHOX08",
		"ZXDMVNFHN3DJGA50",
		"IGDJPCCMSIMAN9GM",
		"DY095ZVAPISP8K2B",
		"GHLB0GEIA75MIZZL",
		"3RL0E9V7AC0ZUQR7",
		"WZKYVK2RNDXBDBOH",
		"LQU01VENW83JYVYS",
		"U497NAI8AZ91VAIY",
		"NDO564SRZCUUAHQ1",
		"WVYHF59FJSIEEPOL",
		"X5A9VBTW8Y1MFROD",
		"WJR34D4MBOP2G1L4",
		"MWWEH3Z9CIWQCUG5",
		"V4Z4674U46FYCYJN",
		"YZFE04MYBO1FYIQ9",
		"ALO4ESAVFP77EV9J",
		"7L663EC9OOJGV64R",
		"JGR7UQQYIBZUPS69",
		"JLA2MZHRE33OBTT7",
		"FLO9CQLY1F7FMKWE",
		"Y51L0WG0GIH0C1A4",
		"YQ5Y7Z5RT1G1484M",
		"H0W1N9VQ4VYUVMB4",
		"C80EAIPAJAJ2Q01E",
		"ETXVS31IC77SLY0L",
		"ZZUR5SY5ISX036XS",
		"KRDFCA8LP1JSNEYV",
		"2C8XH8CRZW77ZI5P",
		"6SZJGPDQQ0N23LKJ",
		"RQO9PDMHXG86QK89",
		"ZZT1LCVOHTHHQVSC",
		"KQ0LH5VJEMBDRPSD",
		"W4O8XDXC6CBRL0PE",
		"W7SJC2UUGBWSQ4RG",
		"WI7M2CBVIWUCPCJN",
		"RZ44DHO9THFRIDFI"
  ]
}


### CREATE_JOURNEY 2
POST {{url}}/recording/journey    
Content-Type: application/json

{
  "description": "scenario sampai requisition ke-2",
  "recordIds": [
		"G4QIAHE3CYWMM39O",
		"HV0XMI588A7V1MOS",
		"Y8YOCMY1HDABGC14",
		"BNFH615MS10PVI5T",
		"H9VJA0TM2A4H9U3I",
		"VSWRHK8FIJVPRKI7",
		"UIK3APYHQM00IF1Q",
		"K8AVMO1QN27H2F51",
		"9H0TJT7IG6RNS190",
		"F1U1116SWYCN9WCQ",
		"RZ7S5RC2M2E53D8H",
		"GGZEUASSC6WM36L0",
		"BGPFEXTHZ3OK3Y2B",
		"FRG1ST8IJTWIMT1F",
		"GMDTHXPKCMYON0ZX",
		"F4DNVICERZIW92XJ",
		"L9XW1GQEFGFVYBMH",
		"PZGW5DTIO3UBIQNP",
		"AT4J723U81ITOA4Y",
		"EYP6OGLYSK7D6U88",
		"MIZ1ZZIYJF77V15C",
		"I1UHMENWVH7CVYLT",
		"OK11IB2NGKILGOYG",
		"71GQIZAHZD4YXLA9",
		"AHN63BFDFPJTYKFF",
		"5EUI9RRVBZCAPKV9",
		"JU53AZD37VAKIK8M",
		"NW30AKEHVY50K6S2",
		"BJV746BM2E20P8Y0",
		"QW4W6YVFNPYQWW6N",
		"VPA0FDOWN4ZIJAK1",
		"KVVXWX0J8K0TY992",
		"7X0W0FI54MRAY93F",
		"9GQQ8HXCSCH4KG92",
		"42SUC2ADP4FRGA0X",
		"TWUQ28B25GPTIPHR",
		"8H50F0HR01PVJBNF",
		"VE3ZN7H4G4YDGGXN",
		"DA2PZVRDV75B6PI0",
		"OAMQ1C8NRA67C6JP",
		"I6MRU6Y4KTM9E3FJ",
		"4MD5TR974NX6KBWW",
		"G7FWXUXOXBQV1FVQ",
		"I8RC3N5HT8T9HOEF",
		"MR9OJXCTP41QLSAB",
		"DOA73FJGUPUJ8O0F",
		"8HT3DTED0Z90G5KB",
		"ODQVWNSU5AVFO64G",
		"KTERX8VCEM1DDVR4",
		"A5WQEHIHS2H0IVQV",
		"F20CN3VACKO16ZT5",
		"716V1SH90Q7FBOSU",
		"788AIFQ4ELGIKWM7",
		"IV5RQ0TC8XW7W5RY",
		"X9PLEI1XOSYREKHK",
		"ADR7SZYHYZXABSDF",
		"M0SEQSWP7TZCVNTD",
		"UXM11MI7IX3ECDFP",
		"QYJNJY0N4STD4RCD",
		"N1BFX9DMU8R4WAGO",
		"OYHKO36T69VB7SCX",
		"N30YV7EQWP49PQWW",
		"6T6YY3AP13XGZVDO",
		"1JPJ916GK1KX19WO",
		"APPB6H6T4BSXY8OI",
		"05YGYV4WRKY0JPOQ",
		"NU5ISLG3JLMJV6BM",
		"IMH77EQ0D0FFWADZ",
		"40MKOTZAJ4CPJQTI",
		"HK9PD9BXFIXRDHUG",
		"QA6GBWJDBXJX5I6L",
		"Z8GOWYLNS13JHM3D",
		"QWWYPCW3A05KODY2",
		"QSJW6SC38D8EA8A1",
		"I5ZR689UPMPTP19Q",
		"352EQKDFNJEDFW82",
		"15DU5ZTQ9L4D0938",
		"27JKNLZXYT0RMOJS",
		"20FSQMCHLQUMLQAK",
		"1H9XOK91US21YUXU",
		"N651J4HUO139TEHR",
		"7EV93Z9H5E3RKG3H",
		"L2M4DJQ4I2UQBRCU",
		"E45NYKLSSYDYMEWF",
		"UHDYO0P80WAFTAER",
		"1J9MVZUVK30OONUS",
		"WXOXP6CMGC6IB5GQ",
		"O7GJENLC4N02H04A",
		"73E9EGCSRU8I8PNB"
	]
}



	

### GET_ALL_JOURNEY
GET {{url}}/recording/journey        
Content-Type: application/json


### GET_ONE_JOURNEY
GET {{url}}/recording/journey/{{journeyId}}                 


### UPDATE_JOURNEY
PUT {{url}}/recording/journey/{{journeyId}}      
Content-Type: application/json

{
  "description": "scenario 1",
  "recordIds": [
    "J0YVNU5OC6DMMBUX",
    "L6799YF9IIOQBN2R",
    "K35JBDUO8DOGJ3JU",
    "YG2NGFOW0GQNJG6O",
    "YGFC7F1H5NN2J27E"
  ]
}


### DELETE_ONE_JOURNEY
DELETE {{url}}/recording/journey/{{journeyId}}                 


### REPLAY_JOURNEY
POST {{url}}/recording/journey/{{journeyId}}/replay          
