include:
  - template: Jobs/SAST.gitlab-ci.yml
  - template: Jobs/Container-Scanning.gitlab-ci.yml
  - template: Jobs/Dependency-Scanning.gitlab-ci.yml
  - template: Jobs/Code-Quality.gitlab-ci.yml
  - project: "mgtteamproject/devops/tools/cicd-templates"
    file: "stages.gitlab-ci.yml"
    ref: master
  - project: "mgtteamproject/devops/tools/cicd-templates"
    file: "jobs.gitlab-ci.yml"
    ref: master

variables:
  K8S_PROJECT: "Eproc"
  APP_NAME: eproc-be

workflow:
  rules:
    # Development
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
      variables:
        NAMESPACE: dev
        KUBE_CONFIG: $KUBE_CONFIG_DEV
        ID_RSA: $ID_RSA_DEV
    # Staging
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+-rc(.+)?/'
      variables:
        NAMESPACE: stg
        KUBE_CONFIG: $KUBE_CONFIG_STG
        ID_RSA: $ID_RSA_STG
    # Production
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      variables:
        NAMESPACE: prod
        KUBE_CONFIG: $KUBE_CONFIG_PROD
        ID_RSA: $ID_RSA_PROD

code_quality:
  image: docker:git
  services:
    - docker:dind
  variables:
    REPORT_FORMAT: html
    CODE_QUALITY_DISABLED: "false"
  artifacts:
    paths: [gl-code-quality-report.html]
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual

# migration-dev:
#   stage: migration
#   extends:
#     - .run-job-k8s
#     - .deploy-dev-refs
#   needs: ["build"]
#   # dependencies:
#   #   - build
#   variables:
#     JOB_NAME: migration
#   rules:
#     - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
#       changes:
#         - src/database/migrations/**/*
#       when: always

build:
  stage: build
  extends:
    - .build-docker
    - .build-refs

retag:
  stage: build
  extends:
    - .retag-image
    - .deploy-prod-refs
    - .deploy-stg-refs


deploy-dev:
  stage: deploy
  extends:
    - .deploy-k8s
    - .deploy-dev-refs
  needs: ["build"]

restart-dev:
  stage: restart
  extends:
    - .restart-k8s
    - .deploy-dev-refs

rollback-dev:
  stage: restart
  extends:
    - .rollback-k8s
    - .deploy-dev-refs

rollout-history-dev:
  stage: release
  extends:
    - .rollout-history-k8s
    - .deploy-dev-refs

deploy-stg:
  stage: deploy
  extends:
    - .deploy-k8s-tagging
    - .deploy-stg-refs
  needs: ["retag"]
  tags:
    - deploy


restart-stg:
  stage: restart
  extends:
    - .restart-k8s
    - .deploy-stg-refs
  tags:
    - deploy

rollback-stg:
  stage: restart
  extends:
    - .rollback-k8s
    - .deploy-stg-refs
  tags:
    - deploy

rollout-history-stg:
  stage: release
  extends:
    - .rollout-history-k8s
    - .deploy-stg-refs
  tags:
    - deploy

deploy-prod:
  stage: deploy
  extends:
    - .deploy-k8s-tagging
    - .deploy-prod-refs
  needs: ["retag"]
  tags:
    - deploy

restart-prod:
  stage: restart
  extends:
    - .restart-k8s
    - .deploy-prod-refs
  tags:
    - deploy

rollback-prod:
  stage: restart
  extends:
    - .rollback-k8s
    - .deploy-prod-refs
  tags:
    - deploy

rollout-history-prod:
  stage: release
  extends:
    - .rollout-history-k8s
    - .deploy-prod-refs
  tags:
    - deploy

release:
  stage: release
  extends:
    - .package-release
