# Use postgres/example user/password credentials
version: "3.1"

services:
  db:
    container_name: database
    image: postgres
    restart: always
    volumes:
      - ~/apps/postgres:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
    #      POSTGRES_DB: mydb
    ports:
      - "5432:5432"

  # app:
  #   container_name: eproc-app
  #   image: eproc-app
  #   environment:
  #     - SERVER_PORT=3000
  #     - SECRET_KEY=thisisyoursecretkey
  #     - TOKEN_EXPIRATION=100h
  #     - DATABASE_USR_HOST=localhost
  #     - DATABASE_USR_NAME=postgres
  #     - DATABASE_USR_USER=root
  #     - DATABASE_USR_PASSWORD=root
  #     - DATABASE_EPROC_HOST=localhost
  #     - DATABASE_EPROC_NAME=db_eproc_local
  #     - DATABASE_EPROC_USER=root
  #     - DATABASE_EPROC_PASS=root
