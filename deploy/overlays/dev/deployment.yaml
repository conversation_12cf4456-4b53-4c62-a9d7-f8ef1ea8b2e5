apiVersion: apps/v1
kind: Deployment
metadata:
  name: eproc-be
spec:
  revisionHistoryLimit: 3
  replicas: 1
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-addr: http://vault.vault.svc.cluster.local:8200
        vault.security.banzaicloud.io/vault-agent: "false"
        vault.security.banzaicloud.io/vault-path: kubernetes
        vault.security.banzaicloud.io/vault-role: eproc-dev
        vault.security.banzaicloud.io/vault-skip-verify: "true"
    spec:
      containers:
        - name: app
          env:
            - name: TZ
              value: vault:eproc/data/dev/env#TZ
          resources:
            limits:
              cpu: 200m
              memory: 254M
            requests:
              cpu: 100m
              memory: 128M
