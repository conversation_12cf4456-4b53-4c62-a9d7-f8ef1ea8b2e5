import { HTTPDataItem } from "../../framework/data_http.js";

export function groupingControllerWithTag(apis: { prefix: string; httpDatas: HTTPDataItem[] }[]) {
  //

  // const httpData: HTTPData = {
  //   httpDatas: [],
  //   securities: ["bearerAuth"],
  // };

  // const router = express.Router();

  const groupedData: { [tag: string]: HTTPDataItem[] } = {};

  apis.forEach((a) => {
    a.httpDatas.forEach((item) => {
      //

      if (item.security?.length) {
        item.header = {
          Authorization: {
            type: "string",
          },
        };
      }

      if (groupedData[item.tag]) {
        groupedData[item.tag].push({ ...item, path: a.prefix + item.path });
      } else {
        groupedData[item.tag] = [{ ...item, path: a.prefix + item.path }];
      }
    });
  });

  const result: { tag: string; httpDatas: HTTPDataItem[] }[] = [];
  for (const tag in groupedData) {
    result.push({ tag, httpDatas: groupedData[tag] });
  }

  // router.get("/", (req, res) => {
  //   res.json(result);
  // });

  return result;
}

// export function groupingControllerWithTag(httpData: HTTPData[]): { tag: string; httpDatas: HTTPData[] }[] {
//   const groupedData: { [tag: string]: HTTPData[] } = {};
//   httpData.forEach((item) => {
//     if (groupedData[item.tag]) {
//       groupedData[item.tag].push(item);
//     } else {
//       groupedData[item.tag] = [item];
//     }
//   });
//   const result: { tag: string; httpDatas: HTTPData[] }[] = [];
//   for (const tag in groupedData) {
//     result.push({ tag, httpDatas: groupedData[tag] });
//   }
//   return result;
// }
