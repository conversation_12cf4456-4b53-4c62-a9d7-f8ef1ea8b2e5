import { DataSource, ILike, In } from "typeorm";
import { getManager } from "../../framework/gateway_typeorm.js";
import { FindDelegation } from "../model/model_delegation.js";
import { Delegation } from "./table_delegation.js";

export const implFindAllDelegation = (ds: DataSource): FindDelegation => {
  return async (ctx, filter) => {
    //
    let where: any | [] = {};

    if (filter?.ids && filter.ids.length > 0) where.id = In(filter.ids);
    if (filter?.delegateToUserId) where.delegateToUserId = filter.delegateToUserId;
    if (filter?.nameLike) where.name = ILike(`%${filter.nameLike}%`);
    if (filter?.startDate) where.startDate = filter.startDate;
    if (filter?.endDate) where.endDate = filter.endDate;
    if (filter?.type) where.type = filter.type;

    const result = await getManager(ctx, ds)
      .getRepository(Delegation)
      .findAndCount({
        where,
        order: {
          id: "ASC",
        },
      });

    return result;
  };
};
