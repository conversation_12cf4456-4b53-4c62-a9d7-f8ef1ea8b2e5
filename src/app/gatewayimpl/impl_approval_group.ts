import { DataSource, In } from "typeorm";
import { ApprovalGroup } from "./table_approval.js";
import { FindApprovalGroup, FindApprovalGroupNative } from "../model/model_approval.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { defaultFilterSize } from "./_gateway.js";

export const implFindAllApprovalGroup = (ds: DataSource): FindApprovalGroup => {
  //
  return async (ctx, filter) => {
    //
    let where: any | [] = {};

    // FIXME change to native query

    if (filter?.documentId) where.documentId = filter.documentId;
    if (filter?.documentType) where.documentType = filter.documentType;
    if (filter?.userId) where.user = { id: filter.userId };
    if (filter?.id) where.id = filter.id;
    if (filter?.positionId) where.position = { id: filter.positionId };
    if (filter?.status) where.status = filter.status;
    if (filter?.role) where.role = filter.role;

    if (filter?.statuses && filter?.statuses.length > 0) where.status = In(filter.statuses);

    const result = await getManager(ctx, ds)
      .getRepository(ApprovalGroup)
      .findAndCount({
        where,
        order: {
          sequence: "ASC",
        },
      });

    return result;
  };
};

export const implFindAllApprovalGroupNative = (ds: DataSource): FindApprovalGroupNative => {
  //
  return async (ctx, filter) => {
    //

    // const query = `
    // SELECT *
    // FROM approval_group
    // WHERE
    //   approvals @> '[
    //     {
    //       "users": [{"id": "${filter.userId}"}],
    //       "status": "${filter.status}"
    //     }
    //   ]'
    //   OR approvals @> '[
    //     {
    //       "position": {"id": "${filter.positionId}"},
    //       "status": "${filter.status}"
    //     }
    //   ]'`;

    const query = `SELECT * FROM approval_group WHERE approvals @> $1 OR approvals @> $2 LIMIT $3 OFFSET $4`;
    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;

    let usersParams: any = {
      users: [{ id: filter.userId }],
      status: filter.status,
    };

    let positionParams: any = {
      position: { id: filter.positionId },
      status: filter.status,
    };

    if (filter.delegationUserApproval) {
      usersParams = {
        users: [{ 
          delegation: {
            user: { id: filter.userId },
          } 
        }],
        status: filter.status,
      };
  
      positionParams = {
        currentUserInPosition: {
          delegation: {
            user: { id: filter.userId },
          }
        },
        status: filter.status,
      };
    }

    const result = await getManager(ctx, ds).query(query, [
      JSON.stringify([usersParams]),
      JSON.stringify([positionParams]),
      size, // limit
      (page - 1) * size, // offset
    ]);

    return [result, result.length];
  };
};
