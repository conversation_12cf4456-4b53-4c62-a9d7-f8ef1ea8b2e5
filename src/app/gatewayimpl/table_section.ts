import { Column, <PERSON><PERSON>ty, ManyToOne, PrimaryColumn } from "typeorm";
import { Department as IDepartment } from "../model/model_department.js";
import { Section as ISection } from "../model/model_section.js";
import { Department } from "./table_department.js";

@Entity()
export class Section implements ISection {
  @PrimaryColumn({ type: "varchar", length: 20 })
  declare id: string;

  @Column({ type: "text" })
  declare name: string;

  @ManyToOne(() => Department)
  declare department: IDepartment | null;

  @Column({ type: "boolean" })
  declare isSection: boolean;
}
