import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { Log as ILog } from "../model/model_log.js";

@Entity("log")
export class Log implements ILog {
  @PrimaryColumn({ type: "text" })
  declare id: string;

  @Column({ type: "text" })
  declare name: string;

  @Column({ type: "jsonb" })
  declare message: any;

  @Column({ type: "text" })
  declare status: string;

  @Column({ type: "timestamp", nullable: true })
  declare createdAt: Date;
}
