import { <PERSON>Source, Find<PERSON><PERSON>s<PERSON>here, <PERSON>ike, In, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Not } from "typeorm";
import { getManager } from "../../framework/gateway_typeorm.js";
import { FindProcPlanDetail, FindProcPlanDetailByFromExcludeDepartment, FindProcPlanDetailByRbtb, FindProcPlanHeader } from "../model/model_procplan.js";
import { ProcPlanDetail, ProcPlanHeader } from "./table_proc_plan.js";
import { defaultFilterSize } from "./_gateway.js";

export const implFindAllProcPlanHeader = (ds: DataSource): FindProcPlanHeader => {
  //
  return async (ctx, filter) => {
    //
    let where: any = {};

    if (filter?.id) where.id = filter.id;
    if (filter?.procPlanType) where.procPlanType = filter.procPlanType;
    if (filter?.year) where.year = filter.year;
    if (filter?.status) where.status = filter.status;
    if (filter?.isSendBack) where.isSendBack = filter.isSendBack;
    if (filter?.sectionIds && filter.sectionIds.length) where.section = { id: In(filter.sectionIds) };
    if (filter?.departmentIds && filter.departmentIds.length > 0) where.department = { id: In(filter.departmentIds) };

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;

    const result = await getManager(ctx, ds)
      .getRepository(ProcPlanHeader)
      .findAndCount({
        ...(filter.useSelect && {
          select: {
            id: true,
            year: true,
            procPlanType: true,
            count: true,
            totalValueEstimation: true,
            status: true,
            submittedDate: true,
            sections: true,
          },
        }),
        where,
        take: size,
        skip: (page - 1) * size,
        relations: {
          department: true,
          section: true,
          approvalGroup: true,
        },
        order: {
          submittedDate: "ASC",
        },
      });

    return result;
  };
};

export const implFindAllProcPlanDetail = (ds: DataSource): FindProcPlanDetail => {
  //
  return async (ctx, filter) => {
    //
    let where: FindOptionsWhere<ProcPlanDetail> | [] = {};

    if (filter?.id) where.id = filter.id;
    if (filter?.ids && filter.ids.length > 0) where.id = In(filter.ids);
    if (filter?.procPlanType) where.procPlanType = filter.procPlanType;
    if (filter?.departmentId) where.department = { id: filter.departmentId };
    if (filter?.departmentIds && filter.departmentIds.length > 0) where.department = { id: In(filter.departmentIds) };
    if (filter?.sectionId) where.section = { id: filter.sectionId };
    if (filter?.year) where.year = filter.year;
    if (filter?.status || filter?.status !== "") where.procPlanHeader = { status: filter.status };
    if (filter?.title) where.title = ILike(`%${filter.title}%`);
    if (filter?.includeCurrentYear) where.year = filter.currentYear && Not(filter.currentYear);
    if (filter?.procPlanHeaderId) where.procPlanHeader = { id: filter.procPlanHeaderId };
    if (filter?.procPlanHeaderIds && filter.procPlanHeaderIds.length) where.procPlanHeader = { id: In(filter.procPlanHeaderIds) };
    if (filter?.procPlanCode) where.procPlanCode = ILike(`%${filter.procPlanCode}%`);

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;

    const result = await getManager(ctx, ds)
      .getRepository(ProcPlanDetail)
      .findAndCount({
        where,
        take: size,
        skip: (page - 1) * size,
        relations: {
          department: true,
          section: true,
          procPlanHeader: {
            department: true,
            section: true,
            approvalGroup: true,
          },
        },
        order: {
          valueInUSD: "DESC",
        },
      });

    return result;
  };
};

export const implFindProcPlanDetailByRbtb = (ds: DataSource): FindProcPlanDetailByRbtb => {
  //
  return async (ctx, req) => {
    //

    const [result] = await getManager(ctx, ds) //
      .getRepository(ProcPlanDetail)
      .createQueryBuilder("proc_plan_detail")
      .leftJoinAndSelect("proc_plan_detail.procPlanHeader", "procPlanHeader")
      .leftJoinAndSelect("proc_plan_detail.section", "section")
      .leftJoinAndSelect("proc_plan_detail.department", "department")
      .where(`"requesterBackToBack" @> :rbtb`, { rbtb: JSON.stringify([{ id: req.userId }]) })
      .andWhere("proc_plan_detail.year = :year", { year: req.year })
      .getManyAndCount();

    // const query = `SELECT * FROM proc_plan_detail WHERE "requesterBackToBack" @> $1 AND year = $2`;
    // const result = await getManager(ctx, ds).query(query, [JSON.stringify([{ id: req.userId }]), req.year]);

    // const [result] = await getManager(ctx, ds) //
    //   .getRepository(ProcPlanDetail)
    //   .findAndCount({
    //     where: {
    //       requesterBackToBack: JsonContains({
    //         d: "",
    //       }),
    //       year: req.year,
    //     },
    //     relations: {
    //       procPlanHeader: true,
    //       section: true,
    //       department: true,
    //     },
    //   });

    return [result, result.length];
  };
};

export const implFindProcPlanDetailByFromExcludeDepartment = (ds: DataSource): FindProcPlanDetailByFromExcludeDepartment => {
  //
  return async (ctx, req) => {
    //

    const [result, count] = await getManager(ctx, ds) //
      .getRepository(ProcPlanDetail)
      .createQueryBuilder("proc_plan_detail")
      .leftJoinAndSelect("proc_plan_detail.department", "department")
      .leftJoinAndSelect("proc_plan_detail.procPlanHeader", "procPlanHeader")
      .leftJoinAndSelect("proc_plan_detail.section", "section")
      .where(`"creator" @> :creator`, { creator: JSON.stringify({ id: req.userId }) })
      .andWhere("proc_plan_detail.year = :year", { year: req.year })
      .andWhere("department.id <> :departmentId", { departmentId: req.departmentId })
      .andWhere(`"proc_plan_detail"."procPlanType" = :procPlanType`, { procPlanType: "UPP" })
      .getManyAndCount();

    // const query = `SELECT * FROM proc_plan_detail WHERE "creator" @> $1 AND "year" = $2 AND "departmentId" <> $3`;
    // const result = await getManager(ctx, ds).query(query, [JSON.stringify({ id: req.userId }), req.year, req.departmentId]);

    return [result, count];
  };
};
