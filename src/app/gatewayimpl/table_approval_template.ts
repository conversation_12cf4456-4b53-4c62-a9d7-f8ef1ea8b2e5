import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { ApprovalTemplate, ApprovalTemplateGroup as IApprovalTemplateGroup } from "../model/model_approval_template.js";
import { DocumentTemplate, TypeOf } from "../model/vo.js";

@Entity()
export class ApprovalTemplateGroup implements IApprovalTemplateGroup {
  //
  @PrimaryColumn({ type: "varchar", length: 25 })
  declare id: string;

  @Column({ type: "int" })
  declare sequence: number;

  @Column({ type: "jsonb" })
  declare approvalTemplates: ApprovalTemplate[];

  @Column({ type: "varchar", length: 25 })
  declare documentType: TypeOf<typeof DocumentTemplate>;
}
