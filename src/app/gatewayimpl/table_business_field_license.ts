import { Column, Entity, PrimaryColumn } from "typeorm";
import { BusinessFieldLicense as IBusinessFieldLicense } from "../model/model_business_field_license.js";

@Entity()
export class BusinessFieldLicense implements IBusinessFieldLicense {
  @PrimaryColumn({ type: "varchar", length: 100 })
  declare id: string;

  @Column({ type: "varchar", length: 200 })
  declare documentName: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  declare categoryCode: string | null;

  @Column({ type: "text", nullable: true })
  declare clasification: string | null;

  // @Column({ type: "text", nullable: true })
  // declare subClasification: string | null;
}
