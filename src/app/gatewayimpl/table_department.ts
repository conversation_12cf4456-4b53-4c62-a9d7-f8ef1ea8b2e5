import { Column, <PERSON>ti<PERSON>, PrimaryColumn } from "typeorm";
import { Department as IDepartment } from "../model/model_department.js";

@Entity()
export class Department implements IDepartment {
  @PrimaryColumn({ type: "varchar", length: 20 })
  declare id: string;

  @Column({ type: "text" })
  declare name: string;

  @Column({ type: "varchar", length: 5, nullable: true })
  declare code: string;

  constructor(id?: string, name?: string) {
    this.id = id!;
    this.name = name!;
  }
}
