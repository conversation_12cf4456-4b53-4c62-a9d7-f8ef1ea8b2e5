import { DataSource, ILike, In } from "typeorm";
import { Vendor } from "./table_vendor.js";
import { FindOneVendor, FindVendor } from "../model/model_vendor.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { defaultFilterSize } from "./_gateway.js";

export const implFindAllVendor = (ds: DataSource): FindVendor => {
  return async (ctx, filter) => {
    let where: any | [] = {};

    // if (filter?.id) where.id = filter.id;
    if (filter?.ids && filter.ids.length > 0) where.id = In(filter.ids);
    if (filter?.nameLike) where.name = ILike(`%${filter.nameLike}%`);

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;

    const result = await getManager(ctx, ds)
      .getRepository(Vendor)
      .findAndCount({
        where,
        take: size,
        skip: (page - 1) * size,
        order: {
          name: "ASC",
        },
      });

    return result;
  };
};

export const implFindOneVendor = (ds: DataSource): FindOneVendor => {
  return async (ctx, idVendor) => {
    const result = await getManager(ctx, ds)
      .getRepository(Vendor)
      .findOne({
        where: {
          id: idVendor,
        },
      });

    return result;
  };
};

export const InitDefaultVendorAccessApps = async (ds: DataSource) => {
  await ds.createQueryBuilder().update(Vendor).set({ accessApps: "procurement,other_app" }).whereInIds(["0000000000", "0001000292", "0001000562"]).execute();
};
