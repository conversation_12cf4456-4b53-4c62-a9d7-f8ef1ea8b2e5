import { <PERSON>Source, ILike, In } from "typeorm";
import { getManager } from "../../framework/gateway_typeorm.js";
import { FindAllBusinessFieldLicense, FindBusinessFieldLicenseDocument } from "../model/model_business_field_license.js";
import { defaultFilterSize } from "./_gateway.js";
import { BusinessFieldLicense } from "./table_business_field_license.js";

export const implFindAllBusinessFieldLicense = (ds: DataSource): FindAllBusinessFieldLicense => {
  //
  return async (ctx, filter) => {
    //
    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;

    let query = getManager(ctx, ds).getRepository(BusinessFieldLicense).createQueryBuilder("business_field_license");

    if (filter?.documentName) query = query.where(`business_field_license.documentName = :documentName`, { documentName: filter.documentName });
    if (filter?.search) query = query.andWhere(`business_field_license."categoryCode" ILIKE :categoryCode`, { categoryCode: `${filter.search}%` });
    if (filter?.search) query = query.orWhere(`business_field_license."clasification" ILIKE :clasification`, { clasification: `%${filter.search}%` });

    query = query
      .take(size)
      .skip((page - 1) * size)
      .orderBy("business_field_license.categoryCode", "ASC");

    const result = await query.getManyAndCount();

    return result;
  };
};

export const implFindBusinessFieldLicenseDocument = (ds: DataSource): FindBusinessFieldLicenseDocument => {
  //
  return async (ctx, filter) => {
    //
    let where: any | [] = {};

    // if (filter?.ids && filter?.ids.length > 0) where.id = In(filter.ids);
    if (filter?.search) where.documentName = ILike(`%${filter.search}%`);
    // if (filter?.search) where.categoryCode = ILike(`${filter.search}%`);
    // if (filter?.search) where.clasification = ILike(`$${filter.search}%`);

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter?.page < 1 ? 1 : filter?.page) || 1;

    const result = await getManager(ctx, ds) //
      .getRepository(BusinessFieldLicense)
      .findAndCount({
        where,
        take: size,
        skip: (page - 1) * size,
        order: {
          categoryCode: "ASC",
        },
      });

    return result;
  };
};
