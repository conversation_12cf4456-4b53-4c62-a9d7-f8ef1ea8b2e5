import { DataSource } from "typeorm";
import { FindPosition } from "../model/model_position.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { Position } from "./table_position.js";
import { defaultFilterSize } from "./_gateway.js";

export const implFindAllPosition = (ds: DataSource): FindPosition => {
  //

  return async (ctx, filter) => {
    //

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;

    const result = await getManager(ctx, ds) //
      .getRepository(Position)
      .findAndCount({
        where: { id: filter.id },
        take: size,
        skip: (page - 1) * size,
      });

    return result;
  };
};
