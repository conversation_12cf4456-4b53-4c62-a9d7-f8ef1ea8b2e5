import { Colum<PERSON>, <PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { DocumentHistory as IDocumentHistory } from "../model/model_document_history.js";
import { User as IUser } from "../model/model_user.js";
import { DocumentTemplate, TypeOf } from "../model/vo.js";

@Entity()
export class DocumentHistory implements IDocumentHistory {
  //

  @PrimaryColumn({ type: "varchar", length: 32 })
  declare id: string;

  @Column({ type: "varchar", length: 25 })
  declare documentId: string;

  @Column({ type: "varchar", length: 25 })
  declare documentType: TypeOf<typeof DocumentTemplate>;

  @Column({ type: "timestamp", nullable: true })
  declare date: Date | null;

  @Column({ type: "jsonb" })
  declare user: IUser | null;

  @Column({ type: "text" })
  declare message: string;

  @Column({ type: "text", nullable: true })
  declare comment: string;
}
