import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { PrItem as IPrItem } from "../model/model_pr_item.js";

@Entity("pr")
export class PrItem implements IPrItem {
  @PrimaryColumn({ type: "text" })
  declare id: string;

  @Column({ type: "text" })
  declare prnumber: string;

  @Column({ type: "text" })
  declare item: string;

  @Column({ type: "text" })
  declare material: string;

  @Column({ type: "text" })
  declare materialdesc: string;

  @Column({ type: "timestamp", nullable: true })
  declare syncAt: Date | null;
}
