import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryColumn } from "typeorm";
import { ApprovalGroup as IApprovalGroup } from "../model/model_approval.js";
import { Department as IDepartment } from "../model/model_department.js";
import { ProcPlanDetail as IProcPlanDetail, ProcPlanHeader as IProcPlanHeader } from "../model/model_procplan.js";
import { Section as ISection } from "../model/model_section.js";
import { User as IUser } from "../model/model_user.js";
import {
  Activity,
  AuthorizationForExpenditure,
  Commodity,
  Currency,
  DocumentStatus,
  PlanCreated,
  ProcPlan,
  TenderMethod,
  TypeOf,
  ValueCurrency,
  WorkProgramAndBudget,
  WorkProgramReference,
} from "../model/vo.js";
import { ApprovalGroup } from "./table_approval.js";
import { Department } from "./table_department.js";
import { Section } from "./table_section.js";

@Entity()
export class ProcPlanHeader implements IProcPlanHeader {
  //
  @PrimaryColumn({ type: "varchar", length: 25 })
  declare id: string;

  @Column({ type: "varchar", length: 3 })
  declare procPlanType: TypeOf<typeof ProcPlan>;

  @Column({ type: "varchar", length: 15 })
  declare status: TypeOf<typeof DocumentStatus>;

  @Column({ type: "boolean" })
  declare isSendBack: boolean;

  @Column({ type: "timestamp", nullable: true })
  declare submittedDate: Date | null;

  @Column({ type: "int" })
  declare year: number;

  @Column({ type: "int" })
  declare count: number;

  @Column({ type: "jsonb", nullable: true })
  declare totalValueEstimation: ValueCurrency[];

  @ManyToOne(() => Section)
  @JoinColumn({ name: "sectionId" })
  declare section: ISection | null;

  @ManyToOne(() => Department)
  @JoinColumn({ name: "departmentId" })
  declare department: IDepartment | null;

  @Column({ type: "jsonb", nullable: true })
  declare submitter: IUser | null;

  @Column({ type: "jsonb", nullable: true })
  declare sections: Section[];

  @OneToOne(() => ApprovalGroup)
  @JoinColumn({ name: "approvalGroupId" })
  declare approvalGroup: IApprovalGroup | null;

  @Column({ type: "varchar", length: 25, nullable: true })
  declare fromProcPlanHeaderId: string | null;
}

@Entity()
export class ProcPlanDetail implements IProcPlanDetail {
  //
  @PrimaryColumn({ type: "varchar", length: 25 })
  declare id: string;

  @Column({ type: "timestamp" })
  declare createdAt: Date;

  @Column({ type: "jsonb", nullable: true })
  declare creator: IUser | null;

  @Column({ type: "jsonb", nullable: true })
  declare requestFor: IUser | null;

  @Column({ type: "jsonb", nullable: true })
  declare requesterBackToBack: IUser[];

  @Column({ type: "int", nullable: true })
  declare year: number;

  @ManyToOne(() => Section)
  declare section: ISection | null;

  @Column({ type: "text", nullable: true })
  declare procPlanCode: string;

  @ManyToOne(() => Department)
  declare department: IDepartment | null;

  @ManyToOne(() => ProcPlanHeader)
  declare procPlanHeader: IProcPlanHeader | null;

  @Column({ type: "varchar", length: 15 })
  declare createdType: TypeOf<typeof PlanCreated>;

  @Column({ type: "varchar", length: 3 })
  declare procPlanType: TypeOf<typeof ProcPlan>;

  @Column({ type: "text", nullable: true })
  declare carryOverProcPlanId: string;

  @Column({ type: "text", nullable: true })
  declare carryOverProcPlanCode: string;

  @Column({ type: "varchar", length: 10 })
  declare commodity: TypeOf<typeof Commodity>;

  @Column({ type: "varchar", length: 10 })
  declare activityType: TypeOf<typeof Activity>;

  @Column({ type: "text" })
  declare title: string;

  @Column({ type: "text" })
  declare generalScopeOfWork: string;

  @Column({ type: "varchar", length: 25 })
  declare tenderMethod: TypeOf<typeof TenderMethod>;

  @Column({ type: "timestamp", nullable: true })
  declare requisitionSubmissionDate: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare tenderStartDate: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare contractDateStart: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare contractDateEnd: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare poDateIssuance: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare poDateDelivery: Date | null;

  @Column({ type: "varchar", length: 3 })
  declare currency: TypeOf<typeof Currency>;

  @Column({ type: "bigint", nullable: true })
  declare valueEstimation: number;

  @Column({ type: "bigint", nullable: true })
  declare valueInUSD: number;

  @Column({ type: "int" })
  declare localContentLevel: number;

  @Column({ type: "bigint", nullable: true })
  declare estCurrYearExpenditure: number;

  @Column({ type: "bigint", nullable: true })
  declare approvalAnnualBudget: number;

  @Column({ type: "jsonb" })
  declare workProgramReferences: TypeOf<typeof WorkProgramReference>[];

  @Column({ type: "jsonb" })
  declare workProgramAndBudget: WorkProgramAndBudget[];

  @Column({ type: "jsonb" })
  declare authorizationForExpenditure: AuthorizationForExpenditure[];

  @Column({ type: "jsonb" })
  declare technicalMoMSubjectAndDate: string[];

  @Column({ type: "text" })
  declare remarks: string;

  @Column({ type: "boolean" })
  declare masterList: boolean;

  @Column({ type: "jsonb", nullable: true })
  declare usedByRequisitionIds: string[];

  @Column({ type: "varchar", length: 20, nullable: true })
  declare durationType: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  declare durationValue: string;

  @Column({ type: "jsonb", nullable: true })
  declare errorFields: string[];

  //
}
