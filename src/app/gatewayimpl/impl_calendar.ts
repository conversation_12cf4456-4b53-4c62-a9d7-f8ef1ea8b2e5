import { DataSource, ILike, In } from "typeorm";
import { getManager } from "../../framework/gateway_typeorm.js";
import { Calendar } from "./table_calendar.js";
import { FindCalendar } from "../model/model_calendar.js";

export const implFindAllCalendar = (ds: DataSource): FindCalendar => {
  return async (ctx, filter) => {
    //
    let where: any | [] = {};

    if (filter?.type) where.type = filter.type;
    if (filter?.date) where.date = filter.date;
    if (filter?.year) where.year = filter.year;
    if (filter?.ids && filter.ids.length > 0) where.id = In(filter.ids);
    if (filter?.nameLike) where.name = ILike(`%${filter.nameLike}%`);

    const result = await getManager(ctx, ds)
      .getRepository(Calendar)
      .findAndCount({
        where,
        order: {
          year: "ASC",
        },
      });

    return result;
  };
};
