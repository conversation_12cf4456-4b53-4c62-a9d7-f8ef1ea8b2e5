// contextData: async (ctx: Context, fieldName: string) => {
//   return ctx.data[fieldName];
// },

import { generateID } from "../../framework/helper.js";
import { DateNowHandler, RandomStringHandler } from "../model/vo.js";

export const implDateNow: () => DateNowHandler = () => async (ctx, _) => new Date();

export const implRandomString: () => RandomStringHandler = () => async (ctx, _) => generateID(16);
