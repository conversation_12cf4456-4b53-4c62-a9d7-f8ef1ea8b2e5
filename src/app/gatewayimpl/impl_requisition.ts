import { DataSource, FindOptionsWhere, ILike, In, Raw } from "typeorm";
import { Requisition } from "./table_requisition.js";
import { FindRequisition, FindRequisitionByFromExcludeDepartment, FindRequisitionByRbtb, FindUserAssigned } from "../model/model_requisition.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { User } from "./table_user.js";
import { defaultFilterSize } from "./_gateway.js";
import { implFindAllUserNative } from "./impl_user.js";

export const implFindAllRequisition = (ds: DataSource): FindRequisition => {
  //
  return async (ctx, filter) => {
    //
    // let where: any | [] = {};
    let where: FindOptionsWhere<Requisition> | [] = {};

    if (filter?.id) where.id = filter.id;
    if (filter?.title) where.title = ILike(`%${filter.title}%`);
    if (filter?.departmentId) where.department = { id: filter.departmentId };
    if (filter?.departmentIds && filter.departmentIds.length > 0) where.department = { id: In(filter.departmentIds) };
    if (filter?.sectionId) where.section = { id: filter.sectionId };
    if (filter?.year) where.year = filter.year;
    if (filter?.status) where.status = filter.status;
    if (filter?.commodity) where.commodity = filter.commodity;
    if (filter?.tenderCode) where.tenderCode = ILike(`%${filter.tenderCode}%`);

    if (filter?.assigned) where.assignedUser = Raw((alias) => `${alias} @> :userFilter`, { userFilter: JSON.stringify({ id: filter.userId }) });
    if (filter?.assignedBTB) where.assignedBackToBack = Raw((alias) => `${alias} @> :userFilter`, { userFilter: JSON.stringify([{ id: filter.userId }]) });
    if (filter?.isAdditionalProcPlan) where.isAdditionalProcPlan = filter.isAdditionalProcPlan;
    if (filter?.procPlanDetailId) where.procPlanDetailId = filter.procPlanDetailId;
    

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter?.page < 1 ? 1 : filter?.page) || 1;

    const result = await getManager(ctx, ds)
      .getRepository(Requisition)
      .findAndCount({
        ...(filter.useSelect && {
          select: {
            id: true,
            tenderCode: true,
            year: true,
            title: true,
            status: true,
            contractDateStart: true,
            poDateIssuance: true,
            currency: true,
            value: true,
            localContentLevel: true,
            submittedDate: true,
            assignedUser: {},
            creator: {},
            requestFor: {},
            requesterBackToBack: {},
          },
        }),
        where,
        relations: {
          department: true,
          section: true,
          approvalGroup: true,
        },
        order: {
          submittedDate: "ASC",
        },
        take: size,
        skip: (page - 1) * size,
      });

    return result;
  };
};

export const implFindUserAssigned = (ds: DataSource): FindUserAssigned => {
  return async (ctx, filter) => {
    //
    const requisitionRepository = getManager(ctx, ds).getRepository(Requisition);

    const results = await Promise.all(
      filter.users.map(async (user) => {
        const count = await requisitionRepository //
          .createQueryBuilder("requisition")
          .where(` "assignedUser" @> :v `, { v: { id: user.id } })
          .getCount();

        return { user, count };
      })
    );

    return results;
  };
};

export const implFindRequisitionByRbtb = (ds: DataSource): FindRequisitionByRbtb => {
  return async (ctx, req) => {
    //
    const findRequisitionBtb = await getManager(ctx, ds)
      .getRepository(Requisition)
      .createQueryBuilder("requisition")
      .leftJoinAndSelect("requisition.section", "section")
      .leftJoinAndSelect("requisition.department", "department")
      .where(`"requesterBackToBack" @> :rbtb`, { rbtb: JSON.stringify([{ id: req.userId }]) })
      .andWhere(`EXTRACT(YEAR FROM "submittedDate") = :year`, { year: req.year });

    if (req.title) {
      findRequisitionBtb.andWhere("title ilike :title", { title: `%${req.title}%` });
    }

    if (req.tenderCode) {
      findRequisitionBtb.andWhere("requisition.tenderCode ilike :tenderCode", { tenderCode: `%${req.tenderCode}%` });
    }

    const [result, count] = await findRequisitionBtb.getManyAndCount();

    return [result, count];
  };
};

export const implFindRequisitionByFromExcludeDepartment = (ds: DataSource): FindRequisitionByFromExcludeDepartment => {
  //
  return async (ctx, req) => {
    //
    const findRequisition = await getManager(ctx, ds) //
      .getRepository(Requisition)
      .createQueryBuilder("requisition")
      .leftJoinAndSelect("requisition.section", "section")
      .leftJoinAndSelect("requisition.department", "department")
      .andWhere(`"creator" @> :creator`, { creator: JSON.stringify({ id: req.userId }) })
      .andWhere("year = :year", { year: req.year })
      .andWhere(`"requisition"."departmentId" <> :departmentId`, { departmentId: req.departmentId });

    if (req.title) {
      findRequisition.andWhere("title ilike :title", { title: `%${req.title}%` });
    }

    if (req.tenderCode) {
      findRequisition.andWhere("requisition.tenderCode ilike :tenderCode", { tenderCode: `%${req.tenderCode}%` });
    }

    const [result, count] = await findRequisition.getManyAndCount();

    return [result, count];
  };
};
