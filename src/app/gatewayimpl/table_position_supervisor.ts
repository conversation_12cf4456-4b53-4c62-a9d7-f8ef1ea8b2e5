import { Column, <PERSON>tity, PrimaryColumn } from "typeorm";
import { PositionSupervisor as IPositionSupervisor } from "../model/model_position_supervisor.js";

@Entity()
export class PositionSupervisor implements IPositionSupervisor {
  @PrimaryColumn({ type: "varchar", length: 100 })
  declare positionId: string;

  @Column({ type: "varchar", length: 100 })
  declare supervisorPositionId: string;
}
