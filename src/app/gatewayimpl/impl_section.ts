import { DataSource, ILike, In } from "typeorm";
import { FindSection } from "../model/model_section.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { Section } from "./table_section.js";

export const implFindAllSection = (ds: DataSource): FindSection => {
  return async (ctx, filter) => {
    //
    let where: any | [] = {};

    if (filter?.departmentId) where.department = { id: filter.departmentId };
    if (filter?.departmentIds && filter.departmentIds.length > 0) where.department = { id: In(filter.departmentIds) };
    if (filter?.ids && filter.ids.length > 0) where.id = In(filter.ids);
    if (filter?.nameLike) where.name = ILike(`%${filter.nameLike}%`);

    where = { ...where, isSection: true };

    const result = await getManager(ctx, ds)
      .getRepository(Section)
      .findAndCount({
        where,
        relations: { department: true },
        order: {
          department: {
            code: "ASC",
          },
        },
      });

    return result;
  };
};
