import { Column, <PERSON>ti<PERSON>, PrimaryColumn } from "typeorm";
import { Approval as IApproval, ApprovalGroup as IApprovalGroup } from "../model/model_approval.js";
import { ApprovalStatus, DocumentTemplate, TypeOf } from "../model/vo.js";

@Entity()
export class ApprovalGroup implements IApprovalGroup {
  //
  @PrimaryColumn({ type: "varchar", length: 25 })
  declare id: string;

  @Column({ type: "varchar", length: 25 })
  declare documentId: string;

  @Column({ type: "varchar", length: 25 })
  declare documentType: TypeOf<typeof DocumentTemplate>;

  @Column({ type: "int" })
  declare sequence: number;

  @Column({ type: "varchar", length: 25, nullable: true })
  declare nextApprovalGroupId: string | null;

  @Column({ type: "text", nullable: true })
  declare status: TypeOf<typeof ApprovalStatus>;

  @Column({ type: "text", nullable: true })
  declare description?: string;

  @Column({ type: "jsonb" })
  declare approvals: IApproval[];

  @Column({ type: "int", default: 0 })
  declare durationDays: number;

  @Column({ type: "int", nullable: true })
  declare documentYear: number;
}
