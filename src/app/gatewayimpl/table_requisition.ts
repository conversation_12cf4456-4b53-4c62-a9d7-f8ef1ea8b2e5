import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryColumn } from "typeorm";
import { ApprovalGroup as IApprovalGroup } from "../model/model_approval.js";
import { Department as IDepartment } from "../model/model_department.js";
import { Section as ISection } from "../model/model_section.js";
import {
  BudgetOwnerDetail,
  BudgetOwnerPSC,
  HSERiskCategory,
  Requisition as IRequisition,
  IdName,
  InsuranceRiskCategory,
  LegalInsurance,
  OEReference,
  PotentialDamage,
  SupportingDocuments,
  TenderBidder,
  TitleContent,
  UserInsurance,
} from "../model/model_requisition.js";
import { User as IUser } from "../model/model_user.js";
import { Commodity, Currency, DocumentStatus, Files, Incoterms, TenderMethod, TypeOf, ContractTypePayment, ContractTypeEngagement } from "../model/vo.js";
import { ApprovalGroup } from "./table_approval.js";
import { Department } from "./table_department.js";
import { User } from "./table_user.js";
import { PrItem } from "../model/model_pr_item.js";
import { Section } from "./table_section.js";

@Entity()
export class Requisition implements IRequisition {
  //
  @PrimaryColumn({ type: "varchar", length: 25 })
  declare id: string;

  @Column({ type: "boolean", nullable: true })
  declare isAdditionalProcPlan: boolean;

  @Column({ type: "jsonb", nullable: true })
  declare additionalProcPlanExplanation: string[];

  @Column({ type: "jsonb", nullable: true })
  declare budgetOwners: string[];

  @Column({ type: "jsonb", nullable: true })
  declare budgetOwnerDetails: BudgetOwnerDetail[] | null;

  @Column({ type: "jsonb", nullable: true })
  declare budgetOwnerPSC: BudgetOwnerPSC | null;

  @Column({ type: "varchar", length: 15 })
  declare status: TypeOf<typeof DocumentStatus>;

  @Column({ type: "int", nullable: true })
  declare year: number;

  @OneToOne(() => ApprovalGroup)
  @JoinColumn({ name: "approvalGroupId" })
  declare approvalGroup: IApprovalGroup | null;

  @Column({ type: "timestamp", nullable: true })
  declare submittedDate: Date | null;

  @Column({ type: "boolean", nullable: true })
  declare isSendBack: boolean;

  @Column({ type: "jsonb", nullable: true })
  declare submitter: User | null;

  @Column({ type: "jsonb", nullable: true })
  declare creator: IUser | null;

  @Column({ type: "jsonb", nullable: true })
  declare requestFor: IUser | null;

  @Column({ type: "jsonb", nullable: true })
  declare requesterBackToBack: IUser[];

  @Column({ type: "jsonb", nullable: true })
  declare assignedUser: User | null;

  @Column({ type: "jsonb", nullable: true })
  declare assignedBackToBack: User[];

  @Column({ type: "jsonb", nullable: true })
  declare membersInvolved: IUser[];

  @ManyToOne(() => Department)
  declare department: IDepartment | null;

  @ManyToOne(() => Section)
  declare section: ISection | null;

  @Column({ type: "varchar", length: 25, nullable: true })
  declare procPlanDetailId: string;

  @Column({ type: "varchar", length: 25, nullable: true })
  declare procPlanDetailCode: string;

  @Column({ type: "jsonb", nullable: true })
  declare procPlanDetails: IRequisition["procPlanDetails"];

  @Column({ type: "varchar", length: 20, nullable: true })
  declare tenderCode: string;

  // =================================================================

  @Column({ type: "text" })
  declare title: string;

  @Column({ type: "text" })
  declare generalScopeOfWork: string;

  @Column({ type: "text" })
  declare justificationOfRequiredWorks: string;

  @Column({ type: "varchar", length: 10 })
  declare commodity: TypeOf<typeof Commodity>;

  @Column({ type: "varchar", length: 25 })
  declare tenderMethod: TypeOf<typeof TenderMethod>;

  @Column({ type: "varchar", length: 150, nullable: true })
  declare contractTypePayment: TypeOf<typeof ContractTypePayment>;

  @Column({ type: "varchar", length: 150, nullable: true })
  declare contractTypeEngagement: TypeOf<typeof ContractTypeEngagement>;

  @Column({ type: "varchar", length: 5 })
  declare incoterms: TypeOf<typeof Incoterms>;

  @Column({ type: "boolean", nullable: true })
  declare importation: boolean;

  @Column({ type: "int" })
  declare localContentLevel: number;

  @Column({ type: "jsonb", nullable: true })
  declare workOfLocation: string[];

  @Column({ type: "timestamp", nullable: true })
  declare contractDateStart: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare contractDateEnd: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare poDateIssuance: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare poDateDelivery: Date | null;

  @Column({ type: "varchar", length: 20, nullable: true })
  declare durationType: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  declare durationValue: string;

  @Column({ type: "jsonb", nullable: true })
  declare purchaseRequisition: PrItem[];

  // ================================================================

  @Column({ type: "varchar", length: 5 })
  declare currency: TypeOf<typeof Currency>;

  @Column({ type: "bigint", nullable: true })
  declare value: number;

  @Column({ type: "varchar", length: 5, nullable: true })
  declare procPlanCurrency: TypeOf<typeof Currency>;

  @Column({ type: "bigint", nullable: true })
  declare procPlanValue: number;

  @Column({ type: "jsonb", nullable: true })
  declare oeReference: OEReference | null;

  // @Column({ type: "jsonb" })
  // declare reference: string[];

  // @Column({ type: "text" })
  // declare ownerEstimationExplanation: string;

  // @Column({ type: "jsonb", nullable: true })
  // declare breakdownFiles: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare referenceFiles: Files | null;

  // =================================================================

  @Column({ type: "text", nullable: true })
  declare typeWork: string;

  @Column({ type: "text", nullable: true })
  declare hseAssessment: string;

  @Column({ type: "jsonb", nullable: true })
  declare hseRiskCategory: HSERiskCategory[];

  // =================================================================

  @Column({ type: "jsonb", nullable: true })
  declare userInsurance: UserInsurance | null;

  @Column({ type: "jsonb", nullable: true })
  declare legalInsurance: LegalInsurance | null;

  @Column({ type: "jsonb", nullable: true })
  declare tenderBidder: TenderBidder | null;

  @Column({ type: "jsonb", nullable: true })
  declare supportingDocuments: SupportingDocuments | null;

  // @Column({ type: "boolean", nullable: true })
  // declare isUseInsurance: boolean;

  // @Column({ type: "jsonb", nullable: true })
  // declare insuranceRiskType: InsuranceRiskCategory[];

  // @Column({ type: "text", nullable: true })
  // declare previousSimilarContract: string;

  // @Column({ type: "jsonb", nullable: true })
  // declare potentialDamage: PotentialDamage[];

  // @Column({ type: "text", nullable: true })
  // declare typeLimitInsurance: string;

  // @Column({ type: "text", nullable: true })
  // declare typeInsuranceNote: string;

  // =================================================================

  // @Column({ type: "boolean", nullable: true })
  // declare isDirectAppointment: boolean;

  // @Column({ type: "jsonb", nullable: true })
  // declare daChecklist: string[];

  // @Column({ type: "jsonb", nullable: true })
  // declare daPotentialVendorCompanies: IdName | null;

  // =================================================================

  // @Column({ type: "jsonb", nullable: true })
  // declare daJustifications: TitleContent[];

  // @Column({ type: "text" })
  // declare daExplanation: string;

  // @Column({ type: "text" })
  // declare daPotentialVendorJustification: string;

  // @Column({ type: "jsonb", nullable: true })
  // declare daPotentialVendorCompany: IdName | null;

  // @Column({ type: "boolean", nullable: true })
  // declare daIsCertainWork: boolean;

  // @Column({ type: "jsonb", nullable: true })
  // declare daSupportingDocumentsFiles: Files | null;

  // =================================================================

  // @Column({ type: "timestamp", nullable: true })
  // declare tmwsmDateAt: Date | null;

  // @Column({ type: "text", nullable: true })
  // declare tmwsmTitle: string;

  // @Column({ type: "jsonb", nullable: true })
  // declare tmwsmFiles: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare tmweFiles: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare sowFiles: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare aprFiles: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare tecFiles: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare sorFiles: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare slaKpiFiles: Files | null;

  // @Column({ type: "boolean", nullable: true })
  // declare isBigValue: boolean;
}
