import { Column, <PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { Position as IPosition } from "../model/model_position.js";

@Entity()
export class Position implements IPosition {
  //
  @PrimaryColumn({ type: "varchar", length: 20 })
  declare id: string;

  @Column({ type: "text" })
  declare name: string;

  @Column({ type: "varchar", length: 30, nullable: true })
  declare role: string;
}
