import { <PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { C<PERSON>VDSpda, CIVDVendorBusinessClass, CIVDVendorBusinessLicense, CIVDVendorSaction, CIVDVendor as ICIVDVendor } from "../model/model_civd_vendor.js";

@Entity()
export class CIVDVendor implements ICIVDVendor {
  @PrimaryColumn({ type: "int" })
  declare civdVendorId: number;

  @Column({ type: "varchar", length: 25, nullable: true })
  declare npwp: string;

  @Column({ type: "varchar", length: 100 })
  declare email: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  declare emailAlternative: string;

  @Column({ type: "text", nullable: true })
  declare address: string;

  @Column({ type: "text", nullable: true })
  declare addressAlternative: string;

  @Column({ type: "varchar", length: 200, nullable: true })
  declare provName: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  declare phone: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  declare contactPerson: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  declare contactPersonPosition: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  declare presidentDirectorName: string;

  @Column({ type: "jsonb", nullable: true })
  declare businessLicense: CIVDVendorBusinessLicense[];

  @Column({ type: "jsonb", nullable: true })
  declare sanction: CIVDVendorSaction[];

  @Column({ type: "jsonb", nullable: true })
  declare spda: CIVDSpda[];

  @Column({ type: "jsonb", nullable: true })
  declare businessClass: CIVDVendorBusinessClass[];

  @Column({ type: "varchar", length: 100, nullable: true })
  declare companyType: string;

  @Column({ type: "varchar", length: 100 })
  declare name: string;

  @Column({ type: "varchar", length: 50, nullable: true })
  declare location: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  declare accessApps: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  declare activeStatus: string | null;
}
