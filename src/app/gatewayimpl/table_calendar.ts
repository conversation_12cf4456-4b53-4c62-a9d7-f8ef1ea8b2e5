import { Column, <PERSON>tity, PrimaryColumn } from "typeorm";
import { Calendar as ICalendar } from "../model/model_calendar.js";

@Entity()
export class Calendar implements ICalendar {
  @PrimaryColumn({ type: "varchar", length: 20 })
  declare id: string;

  @Column({ type: "varchar", length: 100 })
  declare name: string;

  @Column({ type: "text" })
  declare description: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  declare type: string;

  @Column({ type: "timestamp", nullable: true })
  declare date: Date | null;

  @Column({ type: "int" })
  declare year: number;
}
