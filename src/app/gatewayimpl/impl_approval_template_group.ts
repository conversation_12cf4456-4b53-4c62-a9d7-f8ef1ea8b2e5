import { DataSource } from "typeorm";
import { FindApprovalTemplateGroup, ApprovalTemplateGroup as IApprovalTemplateGroup } from "../model/model_approval_template.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { ApprovalTemplateGroup } from "./table_approval_template.js";
import { createContext } from "../../framework/core.js";
import { Commodity, CommodityService, DocumentTemplate, SubDocumentRequisition, TypeOf, UserRole, ValueRange } from "../model/vo.js";

export const implFindAllApprovalTemplateGroup = (ds: DataSource): FindApprovalTemplateGroup => {
  //
  return async (ctx, filter) => {
    //

    const result = await getManager(ctx, ds)
      .getRepository(ApprovalTemplateGroup)
      .findAndCount({
        where: { documentType: filter.documentType },
        order: { sequence: "ASC" },
      });

    return result;
  };
};

export const InitDefaultApprovalTemplateGroups = async (ds: DataSource) => {
  //
  const ctx = createContext();

  const [atgs] = await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).findAndCount();
  if (atgs.length > 0) {
    return;
  }

  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PROC_PLAN_UPP";
    const objs = [
      //
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole> }] },
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole> }] },
      { documentType, approvalTemplates: [{ users: [{ id: "80181303" }], as: "Planning, Bidding & Reporting", rule: { hasNoApprover: true } }] }, // <EMAIL>
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }

  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PROC_PLAN_APP";
    const objs = [
      //
      { documentType, approvalTemplates: [{ users: [{ id: "********" }], as: "Vendor Management" }] }, // VM <EMAIL>
      { documentType, approvalTemplates: [{ users: [{ id: "********" }], as: "Local Content Management" }] }, // LCL <EMAIL>
      { documentType, approvalTemplates: [{ users: [{ id: "89228822" }], as: "Planning, Bidding & Reporting" }] }, // PBR <EMAIL>
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole> }] },
      { documentType, approvalTemplates: [{ role: "SMVP" as TypeOf<typeof UserRole> }] },
      { documentType, approvalTemplates: [{ role: "GM" as TypeOf<typeof UserRole> }] },
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }

  {
    const documentType: TypeOf<typeof DocumentTemplate> = "REQUISITION";
    const objs: IApprovalTemplateGroup[] = [
      //
      {
        documentType,
        approvalTemplates: [
          //
          {
            role: "STAFF" as TypeOf<typeof UserRole>, //
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>, //
            rule: { isRequestFor: true },
          },
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [
              { id: "********" }, // <EMAIL> - ******** (Primary)
              { id: "********" }, // <EMAIL> - ********
            ],
            as: "Staff Budget Reporting",
            subDocumentType: "OWNER_ESTIMATION" as TypeOf<typeof SubDocumentRequisition>, //
            rule: {
              budgetOwners: [
                "70060000", // Health, Safety, Security & Environment (HSSE)
                "70260000", // Maintenance
                "70050000", // Project MDA-MBH-MDK
                "70160000", // Project MAC-NFD
                "70140000", // Drilling & Completion (Drilling, Completion & Well Integrity)
                "70020000", // Logistic (Supply Chain Management)
                "70120000", // Internal Audit & Compliance
                "70130000", // Subsurface
                // "71000000", "76000000", "75000000", "74000000", "70000000", "73000000", "72000000" // Executive,
              ],
            },
          },
          {
            users: [
              { id: "********" }, // <EMAIL> - ******** (Primary)
              { id: "********" }, // <EMAIL> - ********
            ],
            as: "Staff Budget Reporting",
            subDocumentType: "OWNER_ESTIMATION" as TypeOf<typeof SubDocumentRequisition>, //
            rule: {
              budgetOwners: [
                "********", // Engineering & Constructions (Engineering & Asset Integrity)
                "********", // Production MDA-MBH-MDK-MAC
                "********", // Production BD
                "********", // Finance & Accounting (Finance, Accounting & Tax)
                "********", // Legal
                "********", // Commercial & Planning
                "********", // Business Process & Technology
                "********", // HR & GA,
                "********", // Relation & ROR, // Regional Office & Relations
                "********", // Budget & Reporting,
                "********", // Strategic Planning & Performance,
                "********", // Marketing
              ],
            },
          },
        ],
      },
      {
        documentType,
        approvalTemplates: [
          {
            users: [{ id: "********" }], // <EMAIL>
            as: "Vendor Management",
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { isAdditionalProcPlan: true },
          },
          {
            users: [{ id: "********" }], // <EMAIL>
            as: "Local Content",
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { isCommodityGoodsAndFirmCommitment: true, isValueGreaterThanMinRequisitionValue: true }, // or
          },
          {
            users: [{ id: "80151174" }], // <EMAIL>
            as: "Formalities",
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            // rule: { commodity: "GOODS" as TypeOf<typeof Commodity> },
          },
          {
            users: [
              { id: "80221385" }, // <EMAIL>
              { id: "80161215" }, // <EMAIL>
            ],
            as: "Staff HSSE Risk",
            subDocumentType: "HSE_RISK_ASSESSMENT" as TypeOf<typeof SubDocumentRequisition>, //
          },
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80151189" }],
            as: "Specialist Insurance",
            subDocumentType: "INSURANCE_ASSESSMENT" as TypeOf<typeof SubDocumentRequisition>,
            // rule: { isUseInsurance: true },
          }, // SULUNG ANGGORO
        ],
      }, // <EMAIL>
      {
        documentType,
        approvalTemplates: [
          //
          {
            role: "HEAD" as TypeOf<typeof UserRole>,
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
          },
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80151164" }],
            as: "Sr. Head Budget Reporting",
            subDocumentType: "OWNER_ESTIMATION" as TypeOf<typeof SubDocumentRequisition>,
          }, // FATHULLAH MAHBUB
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80181303" }],
            as: "Head PBR",
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { isAdditionalProcPlan: true, isAdditionalProcPlanOrValueMoreThanPercentage: true },
          }, // OKY ELDYAGUSTA
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            role: "MGR" as TypeOf<typeof UserRole>,
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
          },
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80181316" }],
            as: "Manager Budget Reporting",
            subDocumentType: "OWNER_ESTIMATION" as TypeOf<typeof SubDocumentRequisition>,
          }, // WIRA KUSUMA
        ],
      }, // <EMAIL>
      {
        documentType,
        approvalTemplates: [
          //
          { users: [{ id: "80151186" }], as: "Manager HSSE Risk", subDocumentType: "HSE_RISK_ASSESSMENT" as TypeOf<typeof SubDocumentRequisition> }, // MHSE MUSLIMIN
        ],
      }, // <EMAIL>

      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80141111" }],
            as: "Manager Legal",
            subDocumentType: "INSURANCE_ASSESSMENT" as TypeOf<typeof SubDocumentRequisition>,
            // rule: { isUseInsurance: true }
          }, // ADISTI RACHMA
        ],
      }, // <EMAIL>
      {
        documentType,
        approvalTemplates: [
          //
          {
            role: "SMVP" as TypeOf<typeof UserRole>,
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { isAdditionalProcPlanOrValueMoreThanPercentage: true, isValueGreaterThanMinOeValue: true, isDaJustification: true }, // or
          },
          // {
          //   role: "SMVP" as TypeOf<typeof UserRole>,
          //   subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
          //   rule: { oeValueRange: "GREATER_THAN" as TypeOf<typeof ValueRange> },
          // },
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80131088" }], // GOODS <EMAIL>
            as: "Head Procurement",
            subDocumentType: "DA_JUSTIFICATION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { commodity: "GOODS" as TypeOf<typeof Commodity>, isDaJustification: true },
          },
          {
            users: [{ id: "80181326" }], // OPS <EMAIL>
            as: "Head Procurement",
            subDocumentType: "DA_JUSTIFICATION" as TypeOf<typeof SubDocumentRequisition>,
            rule: {
              commodity: "SERVICES" as TypeOf<typeof Commodity>,
              commodityServiceType: "OPS" as TypeOf<typeof CommodityService>,
              isDaJustification: true,
            },
          },
          {
            users: [{ id: "80121004" }], // NON_OPS <EMAIL>
            as: "Head Procurement",
            subDocumentType: "DA_JUSTIFICATION" as TypeOf<typeof SubDocumentRequisition>,
            rule: {
              commodity: "SERVICES" as TypeOf<typeof Commodity>,
              commodityServiceType: "NON_OPS" as TypeOf<typeof CommodityService>,
              isDaJustification: true,
            },
          },
        ],
      },
      //================================================================
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80151154" }],
            as: "Manager Procurement",
            subDocumentType: "DA_JUSTIFICATION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { isDaJustification: true },
          }, // MPRO <EMAIL>
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80121049" }],
            as: "Supply Chain Management",
            subDocumentType: "DA_JUSTIFICATION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { isValueGreaterThanMaxDaValue: true, isDaJustification: true },
          }, // SCM <EMAIL>
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            role: "GM" as TypeOf<typeof UserRole>,
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { isAdditionalProcPlanOrValueMoreThanPercentage: true, isValueGreaterThanMaxOeValue: true, isDaJustification: true }, // or
          },
        ],
      },
      {
        documentType,
        approvalTemplates: [
          //
          {
            users: [{ id: "80131088" }], // GOODS <EMAIL>
            as: "Head Procurement",
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { commodity: "GOODS" as TypeOf<typeof Commodity> },
          },
          {
            users: [{ id: "80181326" }], // OPS <EMAIL>
            as: "Head Procurement",
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { commodity: "SERVICES" as TypeOf<typeof Commodity>, commodityServiceType: "OPS" as TypeOf<typeof CommodityService> },
          },
          {
            users: [{ id: "80121004" }], // NON_OPS <EMAIL>
            as: "Head Procurement",
            subDocumentType: "REQUISITION" as TypeOf<typeof SubDocumentRequisition>,
            rule: { commodity: "SERVICES" as TypeOf<typeof Commodity>, commodityServiceType: "NON_OPS" as TypeOf<typeof CommodityService> },
          },
        ],
      },
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }

  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PQ_REQUIREMENT";
    const objs = [
      //
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole> }] }, // PIC
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole> }] }, // HEAD PIC
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // HEAD REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole> }] }, // PROCUREMENT MANAGER
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }
  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PQ_REGISTRATION";
    const objs = [
      //
      { documentType, approvalTemplates: [{ users: [{ id: "89228822" }], as: "Staff PBR (Bidding Officer)" }] }, // <NAME_EMAIL>
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }
  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PQ_EVALUATION_1";
    const objs = [
      //
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole> }] }, // PIC
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole> }] }, // HEAD PIC
      // { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // HEAD REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole> }] }, // PROCUREMENT MANAGER
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }
  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PQ_EVALUATION_2";
    const objs = [
      //
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole> }] }, // PIC
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole> }] }, // HEAD PIC
      // { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // HEAD REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole> }] }, // PROCUREMENT MANAGER
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }
  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PQ_EVALUATION_3";
    const objs = [
      //
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole> }] }, // PIC
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole> }] }, // HEAD PIC
      // { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // HEAD REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "MGR" as TypeOf<typeof UserRole> }] }, // PROCUREMENT MANAGER
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }
  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PQ_CLARIFICATION";
    const objs = [
      //
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole> }] }, // PIC
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // HEAD REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole> }] }, // HEAD PIC
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }
  {
    const documentType: TypeOf<typeof DocumentTemplate> = "PQ_FINAL_RESULT";
    const objs = [
      //
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole> }] }, // PIC
      { documentType, approvalTemplates: [{ role: "STAFF" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole>, rule: { isRequestFor: true } }] }, // HEAD REQUISITION REQUESTFOR
      { documentType, approvalTemplates: [{ role: "HEAD" as TypeOf<typeof UserRole> }] }, // HEAD PIC
    ].map((x, i) => ({
      ...x,
      id: `${documentType}_${(i + 1).toString().padStart(2, "0")}`,
      sequence: i + 1,
    }));

    await getManager(ctx, ds).getRepository(ApprovalTemplateGroup).save(objs);
  }
};
