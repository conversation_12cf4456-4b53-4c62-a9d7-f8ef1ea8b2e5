import { Column, Entity, PrimaryColumn } from "typeorm";
import { Delegation as IDelegation } from "../model/model_delegation.js";

@Entity()
export class Delegation implements IDelegation {
  @PrimaryColumn({ type: "varchar", length: 20 })
  declare id: string;

  @Column({ type: "varchar", length: 100 })
  declare delegateToUserId: string;

  @Column({ type: "varchar", length: 255 })
  declare delegatorPositionName: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  declare delegatorEmail: string | null;

  @Column({ type: "varchar", length: 255 })
  declare delegatorName: string;

  @Column({ type: "varchar", length: 255 })
  declare delegateToName: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  declare delegateToEmail: string | null;

  @Column({ type: "varchar", length: 255, nullable: true })
  declare delegateToUserPositionId: string | null;

  @Column({ type: "varchar", length: 255, nullable: true })
  declare delegateToUserPositionName: string | null;

  @Column({ type: "varchar" })
  declare type: "DELEGATION" | "ACTING";

  @Column({ type: "text", nullable: true })
  declare remarks: string;

  @Column({ type: "timestamp", nullable: true })
  declare startDate: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare endDate: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare syncAt: Date | null;
}
