import { DataSource, In, Not } from "typeorm";
import { FindDepartment } from "../model/model_department.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { Department } from "./table_department.js";
import { defaultFilterSize } from "./_gateway.js";

export const implFindAllDepartment = (ds: DataSource): FindDepartment => {
  //
  return async (ctx, filter) => {
    //
    let where: any | [] = {};

    where.code = Not(""); // always exclude executive

    if (filter?.ids && filter?.ids.length > 0) where.id = In(filter.ids);
    if (filter?.code) where.code = filter.code;

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter?.page < 1 ? 1 : filter?.page) || 1;

    const result = await getManager(ctx, ds) //
      .getRepository(Department)
      .findAndCount({
        where,
        take: size,
        skip: (page - 1) * size,
        order: {
          code: "ASC",
        },
      });

    return result;
  };
};
