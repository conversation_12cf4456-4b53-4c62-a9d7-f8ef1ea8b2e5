import { DataSource } from "typeorm";
import { getManager } from "../../framework/gateway_typeorm.js";
import { FindPrItem } from "../model/model_pr_item.js";
import { defaultFilterSize } from "./_gateway.js";

export const implFindAllPrItem = (ds: DataSource): FindPrItem => {
  //

  return async (ctx, filter) => {
    //

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;
    const offset = (page - 1) * size;

    let where = "";
    let params: any[] = [];

    // Build the where clause based on filter
    if (filter.prnumber) {
      where = "WHERE prnumber ILIKE $1";
      params.push(`%${filter.prnumber}%`);
    }

    // Add pagination parameters
    const queryParams = [...params, size, offset];

    const data = await getManager(ctx, ds).query(
      `SELECT * FROM pr ${where} ORDER BY id DESC LIMIT $${queryParams.length - 1} OFFSET $${queryParams.length};`,
      queryParams
    );
    const count = await getManager(ctx, ds).query(`SELECT COUNT(*) as total FROM pr ${where};`, params);
    const total = parseInt(count[0].total);

    return [data, total];
  };
};
