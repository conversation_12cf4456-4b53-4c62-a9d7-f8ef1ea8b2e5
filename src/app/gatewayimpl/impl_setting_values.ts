import { DataSource } from "typeorm";
import { FindSettingValues } from "../model/model_setting_values.js";
import { SettingValues } from "./table_setting_values.js";
import { getManager } from "../../framework/gateway_typeorm.js";

export const implFindSettingValues = (ds: DataSource): FindSettingValues => {
  //

  return async (ctx) => {
    //
    const result = await getManager(ctx, ds) //
      .getRepository(SettingValues)
      .findAndCount();

    return result;
  };
};
