import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from "typeorm";
import { Department as IDepartment } from "../model/model_department.js";
import { Position as IPosition } from "../model/model_position.js";
import { Section as ISection } from "../model/model_section.js";
import { User as IUser } from "../model/model_user.js";
import { Department } from "./table_department.js";
import { Position } from "./table_position.js";
import { Section } from "./table_section.js";

@Entity()
export class User implements IUser {
  @PrimaryColumn({ type: "varchar", length: 20 })
  declare id: string;

  @Column({ type: "text" })
  declare name: string;

  @Column({ type: "text" })
  declare email: string;

  @ManyToOne(() => Position)
  declare position: IPosition | null;

  @ManyToOne(() => Section)
  declare section: ISection | null;

  @ManyToOne(() => Department)
  declare department: IDepartment | null;

  @Column({ type: "varchar", length: 20 })
  declare supervisorPositionId: string;
}
