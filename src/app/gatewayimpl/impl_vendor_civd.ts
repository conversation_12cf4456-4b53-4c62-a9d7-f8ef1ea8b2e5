import { DataSource } from "typeorm";
import { getManager } from "../../framework/gateway_typeorm.js";
import { FindOneVendorCIVD, FindVendorCIVD } from "../model/model_civd_vendor.js";
import { defaultFilterSize } from "./_gateway.js";
import { CIVDVendor } from "./table_civd_vendor.js";
import { Vendor } from "./table_vendor.js";

export const implFindAllVendorCivd = (ds: DataSource): FindVendorCIVD => {
  return async (ctx, filter) => {
    //
    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter.page < 1 ? 1 : filter.page) || 1;

    let query = getManager(ctx, ds).getRepository(CIVDVendor).createQueryBuilder("civd_vendor");

    if (filter?.civdIds && filter.civdIds.length > 0) query = query.where(`civd_vendor."civdVendorId" IN (:...civdIds)`, { civdIds: filter.civdIds });
    if (filter?.email) query = query.where(`civd_vendor.email = :email`, { email: filter.email });
    if (filter?.npwp) query = query.where(`civd_vendor.npwp = :npwp`, { npwp: filter.npwp });
    if (filter?.nameLike) query = query.andWhere(`civd_vendor."name" ILIKE :nameLike`, { nameLike: `%${filter.nameLike}%` });
    if ((filter?.spdaValid ?? "").length > 0) {
      if (filter?.spdaValid === "Valid") query = query.andWhere(`"spda" @> '[{"validity": "Valid"}]'`);
      else query = query.andWhere(`NOT ("spda" @> '[{"validity": "Valid"}]')`);
    }

    query = query
      .take(size)
      .skip((page - 1) * size)
      .orderBy("civd_vendor.name", "ASC");

    const result = await query.getManyAndCount();

    return result;
  };
};

export const implFindOneVendorCivd = (ds: DataSource): FindOneVendorCIVD => {
  return async (ctx, civdVendorId) => {
    const result = await getManager(ctx, ds)
      .getRepository(CIVDVendor)
      .findOne({
        where: {
          civdVendorId: civdVendorId,
        },
      });

    return result;
  };
};

export const InitDefaultVendorCivdAccessApps = async (ds: DataSource) => {
  await ds.createQueryBuilder().update(Vendor).set({ accessApps: "procurement,other_app" }).whereInIds(["0000000000", "0001000292", "0001000562"]).execute();
};
