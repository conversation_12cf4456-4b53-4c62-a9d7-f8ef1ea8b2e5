import { Column, <PERSON>tity, PrimaryColumn } from "typeorm";
import { Vend<PERSON> as IVendor } from "../model/model_vendor.js";

@Entity()
export class Vendor implements IVendor {
  @PrimaryColumn({ type: "varchar", length: 20 })
  declare id: string;

  @Column({ type: "varchar", length: 100 })
  declare name: string;

  @Column({ type: "varchar", length: 50 })
  declare location: string;
  
  @Column({ type: "varchar", length: 255, nullable: true })
  declare accessApps: string;
}
