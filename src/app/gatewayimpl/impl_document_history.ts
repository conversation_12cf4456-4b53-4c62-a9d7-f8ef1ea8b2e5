import { Between, DataSource, FindOptionsWhere } from "typeorm";
import { getManager } from "../../framework/gateway_typeorm.js";
import { FindDocumentHistory } from "../model/model_document_history.js";
import { DocumentHistory } from "./table_document_history.js";

export const implFindAllDocumentHistory = (ds: DataSource): FindDocumentHistory => {
  //
  return async (ctx, filter) => {
    //
    let where: FindOptionsWhere<DocumentHistory> = {};

    if (filter?.documentId) where.documentId = filter.documentId;
    if (filter?.documentType) where.documentType = filter.documentType; 

    // Check if filter.year exists and is a valid year
    if (filter?.year && Number.isInteger(filter.year) && filter.year > 0) {
      // Create a date range for the entire year
      const startDate = new Date(filter.year, 0, 1); // January 1st of the year
      const endDate = new Date(filter.year, 11, 31, 23, 59, 59); // December 31st of the year
      where.date = Between(startDate, endDate); // Use TypeORM's Between operator
    }

    const result = await getManager(ctx, ds)
      .getRepository(DocumentHistory)
      .findAndCount({
        where,
        order: { date: "ASC" },
      });

    return result;
  };
};
