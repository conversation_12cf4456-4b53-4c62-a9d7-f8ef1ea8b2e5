import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, In, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>qua<PERSON>, Not } from "typeorm";
import { FindUser, FindUserSupervisors, FindUserSupervisorsRoleDepartment, User as IUser } from "../model/model_user.js";
import { getManager } from "../../framework/gateway_typeorm.js";
import { User } from "./table_user.js";
import { defaultFilterSize } from "./_gateway.js";
import { Delegation } from "./table_delegation.js";
import { getDateOnly } from "../utility/helper.js";
import { Position } from "./table_position.js";
import { Context } from "../../framework/core.js";
import { PositionSupervisor } from "./table_position_supervisor.js";

export const implFindAllUser = (ds: DataSource): FindUser => {
  //
  return async (ctx, filter) => {
    //

    let where: any = {};

    if (filter?.nameLike) where.name = ILike(`%${filter.nameLike}%`);
    if (filter?.departmentId) where.department = { id: filter.departmentId };
    if (filter?.email) where.email = { email: filter.email };

    if (filter?.supervisorPositionId) where.supervisorPositionId = filter.supervisorPositionId;
    if (filter?.role) where.position = { role: filter.role };
    if (filter?.includeExecutive === false) where.department = { code: Not("") };

    if (filter?.ids && filter.ids.length > 0) where.id = In(filter.ids);
    if (filter?.emails && filter.emails.length > 0) where.email = In(filter.emails);
    if (filter?.positionIds && filter.positionIds.length > 0) where.position = { id: In(filter.positionIds) };

    if (filter?.sectionId && filter?.onlyIsSectionUser) {
      where.section = { id: filter.sectionId, isSection: true };
    } else {
      if (filter?.sectionId) where.section = { id: filter.sectionId };
      if (filter?.onlyIsSectionUser === true) where.section = { isSection: true };
    }

    const size = filter?.size || defaultFilterSize;
    const page = (filter?.page && filter?.page < 1 ? 1 : filter?.page) || 1;

    const result = await getManager(ctx, ds)
      .getRepository(User)
      .findAndCount({
        where,
        relations: {
          department: true,
          section: true,
          position: true,
        },
        take: size,
        skip: (page - 1) * size,
        order: {
          name: "ASC",
        },
      });

    return result;
  };
};

export const implFindAllUserNative = (ds: DataSource): FindUser => {
  //
  return async (ctx, filter) => {
    //

    let query = `
      SELECT 
        u.*, 
        d.name as "departmentName", 
        d.code as "departmentCode",
        s.name as "sectionName", 
        s."isSection" as "isSection",
        p.name as "positionName",
        p.role as "positionRole", 
        "supervisor".id as "supervisorId"
      FROM "user" u
      LEFT JOIN "department" d ON u."departmentId" = d.id
      LEFT JOIN "section" s ON u."sectionId" = s.id
      LEFT JOIN "position" p ON u."positionId" = p.id
      LEFT JOIN "user" "supervisor" ON supervisor."positionId" = u."supervisorPositionId"
      LEFT JOIN "position" "supervisorPosition" ON "supervisorPosition".id = "supervisor"."positionId"
    `;

    // Add conditions dynamically
    const conditions: string[] = [];
    const params: any[] = [];

    if (filter?.nameLike) {
      conditions.push('u."name" ILIKE $1');
      params.push(`%${filter.nameLike}%`);
    }
    if (filter?.departmentId) {
      conditions.push(`u."departmentId" = $${params.length + 1}`);
      params.push(filter.departmentId);
    }
    if (filter?.email) {
      conditions.push(`u."email" = $${params.length + 1}`);
      params.push(filter.email);
    }
    if (filter?.supervisorPositionId) {
      conditions.push(`u."supervisorPositionId" = $${params.length + 1}`);
      params.push(filter.supervisorPositionId);
    }
    if (filter?.role) {
      conditions.push(`p."role" = $${params.length + 1}`);
      params.push(filter.role);
    }
    if (filter?.includeExecutive === false) {
      conditions.push(`d."code" != ''`);
    }
    if (filter?.ids && filter.ids.length > 0) {
      conditions.push(`u."id" IN (${filter.ids.map((_, i) => `$${params.length + i + 1}`).join(", ")})`);
      params.push(...filter.ids);
    }
    if (filter?.emails && filter.emails.length > 0) {
      conditions.push(`u."email" IN (${filter.emails.map((_, i) => `$${params.length + i + 1}`).join(", ")})`);
      params.push(...filter.emails);
    }
    if (filter?.positionIds && filter.positionIds.length > 0) {
      conditions.push(`p."id" IN (${filter.positionIds.map((_, i) => `$${params.length + i + 1}`).join(", ")})`);
      params.push(...filter.positionIds);
    }
    if (filter?.sectionId && filter?.onlyIsSectionUser) {
      conditions.push(`u."sectionId" = $${params.length + 1} AND s."isSection" = true`);
      params.push(filter.sectionId);
    } else if (filter?.sectionId) {
      conditions.push(`u."sectionId" = $${params.length + 1}`);
      params.push(filter.sectionId);
    } else if (filter?.onlyIsSectionUser === true) {
      conditions.push(`s."isSection" = true`);
    }

    if (filter?.isRequestFor) {
      conditions.push(`(p."role" = 'STAFF' OR p."role" = 'HEAD')`);
      // conditions.push('"supervisor".id IS NOT NULL'); // supervisor exists
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(" AND ")}`;
    }

    // Add pagination
    const size = filter?.size || defaultFilterSize;
    const page = filter?.page && filter?.page > 0 ? filter.page : 1;
    query += ` ORDER BY u."name" ASC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(size, (page - 1) * size);

    // const result = await ds.query(query, params);
    const result = await ds.query(query, params);

    if (filter?.isRequestFor) {
      let filteredResult = result.filter((user: any) => user.supervisorPositionId !== user.positionId);

      if (filteredResult.length > 0) {
        filteredResult = filteredResult.map((nativeUser: any): IUser => {
          return {
            id: nativeUser.id,
            name: nativeUser.name,
            email: nativeUser.email,
            supervisorPositionId: nativeUser.supervisorPositionId,
            department: nativeUser.departmentId
              ? {
                id: nativeUser.departmentId,
                name: nativeUser.departmentName,
                code: nativeUser.departmentCode,
              }
              : null,
            section: nativeUser.sectionId
              ? {
                id: nativeUser.sectionId,
                name: nativeUser.sectionName,
                isSection: nativeUser.isSection,
              }
              : null,
            position: nativeUser.positionId
              ? {
                id: nativeUser.positionId,
                name: nativeUser.positionName,
                role: nativeUser.positionRole,
              }
              : null,
          };
        });
      }

      return [filteredResult, filteredResult.length];
    }

    return [result, result.length];
  };
};

export const implFindUserSupervisors = (ds: DataSource): FindUserSupervisors => {
  //
  return async (ctx, req) => {
    //
    const result: IUser[] = [];

    let posId = req.positionId;

    let counterGuard = 0;

    do {
      let user = await getManager(ctx, ds)
        .getRepository(User)
        .findOne({
          where: {
            position: { id: posId },
          },
          relations: {
            department: true,
            section: true,
            position: true,
          },
        });

      if (user) {
        result.push(user);
        posId = user.supervisorPositionId!;
      } else {
        // check acting
        const acting = await getActingDoA(ctx, ds, posId);
        if (acting) {
          const delegateUser = await getManager(ctx, ds)
            .getRepository(User)
            .findOne({
              where: { id: acting.delegateToUserId },
              relations: { department: true, section: true, position: true },
            });
            
          if (delegateUser) {
            const actingPosition = await getManager(ctx, ds)
              .getRepository(Position)
              .findOne({ where: { id: acting.id } });

            if (actingPosition) {
              delegateUser.position = actingPosition;
              result.push(delegateUser);
            }
          }

          const positionSupervisor = await getManager(ctx, ds)
            .getRepository(PositionSupervisor)
            .findOne({
              where: { positionId: acting.id },
              order: { supervisorPositionId: "ASC" },
            });

          posId = positionSupervisor?.supervisorPositionId!;
        }
      }

      if (user?.position?.id === posId) {
        break;
      }

      counterGuard++;
    } while (counterGuard < 20);
    return [result, result.length];
  };
};

export const implFindUserSupervisorsRoleDepartment = (ds: DataSource): FindUserSupervisorsRoleDepartment => {
  //
  return async (ctx, payload) => {
    //

    const result: IUser[] = [];

    const firstStructural = await getManager(ctx, ds)
      .getRepository(User)
      .findOne({
        where: {
          position: { role: payload.role },
          department: { id: payload.departmentId },
        },
        relations: {
          position: true,
          department: true,
        },
      });

    result.push(firstStructural!);

    let posId = firstStructural?.supervisorPositionId;

    let counterGuard = 0;
    do {
      const user = await getManager(ctx, ds)
        .getRepository(User)
        .findOne({
          where: {
            position: { id: posId },
          },
          relations: {
            position: true,
          },
        });

      if (user) {
        result.push(user);
      }

      posId = user?.supervisorPositionId!;

      if (user?.position?.id === posId) {
        break;
      }

      counterGuard++;
    } while (counterGuard < 20);

    return [result, result.length];
  };
};

const getActingDoA = async (ctx: Context, ds: DataSource, posId: string) => {
  //
  const now = getDateOnly(new Date());
  const actingUser = await getManager(ctx, ds)
    .getRepository(Delegation)
    .findOne({
      where: {
        id: posId,
        type: "ACTING",
        startDate: LessThanOrEqual(now),
        endDate: MoreThanOrEqual(now),
      },
    });

  return actingUser;
};
