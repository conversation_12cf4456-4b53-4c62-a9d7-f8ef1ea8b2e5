import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, PrimaryColumn } from "typeorm";
import { HasApprovalGroup } from "../model/model_approval.js";
import {
  EvaluationClarification,
  EvaluationSubmission,
  PrequalificationTemplate as IPrequalificationTemplate,
  PrequalificationVendor as IPrequalificationVendor,
  PQBusinessField,
  PQBusinessLicense,
  PQMeetingInfo,
  PQSubmissionInfo,
  ResultRemarks,
  VendorPhasePQClarification,
  VendorPhasePQRegistration,
} from "../model/model_prequalification.js";
import { User } from "../model/model_user.js";
import {
  Business,
  BusinessClass,
  Commodity,
  CompanyStatus,
  Currency,
  Files,
  HighestExperienceScore,
  PreQualificationPhase,
  PrequalificationType,
  SharepointFile,
  TenderMethod,
  TypeOf,
} from "../model/vo.js";
// import { Vendor as IVendor } from "../model/model_vendor.js";
// import { Vendor } from "./table_vendor.js";
import { CIVDVendor } from "./table_civd_vendor.js";

@Entity()
export class PrequalificationTemplate implements IPrequalificationTemplate {
  //
  @PrimaryColumn({ type: "varchar", length: 25 })
  declare id: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  declare tenderCode: string;

  @Column({ type: "text", nullable: true })
  declare title: string;

  @Column({ type: "text", nullable: true })
  declare generalScopeOfWorks: string;

  @Column({ type: "jsonb", nullable: true })
  declare assignedUser: User | null;

  @Column({ type: "jsonb", nullable: true })
  declare requestedBackToBacks: User[];

  @Column({ type: "varchar", length: 20, nullable: true })
  declare pqState: "PQ_REQUIREMENT_DRAFT" | "PQ_REQUIREMENT_SUBMITTED";

  @Column({ type: "varchar", length: 30, nullable: true })
  declare businessType: TypeOf<typeof Business>;

  @Column({ type: "varchar", length: 10, nullable: true })
  declare businessClass: TypeOf<typeof BusinessClass>;

  @Column({ type: "jsonb", nullable: true })
  declare businessLicenses: PQBusinessLicense[];

  // @Column({ type: "varchar", length: 60, nullable: true })
  // declare businessLicense: string;

  // @Column({ type: "jsonb", nullable: true })
  // declare businessFields: PQBusinessField[];

  @Column({ type: "int", nullable: true })
  declare convertionRateUSDToIDR: number;

  @Column({ type: "int", nullable: true })
  declare localContentLevel: number;

  @Column({ type: "jsonb", nullable: true })
  declare additionalPQRequirements: SharepointFile[] | null;

  @Column({ type: "text", nullable: true })
  declare sourceRequisitionId: string;

  @Column({ type: "varchar", length: 5, nullable: true })
  declare currency: TypeOf<typeof Currency>;

  @Column({ type: "bigint", nullable: true })
  declare ownerEstimateValue: number;

  @Column({ type: "jsonb", nullable: true })
  declare workOfLocation: string[];

  // @Column({ type: "jsonb", nullable: true })
  // declare projectLocation: string[];

  @Column({ type: "varchar", length: 80, nullable: true })
  declare companyStatus: TypeOf<typeof CompanyStatus>;

  @Column({ type: "varchar", nullable: true })
  declare domicile: string;

  @Column({ type: "varchar", length: 10, nullable: true })
  declare highRiskCategory: string;

  @Column({ type: "int", nullable: true })
  declare basicCapability: number;

  @Column({ type: "varchar", nullable: true })
  declare highestExperienceScore: TypeOf<typeof HighestExperienceScore>;

  @Column({ type: "boolean", nullable: true })
  declare financialDueDiligence: boolean;

  @Column({ type: "varchar", nullable: true })
  declare wavPassingLevel: string;

  @Column({ type: "jsonb", nullable: true })
  declare otherInformations: { text: string; show: boolean }[];

  @Column({ type: "varchar", length: 30, nullable: true })
  declare prequalificationType: TypeOf<typeof PrequalificationType>;

  @Column({ type: "jsonb", nullable: true })
  declare invitedVendors: number[];

  @Column({ type: "timestamp", nullable: true })
  declare announcementDate: Date | null;

  @Column({ type: "boolean", nullable: true })
  declare pqMeeting: boolean;

  @Column({ type: "timestamp", nullable: true })
  declare pqMeetingDate: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare pqRegistrationDate: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare pqSubmissionDate: Date | null;

  @Column({ type: "jsonb", nullable: true })
  declare pqAnnouncementDoc: SharepointFile[] | null;

  @Column({ type: "jsonb", nullable: true })
  declare pqStatementLetters: SharepointFile[] | null;

  @Column({ type: "jsonb", nullable: true })
  declare vhseMSQuisionerForm: SharepointFile[] | null;

  @Column({ type: "jsonb", nullable: true })
  declare financialDueDiligenceForm: SharepointFile[] | null;

  @Column({ type: "jsonb", nullable: true })
  declare generalProvisionsRequirementsForm: SharepointFile[] | null;

  @Column({ type: "jsonb", nullable: true })
  declare localCompanyStatementLetters: SharepointFile[] | null;

  @Column({ type: "jsonb", nullable: true })
  declare basicCapabilityCalculationForm: SharepointFile[] | null;

  @Column({ type: "timestamp", nullable: true })
  declare assignmentDate: Date | null;

  @Column({ type: "varchar", length: 25, nullable: true })
  declare tenderMethod: TypeOf<typeof TenderMethod>;

  @Column({ type: "timestamp", nullable: true })
  declare contractDateStart: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare contractDateEnd: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare poDateIssuance: Date | null;

  @Column({ type: "timestamp", nullable: true })
  declare poDateDelivery: Date | null;

  @Column({ type: "jsonb", nullable: true })
  declare phasesRequirement: HasApprovalGroup | null;

  @Column({ type: "jsonb", nullable: true })
  declare phasesRegistration: HasApprovalGroup | null;

  @Column({ type: "jsonb", nullable: true })
  declare phasesEvaluation: IPrequalificationTemplate["phasesEvaluation"];

  @Column({ type: "jsonb", nullable: true })
  declare phasesClarification: EvaluationClarification | null;

  @Column({ type: "jsonb", nullable: true })
  declare pqMeetingInfo: PQMeetingInfo | null;

  @Column({ type: "varchar", length: 30, nullable: true })
  declare currentPhase: TypeOf<typeof PreQualificationPhase>;

  @Column({ type: "varchar", length: 10, nullable: true })
  commodity: TypeOf<typeof Commodity>;

  @Column({ type: "text", nullable: true })
  declare justification: string;
}

@Entity()
export class PrequalificationVendor implements IPrequalificationVendor {
  //
  @PrimaryColumn({ type: "varchar", length: 25 })
  declare id: string;

  @ManyToOne(() => PrequalificationTemplate)
  declare prequalificationTemplate: IPrequalificationTemplate;

  // @ManyToOne(() => Vendor)
  // declare vendor: IVendor;
  @ManyToOne(() => CIVDVendor)
  @JoinColumn({ name: "civdVendorId" })
  declare civdVendor: CIVDVendor;

  @Column({ type: "int", nullable: true })
  declare civdVendorId: number;

  // @Column({ type: "varchar", length: 30, nullable: true })
  // declare entitas: "consortium" | "tunggal";

  // @Column({ type: "jsonb", nullable: true })
  // declare listPerusahaan: { namaPerusahaan?: string; statusPerusahaan?: string; statusKeanggotaan: "pemuka" | "anggota" }[];

  @Column({ type: "varchar", length: 30, nullable: true })
  declare status: IPrequalificationVendor["status"];

  @Column({ type: "jsonb", nullable: true })
  declare phaseRegistration: VendorPhasePQRegistration;

  @Column({ type: "jsonb", nullable: true })
  declare phaseSubmission: { submission1: PQSubmissionInfo; submission2: PQSubmissionInfo; submission3: PQSubmissionInfo };

  @Column({ type: "jsonb", nullable: true })
  declare phaseClarification: VendorPhasePQClarification;

  // @Column({ type: "jsonb", nullable: true })
  // declare suratMinat: ResultRemarks[];

  // @Column({ type: "jsonb", nullable: true })
  // declare suratKuasaMinat: ResultRemarks[];

  // @Column({ type: "jsonb", nullable: true })
  // declare suratSPDA: ResultRemarks[];

  // @Column({ type: "jsonb", nullable: true })
  // declare dokumenDomisili: ResultRemarks[];

  // @Column({ type: "jsonb", nullable: true })
  // declare suratIzinUsaha:  ResultRemarks[];

  // @Column({ type: "jsonb", nullable: true })
  // declare sertifikatTKDN: ResultRemarks[];

  // @Column({ type: "jsonb", nullable: true })
  // declare suratPernyataanPQ: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare suratKuasaPernyataanPQ: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare dokumenPembuktionSPDN: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare dokumenPerjanjian: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare dokumenK3LL: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare dokumenEKF: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare dokumenLK: Files | null;

  // @Column({ type: "jsonb", nullable: true })
  // declare dokumenBPKK: Files[];
}
