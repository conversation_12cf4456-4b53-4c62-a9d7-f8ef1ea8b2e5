import { BaseFindManyFilter, DeleteEntity, FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { isOperationalServiceDepartment } from "./model_department.js";
import { User } from "./model_user.js";
import { Commodity, CommodityService, ContractTypeEngagement, DocumentTemplate, SubDocumentRequisition, TenderMethod, TypeOf, UserRole, ValueRange } from "./vo.js";

export interface ApprovalTemplateRule {
  documentType?: TypeOf<typeof DocumentTemplate>;
  // oeValueRange?: TypeOf<typeof ValueRange>;
  // daValueRange?: TypeOf<typeof ValueRange> | null;
  commodity?: TypeOf<typeof Commodity>;
  commodityServiceType?: TypeOf<typeof CommodityService>;
  // isFirmCommitment?: boolean;
  isAdditionalProcPlan?: boolean;
  budgetOwners?: string[];
  isRequestFor?: boolean;
  hasNoApprover?: boolean;
  isCommodityGoodsAndFirmCommitment?: boolean;
  isDaJustification?: boolean;
  isValueGreaterThanMinOeValue?: boolean; // 20_000
  isValueGreaterThanMaxOeValue?: boolean; // 50_000
  isValueGreaterThanMinRequisitionValue?: boolean; // 100_000
  isValueGreaterThanMaxDaValue?: boolean; // 5_000_000
  isAdditionalProcPlanOrValueMoreThanPercentage?: boolean; // 10
  // isUseInsurance?: boolean;
  additionalBudgetOwnerManager?: boolean;
}

export interface ApprovalTemplate {
  role?: TypeOf<typeof UserRole>;
  users?: User[];
  as?: string;
  subDocumentType?: TypeOf<typeof SubDocumentRequisition>;
  rule?: ApprovalTemplateRule;
}

export interface ApprovalTemplateGroup {
  id?: string;
  sequence?: number;
  approvalTemplates: ApprovalTemplate[];
  documentType?: TypeOf<typeof DocumentTemplate>;
}

export type FindApprovalTemplateGroupFilter = BaseFindManyFilter & {
  documentType: TypeOf<typeof DocumentTemplate>;
};

export type FindApprovalTemplateGroup = FindManyEntity<ApprovalTemplateGroup, FindApprovalTemplateGroupFilter>;
export type SaveApprovalTemplateGroups = SaveEntity<ApprovalTemplateGroup[]>;
export type DeleteApprovalTemplateGroups = DeleteEntity<{ documentType: TypeOf<typeof DocumentTemplate> }>;

export const requisitionDocRuleValue = {
  minRQValue: 100_000, 
  maxRQValue: 300_000, 
  minOEValue: 20_000, 
  maxOEValue: 50_000, 
  minDAValue: 5_000_000, 
  maxDAValue: 5_000_000, 
  valuePercentage: 10 / 100,
}

export const getRequistionDocRule = (
  commodity: TypeOf<typeof Commodity>,
  departmentCode: string,
  budgetOwners: string[],
  isAdditionalProcPlan: boolean,
  tenderMethod: TypeOf<typeof TenderMethod>,
  contractTypeEngagement: TypeOf<typeof ContractTypeEngagement>,
  value: number,
  procPlanValue: number,
): ApprovalTemplateRule => {
  return {
    additionalBudgetOwnerManager: true,
    documentType: "REQUISITION",
    commodity: commodity,
    commodityServiceType: isOperationalServiceDepartment(commodity, departmentCode),
    budgetOwners: budgetOwners,
    isAdditionalProcPlan: isAdditionalProcPlan,
    isRequestFor: true,
    isCommodityGoodsAndFirmCommitment: commodity === "GOODS" && contractTypeEngagement === "FIRM_COMMITMENT",
    isDaJustification: tenderMethod === "DIRECT_APPOINTMENT" && value > requisitionDocRuleValue.minRQValue,
    isValueGreaterThanMinOeValue: value > requisitionDocRuleValue.minOEValue, // 20_000
    isValueGreaterThanMaxOeValue: value > requisitionDocRuleValue.maxOEValue, // 50_000
    isValueGreaterThanMinRequisitionValue: value > requisitionDocRuleValue.minRQValue,
    isValueGreaterThanMaxDaValue: value > requisitionDocRuleValue.maxDAValue, // 5_000_000
    isAdditionalProcPlanOrValueMoreThanPercentage: isAdditionalProcPlan || value > procPlanValue + procPlanValue * requisitionDocRuleValue.valuePercentage // 10%
  }
}