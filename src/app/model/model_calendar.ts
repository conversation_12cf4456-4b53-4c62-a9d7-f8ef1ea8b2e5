import { Context } from "../../framework/core.js";
import { FindManyEntity } from "../../framework/repository.js";

export interface Calendar {
  id: string;
  name: string;
  description: string;
  type?: string;
  date: Date | null;
  year: number;
}

export class FindCalendarFilter {
  ids?: string[];
  nameLike?: string;
  type?: string;
  date?: Date;
  year?: number;
  page?: number;
  size?: number;
}

export type FindCalendar = FindManyEntity<Calendar, FindCalendarFilter>;

export type FindOneCalendar = (ctx: Context, id: string) => Promise<Calendar | null>;
