import { Context } from "../../framework/core.js";
import { BaseFindManyFilter, FindManyEntity } from "../../framework/repository.js";

export interface BusinessFieldLicense {
  id: string;
  documentName: string;
  categoryCode?: string | null;
  clasification?: string | null;
  // subClasification?: string | null;
}

export class FindBusinessFieldLicenseFilter extends BaseFindManyFilter {
  ids?: string[];
  search?: string;
  documentName?: string;
  categoryCode?: string;
  clasification?: string;
  // subClasification?: string;
}

export type FindAllBusinessFieldLicense = FindManyEntity<BusinessFieldLicense, FindBusinessFieldLicenseFilter>;

export type FindBusinessFieldLicenseDocument = FindManyEntity<BusinessFieldLicense, FindBusinessFieldLicenseFilter>;

export type FindOneBusinessFieldLicense = (ctx: Context, id: string) => Promise<BusinessFieldLicense | null>;
