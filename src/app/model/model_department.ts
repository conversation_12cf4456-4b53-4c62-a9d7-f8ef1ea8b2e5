import { Context } from "../../framework/core.js";
import { BaseFindManyFilter, FindManyEntity } from "../../framework/repository.js";
import { FindUser, User } from "./model_user.js";
import { Commodity, CommodityService, TypeOf } from "./vo.js";

export interface Department {
  id: string;
  name?: string;
  code?: string;
}

export class FindDepartmentFilter extends BaseFindManyFilter {
  ids?: string[];
  code?: string;
}

export type FindDepartment = FindManyEntity<Department, FindDepartmentFilter>;

export const isOperationalServiceDepartment = (commodity: TypeOf<typeof Commodity>, departmentCode: string): TypeOf<typeof CommodityService> | undefined => {
  if (commodity === "GOODS") {
    return undefined;
  }

  return ["03", "04", "06", "13", "15", "18", "19", "20"].some((x) => x === departmentCode) ? "OPS" : "NON_OPS";
};

export const collectDepartments = async (ctx: Context, findUser: FindUser, user: User, deptSet: Set<string>) => {
  //

  if (user.department?.code === "") {
    const [users] = await findUser(ctx, { supervisorPositionId: user.position?.id });
    for (const u of users) {
      if (u.id === user.id) continue;
      // console.log(u.name);
      await collectDepartments(ctx, findUser, u, deptSet);
    }
  } else {
    if (!deptSet.has(user.department?.id!)) deptSet.add(user.department?.id!);
  }
};
