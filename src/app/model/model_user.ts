import { ActionHandler, Context } from "../../framework/core.js";
import { BaseFindManyFilter, FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { Department } from "./model_department.js";
import { Position } from "./model_position.js";
import { Section } from "./model_section.js";
import { departmentGetRBTBsExceptions } from "./vo.js";

export interface User {
  id: string;
  name?: string;
  email?: string;
  position?: Position | null;
  section?: Section | null;
  department?: Department | null;
  supervisorPositionId?: string;
}

export interface UserLogin extends User {
  isAdmin?: boolean | null;
  isAdminProcPlan?: boolean | null;
  isAdminRequisition?: boolean | null;
}

export class FindUserFilter extends BaseFindManyFilter {
  ids?: string[];
  emails?: string[];
  email?: string;
  nameLike?: string;
  sectionId?: string;
  departmentId?: string;
  positionIds?: string[];
  supervisorPositionId?: string;
  role?: string;
  includeExecutive?: boolean;
  onlyIsSectionUser?: boolean;
  isRequestFor?: boolean;
}

export type SaveEmailUser = SaveEntity<User>;
export type FindUser = FindManyEntity<User, FindUserFilter>;
export type FindUserSupervisors = ActionHandler<{ positionId: string }, [User[], number]>;
export type FindUserSupervisorsRoleDepartment = ActionHandler<{ role: string; departmentId: string }, [User[], number]>;
export type FindUserNative = FindManyEntity<User, FindUserFilter>;

export const getRequestFor = async (ctx: Context, findUser: FindUser, requestForId: string, departmentId: string, sectionId: string) => {
  if (!requestForId) {
    throw new Error(`requestForId is required`);
  }

  // ambil user requestfor
  const [requestForUsers] = await findUser(ctx, {
    ids: [requestForId],
    departmentId,
    sectionId,
    size: 1,
  });
  const requestForUser = requestForUsers.length > 0 ? requestForUsers[0] : null;
  if (!requestForUser) {
    throw new Error(`request for id ${requestForId} not found`);
  }
  return requestForUser;
};

export const getRBTBs = async (ctx: Context, findUser: FindUser, userLoginId: string, requestForId: string, rbtbIds: string[], departmentId: string) => {
  //
  if (rbtbIds?.length === 0) {
    throw new Error(`requesterBackToBack must not be empty`);
  }

  rbtbIds.forEach((x) => {
    if (!x) {
      throw new Error(`requesterBackToBack must not be empty`);
    }
    if (x === requestForId && !departmentGetRBTBsExceptions.includes(departmentId)) {
      throw new Error(`requestFor must not in requesterBackToBack`);
    }
    // if (x === userLoginId) {
    //   throw new Error(`userLogin must not in requesterBackToBack`);
    // }
  });

  // ambil user rbtb
  const [rbtbs] = await findUser(ctx, {
    ids: rbtbIds,
    departmentId,
  });

  if (rbtbs.length === 0) {
    throw new Error(`requester back to back for ids ${rbtbIds} not found`);
  }
  return rbtbs;
};
