import { Context } from "../../framework/core.js";
import { FindManyEntity } from "../../framework/repository.js";

export interface PositionSupervisor {
  positionId: string;
  supervisorPositionId: string;
}

export class FindPositionSupervisorFilter {
  positionId?: string;
  supervisorPositionId?: string;
  page?: number;
  size?: number;
}

export type FindPositionSupervisor = FindManyEntity<PositionSupervisor, FindPositionSupervisorFilter>;

export type FindOnePositionSupervisor = (ctx: Context, id: string) => Promise<PositionSupervisor | null>;
