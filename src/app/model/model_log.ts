import { DeepPartial, ObjectLiteral } from "typeorm/browser";
import { Context, createContext } from "../../framework/core.js";
import { FindManyEntity } from "../../framework/repository.js";
import { generateID } from "../../framework/helper.js";
import { DataSource } from "typeorm/browser";
import { getDatasource } from "../../index.js";

// Define the Log interface here instead of importing it
export interface Log {
  id: string;
  name: string;
  message: any;
  status: string; // ERROR | SUCCESS
  createdAt: Date | null;
}

export class FindLogFilter {
  // id?: string;
  name?: string;
  status?: string;
  page?: number;
  size?: number;
}

export type FindLog = FindManyEntity<ObjectLiteral, FindLogFilter>;
export type FindOneLog = (ctx: Context, id: string) => Promise<Log | null>;

export const setLogger = (name: string, message: any, status: string): Log => {
  // 
  const logger = {
    id: generateID(16),
    name: name,
    message: message,
    status: status.toUpperCase(),
    createdAt: new Date(),
  };
  return logger;
};

export const saveLogger = async (name: string, messages: any, status: string): Promise<Log> => {
  // 
  const ds = getDatasource();
  
  if (!ds.isInitialized) {
    await ds.initialize();
  }

  let message = messages;
  if (typeof messages === "object" && !(messages instanceof Error)) {
    message = messages;
  } else if (messages instanceof Error) {
    message = { error: messages.message, stack: messages.stack };
  } else if (typeof messages === "string") {
    message = { text: messages };
  }

  try {
    const repo = ds.getRepository("log");
    const log = await repo.save({
      id: generateID(16),
      name: name,
      message: message,
      status: status.toUpperCase(),
      createdAt: new Date(),
    });
    
    return log;
  } catch (error) {
    console.error("Failed to save log:", error);
    // Return a log object even if saving failed
    return {
      id: generateID(16),
      name: name,
      message: message,
      status: status.toUpperCase(),
      createdAt: new Date(),
    };
  }
};
