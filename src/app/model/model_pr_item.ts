import { ObjectLiteral } from "typeorm/browser";
import { Context } from "../../framework/core.js";
import { FindManyEntity } from "../../framework/repository.js";

export interface PrItem {
  id: string;
  prnumber: string;
  item: string;
  material: string;
  materialdesc: string;
  // _createdBy:	string;
  // _createdAt:	Date;
  // _updatedBy:	string;
  // _updatedAt:	Date;
  // _deletedAt:	Date;
  // _authors:	string;
  // _readers:	string;
  syncAt?: Date | null;
}

export class FindPrItemFilter {
  // id?: string;
  prnumber?: string;
  page?: number;
  size?: number;
}

export type FindPrItem = FindManyEntity<ObjectLiteral, FindPrItemFilter>;
export type FindOnePrItem = (ctx: Context, idPrItem: string) => Promise<PrItem | null>;
