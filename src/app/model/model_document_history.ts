import { DocumentTemplate, TypeOf } from "./vo.js";
import { User } from "./model_user.js";
import { BaseFindManyFilter, FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { DateOrString } from "../utility/helper.js";

export interface DocumentHistory {
  id: string;
  documentId: string;
  documentType: TypeOf<typeof DocumentTemplate>;
  date: Date | null;
  user: User | null;
  message: string;
  comment: string;
}

export class FindDocumentHistoryFilter extends BaseFindManyFilter {
  documentId: string;
  documentType?: TypeOf<typeof DocumentTemplate>;
  year: number;
}

export type SaveDocumentHistory = SaveEntity<DocumentHistory>;
export type FindDocumentHistory = FindManyEntity<DocumentHistory, FindDocumentHistoryFilter>;
