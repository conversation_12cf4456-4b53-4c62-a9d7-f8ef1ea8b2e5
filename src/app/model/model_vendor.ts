import { Context } from "../../framework/core.js";
import { FindManyEntity } from "../../framework/repository.js";

export interface Vendor {
  id: string;
  name: string;
  location: string;
  accessApps?: string;
}

// TODO::new vendor data format
// {
//   "id": 8,
//   "username": "<EMAIL>",
//   "email": "<EMAIL>",
//   "password": "$2a$10$33APTdvapKSmTCoZqmu71241pWaehasMvXUu1RCI5DyyZGQyAn6C",
//   "locked": 0,
//   "vendor_id": "0001024002",
//   "vendor_name": "MEDITERANIA NARIMA PRATAMA",
//   "admin": 0,
//   "apps_access": "invoice_upload",
//   "create_at": "2021-03-22 03:05:58.660",
//   "update_at": "2021-12-28 04:21:11.000"
// }


export class FindVendorFilter {
  ids?: string[];
  nameLike?: string;
  page?: number;
  size?: number;
}

export type FindVendor = FindManyEntity<Vendor, FindVendorFilter>;

export type FindOneVendor = (ctx: Context, idVendor: string) => Promise<Vendor | null>;
