import { Context } from "../../framework/core.js";
import { FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { User } from "./model_user.js";

export interface Delegation {
  id: string; // positionId
  delegateToUserId: string; // userId
  delegatorPositionName: string;
  delegatorEmail?: string | null;
  delegatorName: string;
  delegateToName: string;
  delegateToEmail?: string | null;
  delegateToUserPositionId?: string | null;
  delegateToUserPositionName?: string | null;
  startDate: Date | null;
  endDate: Date | null;
  type: "DELEGATION" | "ACTING";
  remarks?: string;
  syncAt: Date | null;
}

export type DelegationUser = {
  user: User;
  startDate: Date | null;
  endDate: Date | null;
  remarks?: string;
  type: "DELEGATION" | "ACTING";
  syncAt: Date | null;
}

export class FindDelegationFilter {
  ids?: string[]; // positionId
  delegateToUserId?: string; // userId
  nameLike?: string;
  startDate?: Date | null;
  endDate?: Date | null;
  type?: "DELEGATION" | "ACTING";
  page?: number;
  size?: number;
}

export type FindDelegation = FindManyEntity<Delegation, FindDelegationFilter>
export type FindOneDelegation = (ctx: Context, id: string) => Promise<Delegation | null>;
export type SaveDelegation = SaveEntity<Delegation>;