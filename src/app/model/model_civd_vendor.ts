import { Context } from "../../framework/core.js";
import { FindManyEntity } from "../../framework/repository.js";
import { translateBusinessClassToEn } from "../utility/helper.js";
import { Files, TypeOf } from "./vo.js";

export interface CIVDVendor {
  civdVendorId: number;
  companyType: string;
  name: string;
  npwp: string;
  address?: string;
  addressAlternative?: string;
  email: string;
  emailAlternative?: string;
  provName?: string;
  phone?: string;
  contactPerson?: string;
  contactPersonPosition?: string;
  presidentDirectorName?: string;
  businessLicense?: CIVDVendorBusinessLicense[];
  businessClass?: CIVDVendorBusinessClass[];
  sanction?: CIVDVendorSaction[];
  spda?: CIVDSpda[];

  location?: string;
  accessApps?: string;

  activeStatus?: string | null;
}

export type vendorCIVDLogin = Pick<CIVDVendor, 'civdVendorId' | 'name' | 'email' | 'npwp' | 'companyType'>;

export type CIVDVendorBusinessLicense = {
  businessType: string; // jenis usaha
  businessFieldCode: string; // kode bidang usaha / KBLI
  businessField: string; // bidang usaha
  businessClass: string | null; // golongan usaha
  businessLicenseNumber: string;
  validityStartDate: Date;
  validityEndDate: Date;
  businessLicenseFile?: Files | null; // .pdf
};

export type CIVDVendorSaction = {
  sanctionNumber?: string;
  sanctionColor: string;
  description: string;
  effectiveDate: Date;
  endDate: Date;
  releaseDate: Date;
  trial: boolean;
  trialEndDate?: Date;
};

export type CIVDSpda = {
  number: string;
  validity: string;
  fileId: string;
  spdaFile?: string | null; // civd file url
  uploadDate: Date;
  expiredDate: Date;
};

export type CIVDVendorBusinessClass = {
  year: number;
  class: TypeOf<typeof BusinessClass> | null;
};

export const BusinessClass = ["KECIL", "MENENGAH", "BESAR"] as const;

// TODO::new vendor data format
// {
//   "id": 8,
//   "username": "<EMAIL>",
//   "email": "<EMAIL>",
//   "password": "$2a$10$33APTdvapKSmTCoZqmu71241pWaehasMvXUu1RCI5DyyZGQyAn6C",
//   "locked": 0,
//   "vendor_id": "**********",
//   "vendor_name": "MEDITERANIA NARIMA PRATAMA",
//   "admin": 0,
//   "apps_access": "invoice_upload",
//   "create_at": "2021-03-22 03:05:58.660",
//   "update_at": "2021-12-28 04:21:11.000"
// }

export class FindVendorCIVDFilter {
  civdIds?: number[];
  email?: string;
  npwp?: string;
  nameLike?: string;
  spdaValid?: string;
  page?: number;
  size?: number;
}

export type FindVendorCIVD = FindManyEntity<CIVDVendor, FindVendorCIVDFilter>;

export type FindOneVendorCIVD = (ctx: Context, civdVendorId: number) => Promise<CIVDVendor | null>;

export const transformCIVDVendors = (vendors: CIVDVendor[]): CIVDVendor[] => {
  //
  const results = vendors.map((vendor) => {
    const spda = vendor.spda?.sort((a, b) => {
      if (a.validity !== b.validity) {
        return a.validity === "Valid" ? -1 : 1;
      }

      const aDate = new Date(a.expiredDate);
      const bDate = new Date(b.expiredDate);
      return bDate.getTime() - aDate.getTime();
    });

    const businessLicense = vendor.businessLicense?.sort((a, b) => {
      const aDate = new Date(a.validityEndDate);
      const bDate = new Date(b.validityEndDate);
      return bDate.getTime() - aDate.getTime();
    });

    const sanction = vendor.sanction?.sort((a, b) => {
      const aDate = new Date(a.endDate);
      const bDate = new Date(b.endDate);
      return bDate.getTime() - aDate.getTime();
    });

    if (vendor.businessClass && vendor.businessClass.length > 0) {
      let businessClass = vendor.businessClass.sort((a, b) => b.year - a.year)[0];
      businessClass.class = translateBusinessClassToEn(businessClass.class!) as any;
      vendor.businessClass = [
        businessClass
      ];
    }

    return {
      ...vendor,
      spda,
      businessLicense,
      sanction,
    };
  });

  return results;
};