import { BaseFindManyFilter, FindManyEntity } from "../../framework/repository.js";
import { Department } from "./model_department.js";

export interface Section {
  id: string;
  name?: string;
  department?: Department | null;
  isSection?: boolean;
}

export class FindSectionFilter extends BaseFindManyFilter {
  departmentId?: string;
  departmentIds?: string[];
  ids?: string[];
  nameLike?: string;
}

export type FindSection = FindManyEntity<Section, FindSectionFilter>;
