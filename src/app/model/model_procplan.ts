import { Context } from "../../framework/core.js";
import { DeleteEntity, FindManyEntity, SaveEntity } from "../../framework/repository.js";
import { DateOrString, getDateOnly, toUSD } from "../utility/helper.js";
import { HasApprovalGroup } from "./model_approval.js";
import { Department } from "./model_department.js";
import { Section } from "./model_section.js";
import { User } from "./model_user.js";
import {
  Activity,
  AuthorizationForExpenditure,
  Commodity,
  Currency,
  DocumentStatus,
  PlanCreated,
  ProcPlan,
  TenderMethod,
  TypeOf,
  ValueCurrency,
  WorkProgramAndBudget,
  WorkProgramReference,
  formatNumber,
  getEmptyValueCurrency,
} from "./vo.js";

export interface ProcPlanHeader extends HasApprovalGroup {
  id: string;
  submitter: User | null;
  procPlanType: TypeOf<typeof ProcPlan>;
  submittedDate: Date | null;
  year: number;
  count: number;
  totalValueEstimation: ValueCurrency[];
  sections: Section[];
  section: Section | null;
  department: Department | null;
  durationDays?: number | 0;
  fromProcPlanHeaderId?: string | null;
}

export class ProcPlanDetail {
  id: string;
  createdAt: Date;
  creator: User | null;
  year: number;
  procPlanCode: string;
  section: Section | null;
  department: Department | null;
  procPlanHeader: ProcPlanHeader | null;
  procPlanType: TypeOf<typeof ProcPlan>;
  requestFor: User | null;
  requesterBackToBack: User[];
  usedByRequisitionIds: string[];

  commodity: TypeOf<typeof Commodity>;
  createdType: TypeOf<typeof PlanCreated>;
  carryOverProcPlanId: string;
  carryOverProcPlanCode: string;
  activityType: TypeOf<typeof Activity>;
  title: string;
  generalScopeOfWork: string;
  tenderMethod: TypeOf<typeof TenderMethod>;

  requisitionSubmissionDate: Date | null;
  tenderStartDate: Date | null;

  contractDateStart: Date | null;
  contractDateEnd: Date | null;
  poDateIssuance: Date | null;
  poDateDelivery: Date | null;

  currency: TypeOf<typeof Currency>;
  valueEstimation: number;
  valueInUSD: number;
  localContentLevel: number;
  estCurrYearExpenditure: number;
  approvalAnnualBudget: number;
  technicalMoMSubjectAndDate: string[];
  workProgramReferences: TypeOf<typeof WorkProgramReference>[];
  workProgramAndBudget: WorkProgramAndBudget[];
  authorizationForExpenditure: AuthorizationForExpenditure[];

  remarks: string;
  masterList: boolean;
  durationType: string;
  durationValue: string;

  errorFields: string[];
}

export type ProcplanDetailPayload = Omit<
  ProcPlanDetail,
  | "id"
  | "createdAt"
  | "creator"
  | "year"
  | "procPlanCode"
  | "section"
  | "department"
  | "procPlanHeader"
  | "procPlanType"
  | "requestFor"
  | "requesterBackToBack"
  | "usedByRequisitionIds"
>;

export class FindProcPlanHeaderFilter {
  page?: number;
  size?: number;
  id?: string;
  sectionIds?: string[];
  departmentIds?: string[];
  procPlanType?: TypeOf<typeof ProcPlan>;
  year?: number;
  status?: TypeOf<typeof DocumentStatus>;
  isSendBack?: boolean;
  useSelect?: boolean;
}

export class FindAllProcPlanDetailFilter {
  page?: number;
  size?: number;
  procPlanType?: TypeOf<typeof ProcPlan>;
  procPlanHeaderId?: string;
  procPlanHeaderIds?: string[];
  departmentId?: string;
  departmentIds?: string[];
  year?: number;
  sectionId?: string;
  id?: string;
  ids?: string[];
  title?: string;
  status?: TypeOf<typeof DocumentStatus>;
  currentYear?: number;
  includeCurrentYear?: boolean;
  procPlanCode?: string;
}

export type SaveProcPlanHeader = SaveEntity<ProcPlanHeader>;
export type FindProcPlanHeader = FindManyEntity<ProcPlanHeader, FindProcPlanHeaderFilter>;
export type SaveProcPlanDetail = SaveEntity<ProcPlanDetail[]>;
export type FindProcPlanDetail = FindManyEntity<ProcPlanDetail, FindAllProcPlanDetailFilter>;
export type FindProcPlanDetailByRbtb = FindManyEntity<ProcPlanDetail, { userId: string; year?: number; page?: number; size?: number }>;
export type FindProcPlanDetailByFromExcludeDepartment = FindManyEntity<
  ProcPlanDetail,
  { userId: string; year?: number; departmentId?: string; page?: number; size?: number }
>;
export type DeleteProcPlanHeader = DeleteEntity<{ id: string }>;
export type DeleteProcPlanDetail = DeleteEntity<{ id: string }>;
export type DeleteProcPlanDetailByHeaderId = DeleteEntity<{ procPlanHeader: { id: string } }>;

export const getLastProcplanHeader = async (
  ctx: Context,
  findProcPlanHeader: FindProcPlanHeader,
  departmentId: string | null,
  sectionId: string | null,
  year: number,
  procPlanType: TypeOf<typeof ProcPlan>
) => {
  const [pphs] = await findProcPlanHeader(ctx, {
    procPlanType,
    departmentIds: departmentId ? [departmentId] : [],
    sectionIds: sectionId ? [sectionId] : [],
    year,
  });
  return pphs.length > 0 ? pphs[0] : null;
};

export const createNewProcPlanHeader = (
  now: DateOrString,
  year: number,
  sectionId: string | null,
  departmentId: string,
  newProcplanHeaderId: string,
  procPlanType: TypeOf<typeof ProcPlan>
): ProcPlanHeader => {
  return {
    id: newProcplanHeaderId,
    year,
    procPlanType,
    status: "DRAFT",
    isSendBack: false,
    submittedDate: getDateOnly(now),
    count: 0,
    section: sectionId ? { id: sectionId } : null,
    department: { id: departmentId },
    approvalGroup: null,
    totalValueEstimation: getEmptyValueCurrency(),
    submitter: null,
    sections: [],
  };
};

export const getExistingProcPlanDetail = async (
  ctx: Context,
  findProcPlanDetail: FindProcPlanDetail,
  procplanId: string,
  procPlanType: TypeOf<typeof ProcPlan>
) => {
  const [ppds] = await findProcPlanDetail(ctx, {
    id: procplanId,
    procPlanType,
  });
  const ppd = ppds.length > 0 ? ppds[0] : null;
  if (!ppd) {
    throw new Error("procplan detail not found..");
  }
  return ppd;
};

export const getUPPErrorFields = (ppd: ProcplanDetailPayload) => {
  let result = [];

  if ((ppd.title ?? "").length === 0) result.push("title");
  if ((ppd.generalScopeOfWork ?? "").length === 0) result.push("generalScopeOfWork");
  if (!ppd.requisitionSubmissionDate) result.push("requisitionSubmissionDate");
  if (!ppd.tenderStartDate) result.push("tenderStartDate");
  if (!ppd.valueEstimation) result.push("valueEstimation");
  if (!ppd.estCurrYearExpenditure) result.push("estCurrYearExpenditure");
  if (!ppd.approvalAnnualBudget) result.push("approvalAnnualBudget");
  if (ppd.localContentLevel === null || ppd.localContentLevel === undefined) result.push("localContentLevel");

  if (ppd.commodity === "GOODS") {
    if (!ppd.poDateIssuance) result.push("poDateIssuance");
    if (!ppd.poDateDelivery) result.push("poDateDelivery");
  }

  if (ppd.commodity === "SERVICES") {
    if (!ppd.contractDateStart) result.push("contractDateStart");
    if (!ppd.contractDateEnd) result.push("contractDateEnd");
  }

  // WPR
  if ((ppd.workProgramReferences ?? []).length === 0) result.push("checkWPR");

  // WPR - Techmom
  if (ppd.workProgramReferences && ppd.workProgramReferences.includes("TechMOM")) {
    if (ppd.technicalMoMSubjectAndDate) {
      ppd.technicalMoMSubjectAndDate.forEach((tmom) => {
        if (ppd.technicalMoMSubjectAndDate.some((tmom) => (tmom ?? "").length === 0)) {
          result.push("technicalMoMSubjectAndDate");
        }
      });
    }
  }

  // WPR - WPnB
  if (ppd.workProgramReferences && ppd.workProgramReferences.includes("WPnB")) {
    if (ppd.workProgramAndBudget) {
      ppd.workProgramAndBudget.some((wpnb) => {
        if (ppd.workProgramAndBudget.some((wpnb) => !wpnb.year || wpnb.scheduleLines.length === 0)) {
          result.push("workProgramAndBudget");
          return true;
        }

        if (wpnb.scheduleLines.some((sl) => !sl.schedule || sl.lines.length === 0)) {
          result.push("workProgramAndBudget");
          return true;
        }
      });
    }
  }

  // WPR - AFE
  if (ppd.workProgramReferences && ppd.workProgramReferences.includes("AFE")) {
    if (ppd.authorizationForExpenditure) {
      ppd.authorizationForExpenditure.some((afe) => {
        if (ppd.authorizationForExpenditure.some((afe) => !afe.no || afe.scheduleLines.length === 0)) {
          result.push("authorizationForExpenditure");
          return true;
        }

        if (afe.scheduleLines.some((sl) => !sl.schedule || sl.lines.length === 0)) {
          result.push("authorizationForExpenditure");
          return true;
        }
      });
    }
  }

  return result;
};

export const validateUPPErrorFields = (ppds: ProcPlanDetail[] | ProcPlanDetail) => {
  let errorFields = [];

  if (Array.isArray(ppds)) {
    ppds.forEach((ppd) => {
      if (ppd.errorFields && ppd.errorFields.length > 0) {
        const fields = {
          id: ppd.id,
          errorFields: ppd.errorFields,
        };
        errorFields.push(fields);
      }
    });
  } else {
    if (ppds.errorFields && ppds.errorFields.length > 0) {
      const fields = {
        id: ppds.id,
        errorFields: ppds.errorFields,
      };
      errorFields.push(fields);
    }
  }

  if (errorFields.length > 0) {
    throw new Error(`Validation error: ${JSON.stringify(errorFields, null, 2)}`);
  }

  return;
};

export const validateProcPlanRequest = (pd: ProcplanDetailPayload) => {
  //

  {
    if (!pd.commodity || !Commodity.some((type) => type === pd.commodity)) {
      throw new Error(`commodity must be one of : ${Commodity}`);
    }

    if (pd.commodity === "GOODS") {
      if (!pd.poDateDelivery || !pd.poDateIssuance) {
        throw new Error("for commodity GOODS, poDateDelivery and poDateIssuance must specified");
      }

      if (pd.poDateIssuance > pd.poDateDelivery) {
        throw new Error("poDateIssuance must be less than poDateDelivery");
      }
    }

    if (pd.commodity === "SERVICES") {
      if (!pd.contractDateStart || !pd.contractDateEnd) {
        throw new Error("for commodity SERVICES, contractDateStart and contractDateEnd must specified");
      }

      if (pd.contractDateStart > pd.contractDateEnd) {
        throw new Error("contractDateStart must be less than contractDateEnd");
      }
    }
  }

  {
    if (!pd.activityType || !Activity.some((type) => type === pd.activityType)) {
      throw new Error(`activityType must be one of : ${Activity}`);
    }
  }

  {
    if (!pd.currency || !Currency.some((type) => type === pd.currency)) {
      throw new Error(`currency must be one of : ${Currency}`);
    }
  }

  {
    if (!pd.createdType || !PlanCreated.some((type) => type === pd.createdType)) {
      throw new Error(`createdType must be one of : ${PlanCreated}`);
    }
  }

  // if (!pd.localContentLevel) {
  //   throw new Error("localContentLevel must be specified");
  // }

  if (!pd.title) {
    throw new Error("title must be specified");
  }

  {
    if (!pd.tenderMethod || !TenderMethod.some((type) => type === pd.tenderMethod)) {
      throw new Error(`tenderMethod must be one of : ${TenderMethod}`);
    }
  }

  if (!pd.valueEstimation) {
    throw new Error("valueEstimation must be specified");
  }

  {
    if (pd.estCurrYearExpenditure > pd.approvalAnnualBudget) {
      throw new Error(`Current Year Expenditure must be at less or equal than Approval Annual Budget`);
    }
  }
  //
};

export const formatValueProcPlan = (estimations: ValueCurrency[] | undefined) => {
  if (!estimations) return 0;
  const usdValue = estimations.find((x) => x.currency === "USD")?.value || 0;
  const idrValue = estimations.find((x) => x.currency === "IDR")?.value || 0;
  return formatNumber(usdValue + toUSD("IDR", idrValue));
};
