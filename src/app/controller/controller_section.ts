import { HTTPDataItem } from "../../framework/data_http.js";
import { arrayOfEnum } from "./_reused_properties.js";

export const controllerSection: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/section",
    usecase: "sectionGetAll",
    tag: "section",
    security: "bearerAuth",
    query: {
      departmentId: { type: "string" },
      nameLike: { type: "string" },
      ids: arrayOfEnum,
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          isSection: { type: "string" },
        },
      },
    },
    responseAsTable: true,
  },
];
