import { HTTPDataItem } from "../../framework/data_http.js";
import { PreQualificationPhase, PrequalificationType } from "../model/vo.js";
import { PQClarificationPayload, PQSubmissionPayload, arrayOfEnum, files, queryPageAndSize, vendorCIVDLogin } from "./_reused_properties.js";

export const controllerVendorPQ: HTTPDataItem[] = [
  //
  {
    method: "post",
    path: "/login",
    usecase: "loginVendor",
    tag: "prequalification_vendor",
    body: {
      idVendor: { type: "string" },
      password: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get PQ Template information",
    path: "/pq_template/:pqId",
    usecase: "vendorPQTemplateGetOne",
    tag: "prequalification_vendor",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get All PQ info",
    path: "/pq",
    usecase: "vendorPQGetAll",
    tag: "prequalification_vendor",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    query: {
      phase: { type: "string", enum: PreQualificationPhase },
      list: { type: "string", enum: ["pqInfo", "myPq"] }, // pqInfo | myPq
      title: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get PQ information",
    path: "/pq/:pqId",
    usecase: "vendorPQGetOne",
    tag: "prequalification_vendor",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get PQ Registration information",
    path: "/pq/:pqId/registration",
    usecase: "vendorPQRegistrationGetOne",
    tag: "prequalification_vendor",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Save PQ Registration",
    path: "/pq/:pqId/registration",
    usecase: "vendorPQRegistrationSubmit",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    tag: "prequalification_vendor",
    param: {
      pqId: { type: "string" },
    },
    body: {
      suratMinat: files,
      suratKuasaMinat: files,
      suratSPDA: files,
      dokumenDomisili: files,
      dokumenDomisiliDeskripsi: { type: "string" },
      suratIzinUsaha: files,
      sertifikatTKDN: files,
    },
  },
  {
    method: "put",
    description: "Update PQ Registration",
    path: "/pq/:pqId/registration",
    usecase: "vendorPQRegistrationUpdate",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    tag: "prequalification_vendor",
    param: {
      pqId: { type: "string" },
    },
    body: {
      suratMinat: files,
      suratKuasaMinat: files,
      suratSPDA: files,
      dokumenDomisili: files,
      dokumenDomisiliDeskripsi: { type: "string" },
      suratIzinUsaha: files,
      sertifikatTKDN: files,
    },
  },
  {
    method: "get",
    description: "Get PQ Requirement information",
    path: "/pq/:pqId/requirement",
    usecase: "vendorPQRequirementGetOne",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get PQ Meeting information",
    path: "/pq/:pqId/meeting",
    usecase: "vendorPQMeetingGetOne",
    tag: "prequalification_vendor",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get PQ Submission information",
    path: "/pq/:pqId/submission/:submissionPhase",
    usecase: "vendorPQSubmissionGetOne",
    security: "bearerAuthVendor",
    local: { vendorCIVDLogin },
    tag: "prequalification_vendor",
    param: {
      pqId: { type: "string" },
      submissionPhase: { type: "number" },
    },
  },
  {
    method: "put",
    description: "Save PQ Submission",
    path: "/pq/:pqId/submission/:submissionPhase",
    usecase: "vendorPQSubmissionSave",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
      submissionPhase: { type: "number" },
    },
    body: {
      submissionPayload: PQSubmissionPayload,
    },
  },
  {
    method: "post",
    description: "Submit PQ Submission",
    path: "/pq/:pqId/submission/:submissionPhase",
    usecase: "vendorPQSubmissionSubmit",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
      submissionPhase: { type: "number" },
    },
  },
  {
    method: "get",
    description: "Get PQ Evaluation information",
    path: "/pq/:pqId/evaluation",
    usecase: "vendorPQEvaluationGetOne",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Save PQ Evaluation",
    path: "/pq/:pqId/evaluation",
    usecase: "vendorPQEvaluationSubmit",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    body: {
      someValue: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get PQ Clarification information",
    path: "/pq/:pqId/clarification",
    usecase: "vendorPQClarificationGetOne",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Submit PQ Clarification",
    path: "/pq/:pqId/clarification",
    usecase: "vendorPQClarificationSubmit",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    local: { vendorCIVDLogin },
    param: {
      pqId: { type: "string" },
    },
    body: {
      clarificationPayload: PQSubmissionPayload,
      PQClarificationPayload,
    },
  },
  {
    method: "get",
    description: "Get All Vendor",
    path: "/vendor",
    usecase: "vendorVendorGetAll",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    query: {
      ...queryPageAndSize,
      id: { type: "string" },
      nameLike: { type: "string" },
    },
  },

  // ===== businessFieldLicense ======
  {
    method: "get",
    path: "/businessFieldLicense",
    usecase: "businessFieldLicenseGetAll",
    tag: "prequalification_vendor",
    security: "bearerAuthVendor",
    query: {
      ...queryPageAndSize,
      search: { type: "string" },
      documentName: { type: "string" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          code: { type: "string" },
        },
      },
    },
  },
  {
    method: "get",
    path: "/businessFieldLicenseDocuments",
    usecase: "businessFieldLicenseDocumentGetAll",
    tag: "prequalification_vendor",
    security: "bearerAuthVendor",
    query: {
      search: { type: "string" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          code: { type: "string" },
        },
      },
    },
  },

  // ===== vendoCIVD ======
  {
    method: "get",
    path: "/vendorCivd",
    usecase: "vendorCivdGetAll",
    tag: "prequalification_vendor",
    responseAsTable: true,
    security: "bearerAuthVendor",
    query: {
      ...queryPageAndSize,
      civdIds: arrayOfEnum,
      nameLike: { type: "string" },
      spdaValid: { type: "string" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          location: { type: "string" },
        },
      },
    },
  },
  {
    method: "get",
    path: "/vendorCivd/:civdVendorId",
    usecase: "vendorCivdGetOne",
    security: "bearerAuthVendor",
    tag: "prequalification_vendor",
    param: {
      civdVendorId: {
        type: "number",
      },
    },
  },
];
