import express from "express";
import multer from "multer";
import path from "path";
import { DataSource } from "typeorm";
import { createContext } from "vm";
import { createController } from "../../framework/core.js";
import { deleteFileFromPublicFolder, FileDownloadRequest, uploadFileToPublicFolder } from "../utility/fileupload.js";
import { downloadExcelAPP, downloadExcelRequisition } from "../utility/generate_worksheets.js";
import { deleteFileFromSharePoint, sanitizeFilename, tokenCookie, uploadFileToSharePoint } from "../utility/sharepoint.js";

export const handleFileOperation = (router: express.IRouter, ds: DataSource) => {
  return createController(["emptyUsecase"], () => {
    //
    router.get("/filedownloadexcel", async (req: express.Request, res: express.Response) => {
      const { ctx, ...request } = createContext(req);

      if (!ctx || !ctx.data || !ctx.data.userLogin) {
        return res.status(400).json({ error: "Cannot download excel file." });
      }

      try {
        const userLogin = ctx.data.userLogin;

        if (!request.query) {
          throw new Error("Request params cannot be empty.");
        }

        const params: FileDownloadRequest = request.query;

        if (!params.documentType) {
          throw new Error("Request params is invalid.");
        }

        if (!["PROC_PLAN_APP", "REQUISITION"].includes(params.documentType)) {
          throw new Error("documentType is invalid.");
        }

        let excelFile;

        if (params.documentType === "PROC_PLAN_APP") {
          excelFile = await downloadExcelAPP(ds, ctx, userLogin, params.year, params.departmentId);
        } else if (params.documentType === "REQUISITION") {
          excelFile = await downloadExcelRequisition(ds, ctx, userLogin, params.year, params.departmentId);
        }

        if (!excelFile) {
          throw new Error("Cannot download excel file.");
        }

        const filename = excelFile.filename.length > 255 ? excelFile.filename.substring(0, 255) : excelFile.filename;

        res.setHeader("Content-Disposition", `attachment; filename=${filename}`);
        res.setHeader("Content-Type", "*/*");
        res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        res.send(excelFile.buffer);

        return;
      } catch (error) {
        return res.json({ message: "Cannot download excel file: " + error });
        // throw new Error("Cannot download excel file.");
      }
    });

    router.post("/fileupload", multer().single("file"), async (req: express.Request, res: express.Response) => {
      const { path: uploadPath } = req.body;
      const file = req.file;

      if (!file) {
        return res.status(400).json({ error: "No file uploaded." });
      }

      if (!uploadPath) {
        return res.status(400).json({ error: "Path is required." });
      }

      try {
        const uniqueSuffix = (Date.now() + Math.floor(Math.random() * 1000000)).toString();
        const newFileName = sanitizeFilename(`${uniqueSuffix}_${file.originalname}`);

        if (process.env.APPLICATION_MODE && process.env.APPLICATION_MODE === "development") {
          await uploadFileToPublicFolder(file.buffer, newFileName, uploadPath);
        } else {
          const token = await tokenCookie(req, res);
          await uploadFileToSharePoint(file.buffer, newFileName, uploadPath, token);
        }

        const result = {
          path: path.join(uploadPath, newFileName),
          name: sanitizeFilename(file.originalname),
        };

        return res.json(result);
      } catch (error) {
        console.error("Error uploading file:", error);
        res.status(500).json({ message: "Failed to upload file to SharePoint." });
      }
    });

    router.post("/filedelete", async (req: express.Request, res: express.Response) => {
      const { path: filePath } = req.body;

      if (!filePath) {
        return res.status(400).json({ error: "Path is required." });
      }

      try {
        if (process.env.APPLICATION_MODE && process.env.APPLICATION_MODE === "development") {
          await deleteFileFromPublicFolder(filePath);
        } else {
          const token = await tokenCookie(req, res);
          await deleteFileFromSharePoint(filePath, token);
        }

        return res.json({ message: "File deleted successfully." });
      } catch (error) {
        console.error("Error deleting file:", error);
        res.status(200).json({ message: "Failed to delete file from SharePoint." });
      }
    });
  });
};
