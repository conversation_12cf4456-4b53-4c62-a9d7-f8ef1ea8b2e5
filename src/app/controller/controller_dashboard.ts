import { HTTPDataItem } from "../../framework/data_http.js";
import { userLogin } from "./_reused_properties.js";

export const controllerDashboard: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/dashboard/approval",
    usecase: "dashboardApproval",
    tag: "dashboard",
    security: "bearerAuth",
    local: { userLogin },
  },
  {
    method: "get",
    path: "/dashboard/procplan",
    usecase: "dashboardProcplan",
    tag: "dashboard",
    security: "bearerAuth",
    local: { userLogin },
  },
  {
    method: "get",
    path: "/dashboard/requisition",
    usecase: "dashboardRequisition",
    tag: "dashboard",
    security: "bearerAuth",
    local: { userLogin },
  },
];
