import express, { Router } from "express";
import jwt from "jsonwebtoken";
import { createController } from "../../framework/core.js";
import { crypt } from "@tawasukha/crypt";
import { generateID } from "../../framework/helper.js";
import { isUserAdmin, isUserAdminProcPlan, isUserAdminRequisition } from "../utility/helper.js";

export const handleLoginMicrosoft = (router: Router) => {
  //

  return createController(["userGetAll", "userEmailUpdate"], (x) => {
    //

    router.get("/cookie", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
      //

      const ctx = {
        data: {},
        date: new Date(),
        traceId: generateID(),
      };

      try {
        const user = getJSONName(req.cookies.hcmlSession); // {"isAuth":true,"name":"<PERSON><PERSON> Login","email":"<EMAIL>"}

        if (!user) {
          const err = new Error("undefined user");
          next(err);
          return;
        }

        let username: string = user?.email.toUpperCase();
        let name: string = user?.name.toUpperCase();
        let nameFromEmailArray = user?.email.split("@")[0].toUpperCase().split("_");
        let nameFromEmail = `${nameFromEmailArray[0]} ${nameFromEmailArray[nameFromEmailArray.length - 1]}`;

        if (username === "<EMAIL>") {
          username = "<EMAIL>";
        }

        let result = await x.userGetAll.inport(ctx, { emails: [username] });

        if (!result || !result.items || result.items.length === 0) {
          result = await x.userGetAll.inport(ctx, { nameLike: nameFromEmail, email: "" });
        }

        if (!result || !result.items || result.items.length === 0) {
          result = await x.userGetAll.inport(ctx, { nameLike: name, email: "" });
        }

        if (!result || !result.items || result.items.length === 0) {
          const err = new Error("user not found");
          next(err);
          return;
        }

        if (!result.items[0].email || result.items[0].email === "") {
          x.userEmailUpdate.inport(ctx, {
            userId: result.items[0].id,
            email: username,
          });

          result.items[0].email = username;
        }

        result.items[0].isAdmin = isUserAdmin(username);
        result.items[0].isAdminProcPlan = isUserAdminProcPlan(result.items[0]);
        result.items[0].isAdminRequisition = isUserAdminRequisition(result.items[0]);

        const payload = { data: result.items[0] };
        const secretKey = process.env.TOKEN_SECRET_KEY as string;
        const expiration = { expiresIn: process.env.TOKEN_EXPIRATION };
        const token = jwt.sign(payload, secretKey, expiration);

        res.cookie("token", token, {
          secure: true,
          maxAge: 1 * 60 * 1000,
        });

        res.redirect(process.env.APP_HOST_LOGIN as string); // URL login

      } catch (err) {
        next(err);
      }
    });

    router.get("/api/auth/login", (req, res) => {
      const encryptedPath = getCryptoURL().encrypt(`auth?redirect=${process.env.APP_HOST_SETCOOKIE}`);
      const url = `${process.env.API_LOGIN_AZURE}/${encryptedPath}`;
      console.log("/api/auth/login url", url);
      res.redirect(url); // URL login
    });

    router.get("/api/auth/logout", (req, res) => {
      const encryptedPath = getCryptoURL().encrypt(`logout?redirect=${process.env.APP_HOST_LOGOUT}`);
      const url = `${process.env.API_LOGIN_AZURE}/${encryptedPath}`;
      console.log("/api/auth/logout url", url);
      res.redirect(url); // URL logout
    });

    // router.get("/loginsuccess", (req, res) => res.send({ message: "Login Success" }));
    // router.get("/logoutsuccess", (req, res) => res.send({ message: "Logout Success" }));
  });
};

const getCryptoURL = () => {
  return crypt({
    secret: (process.env.API_SECRET_KEY as string).slice(0, 16),
    output: "base64url",
  });
};

export function getJSONName(cookieSession: string) {
  const crypto = getCryptoURL();

  let returnName;
  let sessionUser;
  if (cookieSession) {
    try {
      sessionUser = crypto.decrypt(cookieSession);
    } catch (err) {
      sessionUser = JSON.stringify({ isAuth: false });
    }
  } else {
    sessionUser = JSON.stringify({ isAuth: false });
  }
  sessionUser = JSON.parse(sessionUser);
  if (sessionUser.isAuth) {
    returnName = sessionUser;
  }
  return returnName;
}
