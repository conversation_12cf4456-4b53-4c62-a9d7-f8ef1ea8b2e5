import { HTTPDataItem } from "../../framework/data_http.js";
import { ObjectType } from "../../framework/data_type.js";
import { Business, BusinessClass, CompanyStatus, HighestExperienceScore, PreQualificationPhase, PrequalificationType } from "../model/vo.js";
import {
  BusinessFieldLicensesPayload,
  filesResultRemarks,
  PQMeetingInfoPayload,
  queryPageAndSize,
  RegistrationResultPayload,
  userLogin,
} from "./_reused_properties.js";

export const controllerPrequalification: HTTPDataItem[] = [
  //
  {
    method: "get",
    description: "Get All Vendor per PQ",
    path: "/pq/:pqId/vendor",
    usecase: "pqVendorGetAll",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    query: {
      ...queryPageAndSize,
      currentPhase: { type: "string", enum: PreQualificationPhase },
    },
  },
  {
    method: "get",
    description: "Generate Document",
    path: "/pq/:pqId/document/:document_code",
    usecase: "pqDocument",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      document_code: { type: "string", enum: ["DOC_A", "DOC_B", "DOC_C"] },
    },
  },
  {
    method: "get",
    description: "Get All PQ data",
    path: "/pq",
    usecase: "pqGetAll",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      as: { type: "string", enum: ["pic", "btb"] },
      assignedUserId: { type: "string" },
      keyword: { type: "string" },
      currentPhase: { type: "string", enum: PreQualificationPhase },
    },
  },
  {
    method: "get",
    description: "Get All PQ data",
    path: "/pq/:pqId",
    usecase: "pqGetOne",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    query: {
      headerOnly: { type: "boolean" },
    },
  },
  {
    method: "get",
    description: "Get One PQ Requirement",
    path: "/pq/:pqId/requirement",
    usecase: "pqRequirementGetOne",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "put",
    description: "Edit existing PQ Requirement",
    path: "/pq/:pqId/requirement",
    usecase: "pqRequirementUpdate",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    body: {
      payload: {
        type: "object",
        properties: {
          pqMeeting: { type: "boolean" },
          pqMeetingDate: { type: "date" },
          pqSubmissionDate: { type: "date" },
          otherInformations: {
            type: "array",
            items: {
              type: "object",
              properties: {
                text: { type: "string" },
                show: { type: "boolean" },
              },
            },
          },
          justification: { type: "string" },
          domicile: { type: "string" },
          companyStatus: { type: "string", enum: CompanyStatus },
          businessClass: { type: "string", enum: BusinessClass },
          businessType: { type: "string", enum: Business },
          prequalificationType: { type: "string", enum: PrequalificationType },
          invitedVendors: { type: "array", items: { type: "string" } },
          // businessLicense: { type: "string" },
          // businessFields: {
          //   type: "array",
          //   items: {
          //     type: "object",
          //     properties: {
          //       label: { type: "string" },
          //       value: { type: "string" },
          //     },
          //   },
          // },
          businessLicenses: BusinessFieldLicensesPayload,
          convertionRateUSDToIDR: { type: "number" },
          basicCapability: { type: "number" },
          highestExperienceScore: { type: "string", enum: HighestExperienceScore }, // 1/3 atau 1/5
          financialDueDiligence: { type: "boolean" },
        },
      },
    },
  },
  {
    method: "post",
    description: "Submit PQ Requirement",
    path: "/pq/:pqId/requirement/submit",
    usecase: "pqRequirementSubmit",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Approve PQ Requirement",
    path: "/pq/:pqId/requirement/approve",
    usecase: "pqRequirementApprove",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Sendback PQ Requirement",
    path: "/pq/:pqId/requirement/sendback",
    usecase: "pqRequirementSendback",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Complete PQ Registration Phase",
    path: "/pq/:pqId/registration/complete",
    usecase: "pqRegistrationComplete",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get Specific Vendor Registration info for specific PQ",
    path: "/pq/:vendorPqId/registration",
    usecase: "pqRegistrationGetOne",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      vendorPqId: { type: "string" },
    },
  },
  {
    method: "put",
    description: "Update Specific Vendor Registration info for specific PQ",
    path: "/pq/:vendorPqId/registration/update",
    usecase: "pqRegistrationUpdate",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      vendorPqId: { type: "string" },
    },
    body: {
      registrationResult: RegistrationResultPayload,
    },
  },
  {
    method: "post",
    description: "Approve Specific Vendor Registration for specific PQ",
    path: "/pq/:vendorPqId/registration/approve",
    usecase: "pqRegistrationApprove",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      vendorPqId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Sendback Specific Vendor Registration for specific PQ",
    path: "/pq/:vendorPqId/registration/sendback",
    usecase: "pqRegistrationSendback",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      vendorPqId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "get",
    description: "Get Meeting Info",
    path: "/pq/:pqId/meeting",
    usecase: "pqMeetingGetOne",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "put",
    description: "Update Meeting Info",
    path: "/pq/:pqId/meeting",
    usecase: "pqMeetingUpdate",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    body: {
      pqMeetingInfoPayload: PQMeetingInfoPayload,
    },
  },
  {
    method: "post",
    description: "Submit Meeting Info",
    path: "/pq/:pqId/meeting",
    usecase: "pqMeetingSubmit",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    body: {
      pqMeetingInfoPayload: PQMeetingInfoPayload,
    },
  },

  // === === === === === === === === === === === === === === === === === === === === ===

  {
    method: "get",
    description: "Get Evaluation Info",
    path: "/pq/:pqId/evaluation/vendor/:civdVendorId",
    usecase: "pqEvaluationGetOne",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      civdVendorId: { type: "number" },
    },
  },
  // {
  //   method: "put",
  //   description: "Update Evaluation Info",
  //   path: "/pq/:pqId/evaluation/vendor/:civdVendorId",
  //   usecase: "pqEvaluationUpdate",
  //   tag: "prequalification",
  //   security: "bearerAuth",
  //   local: { userLogin },
  //   param: {
  //     pqId: { type: "string" },
  //     civdVendorId: { type: "number" },
  //   },
  // },
  {
    method: "post",
    description: "Approve Evaluation",
    path: "/pq/:pqId/evaluation/:index/approve",
    usecase: "pqEvaluationApprove",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      index: { type: "string", enum: ["1", "2", "3"] },
    },
  },
  {
    method: "post",
    description: "Sendback Evaluation",
    path: "/pq/:pqId/evaluation/:index/sendback",
    usecase: "pqEvaluationSendback",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      index: { type: "string", enum: ["1", "2", "3"] },
    },
    body: {
      comment: { type: "string" },
    },
  },

  {
    method: "post",
    description: "Submit Evaluation",
    path: "/pq/:pqId/evaluation/:index/submit",
    usecase: "pqEvaluationSubmit",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      index: { type: "string", enum: ["1", "2", "3"] },
    },
    body: {
      comment: { type: "string" },
      response: { type: "string", enum: ["AD", "AI", "PF"] },
      dueDate: { type: "date" },
      failedProcess: { type: "string" },
      failedNotes: { type: "string" },
      meetingDates: { type: "array", items: { type: "object", properties: { civdVendorId: { type: "number" }, date: { type: "date" } } } },
    },
  },

  {
    method: "post",
    description: "Save Evaluation Vendor",
    path: "/pq/:pqId/evaluation/:index/save/:vendorPqId",
    usecase: "pqEvaluationVendorSave",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      vendorPqId: { type: "string" },
      index: { type: "string", enum: ["1", "2", "3"] },
    },
    body: {
      suratPernyataanPQ: filesResultRemarks,
      suratSPDA: filesResultRemarks,
      dokumenBuktiStatusPDN: filesResultRemarks,
      dokumenDomisili: filesResultRemarks,
      suratIzinUsaha: filesResultRemarks,
      sertifikatTKDN: filesResultRemarks,
      suratPerjanjianKonsorsium: filesResultRemarks,
      dokumenK3LL: filesResultRemarks,
      dokumenEvaluasiKemampuanFinansial: filesResultRemarks,
      summaryExperiences: filesResultRemarks,
      otherDocuments: {
        type: "array",
        items: filesResultRemarks as ObjectType,
      },
      // dokumenBuktiPKK1: filesResultRemarks,
      // dokumenBuktiPKK2: filesResultRemarks,
      // dokumenBuktiPKK3: filesResultRemarks,
    },
  },

  {
    method: "post",
    description: "Submit Evaluation Vendor",
    path: "/pq/:pqId/evaluation/:index/submit/:vendorPqId",
    usecase: "pqEvaluationVendorSubmit",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      vendorPqId: { type: "string" },
      index: { type: "string", enum: ["1", "2", "3"] },
    },
  },

  // === === === === === === === === === === === === === === === === === === === === ===

  {
    method: "get",
    description: "Get Clarification Info",
    path: "/pq/:pqId/clarification/vendor/:civdVendorId",
    usecase: "pqClarificationGetOne",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      civdVendorId: { type: "number" },
    },
  },
  {
    method: "put",
    description: "Update Clarification Info",
    path: "/pq/:pqId/clarification/vendor/:civdVendorId",
    usecase: "pqClarificationUpdate",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
      civdVendorId: { type: "number" },
    },
  },
  {
    method: "post",
    description: "Approve Clarification Info",
    path: "/pq/:pqId/clarification/approve",
    usecase: "pqClarificationApprove",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
  },
  {
    method: "post",
    description: "Sendback Clarification Info",
    path: "/pq/:pqId/clarification/sendback",
    usecase: "pqClarificationSendback",
    tag: "prequalification",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      pqId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
];
