import express from "express";
import { createController } from "../../framework/core.js";
import { convertReadableStream, getFileFromSharePoint, tokenCookie } from "../utility/sharepoint.js";
import { getFileFromPublicFolder } from "../utility/fileupload.js";

export const handleFilePublic = (router: express.IRouter) => {
  return createController(["emptyUsecase"], () => {
    router.get("/sharepoint", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
      try {
        if (process.env.APPLICATION_MODE && process.env.APPLICATION_MODE === "development") {
        } else {
          await tokenCookie(req, res);
        }
        return res.json();
      } catch (err) {
        next(err);
      }
    });

    router.get("/fileget", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
      try {
        const filePath = req.query.path as string;

        if (!filePath) {
          return res.status(400).json({ message: "File path is required" });
        }

        let fileResponse;

        if (process.env.APPLICATION_MODE && process.env.APPLICATION_MODE === "development") {
          fileResponse = await getFileFromPublicFolder(filePath);
        } else {
          fileResponse = await getFileFromSharePoint(filePath, await tokenCookie(req, res));
        }

        res.setHeader("Content-Type", fileResponse.headers.get("Content-Type") || "application/octet-stream");
        res.setHeader("Content-Disposition", `inline; filename="${filePath.split("/").pop()}"`);

        const stream = await convertReadableStream(fileResponse.body!);
        stream.pipe(res);
      } catch (err) {
        next(err);
      }
    });
  });
};
