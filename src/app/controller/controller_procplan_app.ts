import { HTTPDataItem } from "../../framework/data_http.js";
import { procPlanDetailPayload, queryPageAndSize, userLogin } from "./_reused_properties.js";

export const controllerProcplanApp: HTTPDataItem[] = [
  //
  {
    method: "post",
    path: "/procplan/app/header/:headerId/approve",
    usecase: "procplanAppActionApprove",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      headerId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/procplan/app/header/:headerId/sendback",
    usecase: "procplanAppActionSendback",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      headerId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/procplan/app/header/:headerId/sendbackToUpp",
    usecase: "procplanAppActionSendbackToUpp",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      headerId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/procplan/app/header/:headerId/submit",
    usecase: "procplanAppActionSubmit",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      headerId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/procplan/app/detail",
    usecase: "procplanAppDetailCreate",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    body: {
      procPlanDetail: procPlanDetailPayload,
    },
  },
  {
    method: "delete",
    path: "/procplan/app/detail/:detailId",
    usecase: "procplanAppDetailDelete",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      detailId: { type: "string" },
    },
  },
  {
    method: "get",
    path: "/procplan/app/detail/:detailId",
    usecase: "procplanAppDetailGetOne",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      detailId: { type: "string" },
    },
  },
  {
    method: "put",
    path: "/procplan/app/detail/:detailId",
    usecase: "procplanAppDetailUpdate",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      detailId: { type: "string" },
    },
    body: {
      procPlanDetail: procPlanDetailPayload,
    },
  },
  {
    method: "get",
    path: "/procplan/app/department",
    usecase: "procplanAppHeaderGetAll",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
    responseAsTable: true,
  },
  {
    method: "get",
    path: "/procplan/app/department/:departmentId",
    usecase: "procplanAppHeaderGetOne",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
    param: {
      departmentId: { type: "string" },
    },
  },
  {
    method: "get",
    path: "/procplan/app/header/:headerId",
    usecase: "procplanAppHeaderGetOne2",
    tag: "proc_plan_app",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
    param: {
      headerId: { type: "string" },
    },
  },
];
