import { HTTPDataItem } from "../../framework/data_http.js";
import { queryPageAndSize } from "./_reused_properties.js";

export const controllerPosition: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/position",
    usecase: "positionGetAll",
    tag: "position",
    security: "bearerAuth",
    query: {
      ...queryPageAndSize,
      id: { type: "string" },
    },
    responseAsTable: true,
    response: {
      200: {
        content: {
          name: { type: "string" },
          role: { type: "string" },
        },
      },
    },
  },
];
