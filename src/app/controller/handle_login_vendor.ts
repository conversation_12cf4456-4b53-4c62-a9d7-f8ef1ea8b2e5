import express from "express";
import jwt from "jsonwebtoken";
import { getRequestWithContext } from "../../framework/controller_express.js";
import { createController } from "../../framework/core.js";
import proxy from "express-http-proxy";

export const handleLoginVendor = (secretKey: string, router: express.IRouter) => {
  //

  return createController(["loginVendor", "loginVendorVera"], (x) => {
    //

    router.post("/login", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
      //

      const ctx = getRequestWithContext(req);

      try {
        const result = await x.loginVendor.inport(ctx, {
          idVendor: req.body.idVendor,
          password: req.body.password,
        });

        const payload = { data: result.user };
        const expiration = { expiresIn: process.env.TOKEN_EXPIRATION };
        // const token = jwt.sign(payload, secretKey, expiration);
        const token = jwt.sign(payload, secretKey as string, expiration);

        res.json({ token: token });
      } catch (err) {
        next(err);
      }
    });

    router.post("/loginVera", async (req: express.Request, res: express.Response, next: express.NextFunction) => {
      //
      const ctx = getRequestWithContext(req);

      try {
        const result = await x.loginVendorVera.inport(ctx, {
          email: req.body.email,
          password: req.body.password,
        });

        const payload = { data: result.vendor };
        const expiration = { expiresIn: process.env.TOKEN_EXPIRATION };
        const token = jwt.sign(payload, secretKey as string, expiration);

        res.json({ vendorName: result.vendor.name, token: token });
      } catch (err) {
        next(err);
      }
    });

    router.post(
      "/proxy/loginVera",
      proxy("103.52.115.149", {
        proxyReqPathResolver: () => "/proxy/loginVera",
        proxyReqBodyDecorator: (bodyContent, srcReq) => {
          // Customize body if needed before forwarding
          return bodyContent;
        },
        userResDecorator: async (proxyRes, proxyResData, userReq, userRes) => {

          const ctx = getRequestWithContext(userReq);

          try {
            const result = await x.loginVendorVera.inport(ctx, {
              email: userReq.body.email,
              password: userReq.body.password,
            });

            const payload = { data: result.vendor };
            const expiration = { expiresIn: process.env.TOKEN_EXPIRATION };
            const token = jwt.sign(payload, secretKey as string, expiration);

            userRes.json({ vendorName: result.vendor.name, token: token });
          } catch (err) {
            throw new Error(`error ${err}`);
          }

          return '';
        },
      })
    );

  });

  //
};
