import { HTTPDataItem } from "../../framework/data_http.js";
import { userLogin, documentStatus, procPlanDetailPayload, queryPageAndSize } from "./_reused_properties.js";

export const controllerProcplanUpp: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/procplan/:procPlanType",
    usecase: "procplanGetAll",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      procPlanType: { type: "string", enum: ["UPP", "APP"] },
      // procPlanCode: { type: "string" },
    },
    query: {
      ...queryPageAndSize,
      departmentId: { type: "string" },
      sectionId: { type: "string" },
      title: { type: "string" },
      status: documentStatus,
      year: { type: "number" },
      includeCurrentYear: { type: "boolean" },
      procPlanCode: { type: "string" },
      isHistory: { type: "boolean" },
    },
    responseAsTable: true,
  },
  {
    method: "post",
    path: "/procplan/upp/header/:headerId/approve",
    usecase: "procplanUppActionApprove",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    param: {
      headerId: { type: "string" },
    },
    local: { userLogin },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/procplan/upp/header/:headerId/sendback",
    usecase: "procplanUppActionSendback",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    param: {
      headerId: { type: "string" },
    },
    local: { userLogin },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/procplan/upp/header/:headerId/submit",
    usecase: "procplanUppActionSubmit",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    param: {
      headerId: { type: "string" },
    },
    local: {
      now: { funcName: "dateNow" },
      newAPPHeaderId: { funcName: "randomString" },
      userLogin: { funcName: "contextData", input: "userLogin" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/procplan/upp/detail",
    usecase: "procplanUppDetailCreate",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    local: { userLogin },
    body: {
      procPlanDetail: procPlanDetailPayload,
    },
  },
  {
    method: "delete",
    path: "/procplan/upp/detail/:detailId",
    usecase: "procplanUppDetailDelete",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    param: {
      detailId: { type: "string" },
    },
    local: { userLogin },
  },
  {
    method: "get",
    path: "/procplan/upp/detail/:detailId",
    usecase: "procplanUppDetailGetOne",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    param: {
      detailId: { type: "string" },
    },
    local: { userLogin },
  },
  {
    method: "put",
    path: "/procplan/upp/detail/:detailId",
    usecase: "procplanUppDetailUpdate",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    param: {
      detailId: { type: "string" },
    },
    local: { userLogin },
    body: {
      procPlanDetail: procPlanDetailPayload,
    },
  },
  {
    method: "get",
    path: "/procplan/upp/section",
    usecase: "procplanUppHeaderGetAll",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
    responseAsTable: true,
  },
  {
    method: "get",
    path: "/procplan/upp/section/:sectionId",
    usecase: "procplanUppHeaderGetOne",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      sectionId: { type: "string" },
    },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
  },
  {
    method: "get",
    path: "/procplan/upp/header/:headerId",
    usecase: "procplanUppHeaderGetOne2",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      headerId: { type: "string" },
    },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
  },
  {
    method: "get",
    path: "/procplan/upp/rbtb",
    usecase: "procplanUppHeaderGetOne3",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
  },

  {
    method: "get",
    path: "/procplan/upp/externaldepartment",
    usecase: "procplanUppHeaderGetOne4",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
    },
  },

  {
    method: "get",
    path: "/procplan/upp/detail/:detailId/externaldepartment",
    usecase: "procplanUppDetailGetOne2",
    tag: "proc_plan_upp",
    security: "bearerAuth",
    param: {
      detailId: { type: "string" },
    },
    local: { userLogin },
  },
];
