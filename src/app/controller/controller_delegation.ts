import { HTTPDataItem } from "../../framework/data_http.js";
import { userLogin } from "./_reused_properties.js";

export const controllerDelegation: HTTPDataItem[] = [
  //
  {
    method: "post",
    path: "/delegation",
    usecase: "delegationCreate",
    tag: "delegation",
    security: "bearerAuth",
    local: { userLogin },
    body: {
      delegateToUserId: { type: "string" },
      delegatorUserId: { type: "string" },
      startDate: { type: "date" },
      endDate: { type: "date" },
      type: { type: "string", enum: ["DELEGATION", "ACTING"] },
      remarks: { type: "string" },
    },
  },
];
