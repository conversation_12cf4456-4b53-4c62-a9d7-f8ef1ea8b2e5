import { HTTPDataItem } from "../../framework/data_http.js";
import { Consequence } from "../model/vo.js";
import { adminUpdateRequisitionPayload, userLogin, arrayOfEnum, documentStatus, queryPageAndSize, requisitionPayload } from "./_reused_properties.js";

export const controllerRequisition: HTTPDataItem[] = [
  //
  {
    method: "post",
    path: "/requisition/:requisitionId/approve",
    usecase: "requisitionActionApprove",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/requisition/:requisitionId/assign",
    usecase: "requisitionActionAssign",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      comment: { type: "string", default: "" },
      assignedUserId: { type: "string" },
      assignedBackToBackIds: arrayOfEnum,
    },
  },
  {
    method: "post",
    path: "/requisition/:requisitionId/sendback",
    usecase: "requisitionActionSendback",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/requisition/:requisitionId/submit",
    usecase: "requisitionActionSubmit",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      comment: { type: "string" },
    },
  },
  {
    method: "post",
    path: "/requisition",
    usecase: "requisitionCreate",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    body: {
      requisition: requisitionPayload,
    },
  },
  {
    method: "delete",
    path: "/requisition/:requisitionId",
    usecase: "requisitionDelete",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
  },
  {
    method: "get",
    path: "/requisition",
    usecase: "requisitionGetAll",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
      title: { type: "string" },
      departmentId: { type: "string" },
      sectionId: { type: "string" },
      status: documentStatus,
      assigned: { type: "boolean", default: false },
      assignedBTB: { type: "boolean", default: false },
      tenderCode: { type: "string" },
    },
    responseAsTable: true,
  },
  {
    method: "get",
    path: "/requisitionrbtb",
    usecase: "requisitionGetAllRbtb",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
      title: { type: "string" },
      departmentId: { type: "string" },
      status: documentStatus,
      tenderCode: { type: "string" },
    },
  },
  {
    method: "get",
    path: "/requisition/:requisitionId",
    usecase: "requisitionGetOne",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
  },
  {
    method: "put",
    path: "/requisition/:requisitionId",
    usecase: "requisitionUpdate",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      requisition: requisitionPayload,
    },
  },
  {
    method: "get",
    path: "/requisitionuserpic",
    usecase: "requisitionUserPIC",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      nameLike: { type: "string" },
    },
  },
  {
    method: "get",
    path: "/requisitionexternaldepartment",
    usecase: "requisitionExternalDepartmentGetAll",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    query: {
      ...queryPageAndSize,
      year: { type: "number", default: 2023 },
      title: { type: "string" },
      tenderCode: { type: "string" },
    },
  },
  {
    method: "put",
    path: "/requisition/:requisitionId/insurance",
    usecase: "requisitionInsuranceUpdate",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      userInsurance: { type: "object", properties: {} },
      legalInsurance: { type: "object", properties: {} },

      // isUseInsurance: { type: "boolean" },
      // insuranceRiskType: {
      //   type: "array",
      //   items: {
      //     type: "object",
      //     properties: {
      //       type: { type: "string" },
      //       useInsurance: { type: "boolean" },
      //       limit: { type: "string" },
      //       remarks: { type: "string" },
      //     },
      //   },
      // },
    },
  },
  {
    method: "put",
    path: "/requisition/:requisitionId/hserisk",
    usecase: "requisitionHseRiskUpdate",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      typeWork: { type: "string" },
      hseAssessment: { type: "string" },
      hseRiskCategory: {
        type: "array",
        items: {
          type: "object",
          properties: {
            areaOfRisk: { type: "string" },
            items: { type: "string" },
            remarks: { type: "string" },
            consequenceHuman: { type: "string", enum: Consequence },
            consequenceAsset: { type: "string", enum: Consequence },
            consequenceEnvironment: { type: "string", enum: Consequence },
            consequenceReputation: { type: "string", enum: Consequence },
          },
        },
      },
    },
  },
  {
    method: "put",
    path: "/requisition/:requisitionId/admin",
    usecase: "requisitionAdminUpdate",
    tag: "requisition",
    security: "bearerAuth",
    local: { userLogin },
    param: {
      requisitionId: { type: "string" },
    },
    body: {
      requisition: adminUpdateRequisitionPayload,
    },
  },
];
