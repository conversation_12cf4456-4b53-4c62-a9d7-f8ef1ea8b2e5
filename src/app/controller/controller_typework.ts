import { HTTPDataItem } from "../../framework/data_http.js";
import { queryPageAndSize } from "./_reused_properties.js";

export const controllerTypework: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/typework",
    usecase: "typeworkGetAll",
    tag: "typework",
    security: "bearerAuth",
    responseAsTable: true,
    query: {
      ...queryPageAndSize,
    },
    response: {
      200: {
        content: {
          typeWork: { type: "string" },
          riskLevel: { type: "string" },
          description: { type: "string" },
        },
      },
    },
  },
];
