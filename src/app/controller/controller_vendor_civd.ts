import { HTTPDataItem } from "../../framework/data_http.js";
import { arrayOfEnum, queryPageAndSize } from "./_reused_properties.js";

export const controllerVendorCIVD: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/vendorCivd",
    usecase: "vendorCivdGetAll",
    tag: "vendor_civd",
    responseAsTable: true,
    security: "bearerAuth",
    query: {
      ...queryPageAndSize,
      civdIds: arrayOfEnum,
      nameLike: { type: "string" },
      spdaValid: { type: "string" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          location: { type: "string" },
        },
      },
    },
  },
  {
    method: "get",
    path: "/vendorCivd/:civdVendorId",
    usecase: "vendorCivdGetOne",
    tag: "vendor_civd",
    security: "bearerAuth",
    param: {
      civdVendorId: {
        type: "number",
      },
    },
  },
];
