import { HTTPDataItem } from "../../framework/data_http.js";

export const controllerSettingValues: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/settingvalues",
    usecase: "settingValuesGet",
    tag: "setting_values",
    security: "bearerAuth",
    response: {
      200: {
        content: {
          yearlyRateUSDToIDR: { type: "string" },
          currentProcPlanYear: { type: "string" },
        },
      },
    },
    responseAsTable: true,
  },
  {
    method: "post",
    path: "/settingvalues",
    usecase: "settingValuesSave",
    tag: "setting_values",
    security: "bearerAuth",
    body: {
      yearlyRateUSDToIDR: { type: "string" },
      currentProcPlanYear: { type: "string" },
    },
  },
];
