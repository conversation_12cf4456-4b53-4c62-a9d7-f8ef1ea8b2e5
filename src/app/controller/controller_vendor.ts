import { HTTPDataItem } from "../../framework/data_http.js";
import { queryPageAndSize } from "./_reused_properties.js";

export const controllerVendor: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/vendor",
    usecase: "vendorGetAll",
    tag: "vendor",
    responseAsTable: true,
    security: "bearerAuth",
    query: {
      ...queryPageAndSize,
      id: { type: "string" },
      nameLike: { type: "string" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          location: { type: "string" },
        },
      },
    },
  },
  {
    method: "get",
    path: "/vendor/:vendorId",
    usecase: "vendorGetOne",
    tag: "vendor",
    param: {
      vendorId: {
        type: "string",
      },
    },
  },
];
