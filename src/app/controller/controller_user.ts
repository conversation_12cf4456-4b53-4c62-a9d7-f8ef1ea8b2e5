import { HTTPDataItem } from "../../framework/data_http.js";
import { arrayOfEnum, inputRole, queryPageAndSize } from "./_reused_properties.js";

export const controllerUser: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/user",
    usecase: "userGetAll",
    tag: "user",
    responseAsTable: true,
    security: "bearerAuth",
    query: {
      ...queryPageAndSize,
      ids: arrayOfEnum,
      emails: arrayOfEnum,
      nameLike: { type: "string" },
      sectionId: { type: "string" },
      supervisorPositionId: { type: "string" },
      departmentId: { type: "string" },
      role: inputRole,
      positionIds: arrayOfEnum,
      includeExecutive: { type: "boolean" },
      onlyIsSectionUser: { type: "boolean" },
      isRequestFor: { type: "boolean" },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          email: { type: "string" },
          supervisorPositionId: { type: "string" },
        },
      },
    },
  },
];
