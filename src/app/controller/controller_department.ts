import { HTTPDataItem } from "../../framework/data_http.js";
import { arrayOfString, queryPageAndSize, userLogin } from "./_reused_properties.js";

export const controllerDepartment: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/department",
    usecase: "departmentGetAll",
    tag: "department",
    security: "bearerAuth",
    query: {
      ...queryPageAndSize,
      ids: arrayOfString,
    },
    responseAsTable: true,
    response: {
      200: {
        content: {
          name: { type: "string" },
          code: { type: "string" },
        },
      },
    },
  },
  {
    method: "get",
    path: "/departmentbyuser",
    usecase: "departmentGetAllByUser",
    tag: "department",
    security: "bearerAuth",
    responseAsTable: true,
    local: { userLogin },
    query: {
      isApp: { type: "boolean", default: false },
      isRequisition: { type: "boolean", default: false },
    },
    response: {
      200: {
        content: {
          name: { type: "string" },
          code: { type: "string" },
        },
      },
    },
  },
];
