import { FuncType } from "../../framework/data_http.js";
import { InputType, ObjectType, StringType } from "../../framework/data_type.js";
import {
  Activity,
  ApprovalStatus,
  AuthorizationForExpenditureSchedule,
  Commodity,
  CommodityService,
  Consequence,
  Currency,
  DocumentStatus,
  DocumentTemplate,
  Incoterms,
  PlanCreated,
  PotentialDamageParty,
  PotentialDamageType,
  SubDocumentRequisition,
  TenderMethod,
  TwoDigitNumerical,
  TypeOf,
  UserRole,
  ValueRange,
  WorkProgramAndBudgetSchedule,
  WorkProgramReference,
  deskripsiDokumenDomisili,
  dokumenK3LL,
} from "../model/vo.js";

export type EnumInputType<T extends string> = {
  type: "string";
  enum: ReadonlyArray<T>;
};

export const documentType: StringType = {
  type: "string",
  enum: DocumentTemplate,
};

// export const contractType: StringType = {
//   type: "string",
//   enum: Contract,
// };

export const tenderMethod: StringType = {
  type: "string",
  enum: TenderMethod,
};

export const files: InputType = {
  type: "object",
  properties: {
    files: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "string" },
          file: { type: "string" },
        },
      },
    },
    url: { type: "string" },
  },
};

export const filesResultRemarks: InputType = {
  type: "object",
  properties: {
    result: { type: "string", enum: ["PASS", "FAIL", "UNDECIDED"] },
    remarks: { type: "string" },
    notes: { type: "string" },
  },
};

const setParty = (party: TypeOf<typeof PotentialDamageParty>): ObjectType => ({
  type: "object",
  properties: {
    party: { type: "string", default: party },
    damageType: {
      type: "array",
      items: [setDamageType("DEATH"), setDamageType("INJURY"), setDamageType("DISEASE"), setDamageType("PHYSICAL"), setDamageType("POLLUTION")],
    },
  },
});

const setDamageType = (damageType: TypeOf<typeof PotentialDamageType>): ObjectType => ({
  type: "object",
  properties: {
    type: { type: "string", default: damageType },
    potential: { type: "boolean" },
    description: { type: "string" },
    ...(damageType === "PHYSICAL" ? { value: { type: "number" }, currency: { type: "string", enum: Currency } } : {}),
  },
});

export const PotentialDamagePayload: InputType = {
  type: "array",
  items: [setParty("HCML"), setParty("CONTRACTOR"), setParty("THIRD_PARTY")],
};

export const ResultRemarks: InputType = {
  type: "object",
  properties: {
    file: files,
    result: { type: "string", enum: ["PASS", "FAIL", "UNDECIDED"] },
    remarks: { type: "string" },
    notes: { type: "string" },
    description: { type: "string", enum: deskripsiDokumenDomisili },
  },
};

export const Vendors: InputType = {
  type: "object",
  properties: {
    id: { type: "string" },
    name: { type: "string" },
    location: { type: "string" },
    accessApps: { type: "string" },
  },
};

export const CompanyEntity: InputType = {
  type: "object",
  properties: {
    companies: Vendors,
    companyStatus: { type: "string", enum: ["PERUSAHAAN DALAM NEGERI", "PERUSAHAAN NASIONAL", "PERUSAHAAN ASING"] },
    membership: { type: "string", enum: ["PEMUKA", "ANGGOTA"] },
  },
};

export const SummaryExperience: InputType = {
  type: "object",
  properties: {
    contractNo: { type: "string" },
    title: { type: "string" },
    businessFields: { type: "array", items: { type: "string" } },
    contractDate: { type: "date" },
    BASTDate: { type: "date" },
    currency: { type: "string", enum: Currency },
    contractValue: { type: "number" },
    ownerName: { type: "string" },
    ownerAddress: { type: "string" },
    contactPerson: { type: "string" },
    email: { type: "string" },
    contactNumber: { type: "string" },
    salinanKontrak: files,
    dokumenBAST: files,
    suratRingkasanPenagihan: files,
    dokumenKemampuanDasar: files,
  },
};

export const PurchaseRequisition: InputType = {
  type: "object",
  properties: {
    id: { type: "string" },
    prnumber: { type: "string" },
    item: { type: "string" },
    material: { type: "string" },
    materialdesc: { type: "string" },
  },
};

// files: { id: string; file: string }[];
// url: string;

export const commodity: StringType = {
  type: "string",
  enum: Commodity,
};

export const inputRole: StringType = {
  type: "string",
  enum: UserRole,
};

export const approvalTemplates: InputType = {
  type: "array",
  items: {
    type: "object",
    properties: {
      role: inputRole,
      users: {
        type: "object",
        properties: {
          id: { type: "string" },
        },
      },
      as: { type: "string" },
      subDocumentType: {
        type: "string",
        enum: SubDocumentRequisition,
      },
      rule: {
        type: "object",
        properties: {
          // oeValueRange: {
          //   type: "string",
          //   enum: ValueRange,
          // },
          // daValueRange: {
          //   type: "string",
          //   enum: ValueRange,
          // },
          documentType: documentType,
          commodity: commodity,
          commodityServiceType: {
            type: "string",
            enum: CommodityService,
          },
          isFirmCommitment: { type: "boolean" },
          // isUseInsurance: { type: "boolean" },
        },
      },
    },
  },
};

export const approvalStatus: StringType = { type: "string", enum: ApprovalStatus };

export const documentStatus: StringType = { type: "string", enum: DocumentStatus };

export const arrayOfString: InputType = { type: "array", items: { type: "string" } };

export const arrayOfEnum: InputType = { type: "array", items: { type: "string", enum: [] } };

export const twoDigitNumerical: StringType = {
  type: "string",
  enum: TwoDigitNumerical,
};

// export const headerAuthorization: StringType = {
//   type: "string",
//   textAreaLine: 2,
//   default:
//     "Basic eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiODAxODEzMjUiLCJuYW1lIjoiWVVMSUEgS0FSTkFJTiIsImVtYWlsIjoiWVVMSUFfS0FSTkFJTkBIQ01MLkNPLklEIiwic3VwZXJ2aXNvclBvc2l0aW9uSWQiOiI4MDE5MDEzNiIsImRlcGFydG1lbnQiOnsiaWQiOiI3MDIxMDAwMCIsIm5hbWUiOiJQcm9jdXJlbWVudCIsImNvZGUiOiIxNyJ9LCJzZWN0aW9uIjp7ImlkIjoiNzAyOTEwMDAiLCJuYW1lIjoiUHJvY3VyZW1lbnQgU2VydmljZXMgT3BlcmF0aW9uIiwiaXNTZWN0aW9uIjp0cnVlfSwicG9zaXRpb24iOnsiaWQiOiI4MDAxOTAzMiIsIm5hbWUiOiJMZWFkIEFuYWx5c3QgQ29udHJhY3QiLCJyb2xlIjoiU1RBRkYifX0sImlhdCI6MTcwODQzNjU1MSwiZXhwIjoxNzQ0NDMyOTUxfQ.HtarHvL9C-EwhQ7_v7xlsK4egZc9R0DPpnVyX4DokGw",
// };

// export const localUserLogin: FuncType = {
//   funcName: "contextData", input: "userLogin",
// };

// export const localNow: FuncType = {
//   funcName: "dateNow",
// };

// export const localRandom: FuncType = {
//   funcName: "randomString",
// };

export const queryPageAndSize: Record<string, InputType> = {
  page: { type: "number", default: 1 },
  size: { type: "number", default: 20 },
};

export const workProgramReferences: InputType = {
  type: "array",
  items: { type: "string", enum: WorkProgramReference },
};

export const workProgramAndBudget: InputType = {
  type: "array",
  items: {
    type: "object",
    properties: {
      year: { type: "string" },
      scheduleLines: {
        type: "array",
        items: {
          type: "object",
          properties: {
            schedule: { type: "string", enum: WorkProgramAndBudgetSchedule },
            lines: {
              type: "array",
              items: twoDigitNumerical,
            },
          },
        },
      },
    },
  },
};

export const authorizationForExpenditure: InputType = {
  type: "array",
  items: {
    type: "object",
    properties: {
      no: { type: "string" },
      scheduleLines: {
        type: "array",
        items: {
          type: "object",
          properties: {
            schedule: {
              type: "string",
              enum: AuthorizationForExpenditureSchedule,
            },
            lines: {
              type: "array",
              items: twoDigitNumerical,
            },
          },
        },
      },
    },
  },
};

export const procPlanDetailPayload: InputType = {
  type: "object",
  properties: {
    year: { type: "number" },
    departmentId: { type: "string" },
    sectionId: { type: "string" },
    requestForId: { type: "string" },
    requesterBackToBackIds: arrayOfEnum,
    commodity: commodity,
    createdType: { type: "string", enum: PlanCreated },
    carryOverProcPlanId: { type: "string" },
    carryOverProcPlanCode: { type: "string" },
    activityType: { type: "string", enum: Activity },
    title: { type: "string" },
    generalScopeOfWork: { type: "string" },
    tenderMethod: { type: "string", enum: TenderMethod },
    requisitionSubmissionDate: { type: "date" },
    tenderStartDate: { type: "date" },
    contractDateStart: { type: "date" },
    contractDateEnd: { type: "date" },
    poDateIssuance: { type: "date" },
    poDateDelivery: { type: "date" },
    currency: { type: "string", enum: Currency },
    valueEstimation: { type: "number" },
    valueInUSD: { type: "number" },
    localContentLevel: { type: "number" },
    estCurrYearExpenditure: { type: "number" },
    approvalAnnualBudget: { type: "number" },
    technicalMoMSubjectAndDate: { type: "array", items: { type: "string" } },
    workProgramReferences: workProgramReferences,
    workProgramAndBudget: workProgramAndBudget,
    authorizationForExpenditure: authorizationForExpenditure,
    remarks: { type: "string" },
    masterList: { type: "boolean" },
    durationType: { type: "string" },
    durationValue: { type: "string" },
  },
};

export const requisitionPayload: InputType = {
  type: "object",
  properties: {
    requestForId: { type: "string" },
    requesterBackToBackIds: arrayOfEnum,
    membersInvolvedIds: arrayOfEnum,
    procPlanDetailId: { type: "string" },
    isAdditionalProcPlan: { type: "boolean" },
    additionalProcPlanExplanation: { type: "array", items: { type: "string", enum: [] } },
    procPlanDetails: arrayOfEnum,
    title: { type: "string" },
    generalScopeOfWork: { type: "string" },
    justificationOfRequiredWorks: { type: "string" },
    commodity: commodity,
    tenderMethod: tenderMethod,
    // contractType: contractType,
    // contractTypeOthers: { type: "string" },
    incoterms: { type: "string", enum: Incoterms },
    importation: { type: "boolean" },
    localContentLevel: { type: "number" },
    workOfLocation: { type: "array", items: { type: "string", enum: [] } },
    contractDateStart: { type: "date" },
    contractDateEnd: { type: "date" },
    poDateIssuance: { type: "date" },
    poDateDelivery: { type: "date" },
    durationType: { type: "string" },
    durationValue: { type: "string" },
    purchaseRequisition: { type: "array", items: PurchaseRequisition },
    tenderCode: { type: "string" },

    currency: { type: "string", enum: Currency },
    value: { type: "number" },

    procPlanCurrency: { type: "string", enum: Currency },
    procPlanValue: { type: "number" },

    budgetOwners: arrayOfString,
    budgetOwnerDetails: arrayOfEnum,

    oeReference: { type: "object", properties: {} },

    // reference: arrayOfEnum,
    // ownerEstimationExplanation: { type: "string" },
    // breakdownFiles: files,
    // referenceFiles: files,

    typeWork: { type: "string" },
    hseAssessment: { type: "string" },
    hseRiskCategory: {
      type: "array",
      items: {
        type: "object",
        properties: {
          areaOfRisk: { type: "string" },
          items: { type: "string" },
          remarks: { type: "string" },
          consequenceHuman: { type: "string", enum: Consequence },
          consequenceAsset: { type: "string", enum: Consequence },
          consequenceEnvironment: { type: "string", enum: Consequence },
          consequenceReputation: { type: "string", enum: Consequence },
        },
      },
    },

    userInsurance: { type: "object", properties: {} },
    legalInsurance: { type: "object", properties: {} },

    tenderBidder: { type: "object", properties: {} },
    supportingDocuments: { type: "object", properties: {} },

    // isUseInsurance: { type: "boolean" },
    // insuranceRiskType: {
    //   type: "array",
    //   items: {
    //     type: "object",
    //     properties: {
    //       type: { type: "string" },
    //       useInsurance: { type: "boolean" },
    //       limit: { type: "string" },
    //       remarks: { type: "string" },
    //     },
    //   },
    // },

    // previousSimilarContract: { type: "string" },
    // potentialDamage: PotentialDamagePayload,
    // typeLimitInsurance: { type: "string" },
    // typeInsuranceNote: { type: "string" },

    // isDirectAppointment: { type: "boolean" },

    // daChecklist: { type: "array", items: { type: "string" } },
    // daPotentialVendorCompanies: { type: "string" },

    // daJustifications: {
    //   type: "array",
    //   items: {
    //     type: "object",
    //     properties: { title: { type: "string" }, content: { type: "string" } },
    //   },
    // },
    // daExplanation: { type: "string" },
    // daPotentialVendorJustification: { type: "string" },
    // daPotentialVendorCompany: { type: "object", properties: { id: { type: "string" }, name: { type: "string" } } },
    // daIsCertainWork: { type: "boolean" },
    // daSupportingDocumentsFiles: files,

    // tmwsmFiles: files,
    // tmweFiles: files,
    // sowFiles: files,
    // aprFiles: files,
    // tecFiles: files,
    // sorFiles: files,
    // slaKpiFiles: files,

    // isBigValue: { type: "boolean" },
  },
};

export const RegistrationResultPayload: InputType = {
  type: "object",
  properties: {
    suratMinat: filesResultRemarks, // registration a
    // suratKuasaMinat?: filesResultRemarks | null; // registration a
    suratSPDA: filesResultRemarks, // registration b
    dokumenDomisili: filesResultRemarks, // registration c
    suratIzinUsaha: filesResultRemarks, // registration d
    sertifikatTKDN: filesResultRemarks, // registration e
  },
};

export const PQMeetingInfoPayload: InputType = {
  type: "object",
  properties: {
    place: { type: "string" },
    deadlineForSubmission: { type: "string" },
    minutesOfMeeting: { type: "array", items: { type: "string" } },
    invitedVendorsPqMeeting: {
      type: "array",
      items: {
        type: "object",
        properties: {
          vendor: {
            type: "object",
            properties: {
              id: { type: "string" },
              name: { type: "string" },
            },
          },
          title: { type: "string" },
          name: { type: "string" },
        },
      },
    },
    file: files,
  },
};

export const PQOtherDocuments: InputType = {
  type: "object",
  properties: {
    text: { type: "string" },
    show: { type: "boolean" },
    file: files,
  },
};

export const PQSubmissionPayload: InputType = {
  type: "object",
  properties: {
    entity: { type: "string", enum: ["CONSORTIUM", "SINGLE"] }, // general information
    companyEntities: { type: "array", items: CompanyEntity }, // general information, bila entity = single hanya ada 1 data perusahaan ; bila entity = consortium, pemuka harus ada 1 dan tidak boleh lebih dari 1
    suratPernyataanPQ: files, // administration documents  a
    suratKuasaPernyataanPQ: files, // administration documents  a
    suratSPDA: files, // administration documents  b
    dokumenBuktiStatusPDN: files, // administration documents  c
    dokumenDomisili: files, // administration documents  d
    dokumenDomisiliDeskripsi: { type: "string", enum: deskripsiDokumenDomisili }, // administration documents  d
    suratIzinUsaha: files, // administration documents  e
    sertifikatTKDN: files, // administration documents  f
    suratPerjanjianKonsorsium: files, // administration documents  g
    dokumenK3LL: files, // technical documents a
    dokumenK3LLDeskripsi: { type: "string", enum: dokumenK3LL }, // technical documents a
    summaryExperiences: { type: "array", items: SummaryExperience }, // technical documents b
    dokumenEvaluasiKemampuanFinansial: files, // financing documents a
    dokumenLaporanKeuangan: files, // financing documents a
    otherDocuments: { type: "array", items: PQOtherDocuments },
    // dokumenBuktiPKK1: files, // other documents a, Dokumen bukti pengalaman kerja khusus 1
    // dokumenBuktiPKK2: files, // other documents b, Dokumen bukti pengalaman kerja khusus 2
    // dokumenBuktiPKK3: files, // other documents c, Dokumen bukti pengalaman kerja khusus 3
  },
};

export const PQClarificationPayload: InputType = {
  type: "object",
  properties: {
    file: files,
    date: { type: "date" },
    number: { type: "string" },
    subject: { type: "string" },
  },
};

export const BusinessFieldLicensesPayload: InputType = {
  type: "array",
  items: {
    type: "object",
    properties: {
      license: { type: "string" },
      fields: {
        type: "array",
        items: {
          type: "object",
          properties: {
            id: { type: "string" },
            documentName: { type: "string" },
            categoryCode: { type: "string" },
            clasification: { type: "string" },
          },
        },
      },
    },
  },
};

export const adminUpdateRequisitionPayload: InputType = {
  type: "object",
  properties: {
    // Exclude these fields from requisitionPayload
    // requestForId: { type: "string" },
    // requesterBackToBackIds: arrayOfEnum,
    // membersInvolvedIds: arrayOfEnum,
    // departmentCode: { type: "string" },
    // sectionId: { type: "string" },

    procPlanDetailId: { type: "string" },
    isAdditionalProcPlan: { type: "boolean" },
    additionalProcPlanExplanation: { type: "array", items: { type: "string", enum: [] } },
    procPlanDetails: arrayOfEnum,
    title: { type: "string" },
    generalScopeOfWork: { type: "string" },
    justificationOfRequiredWorks: { type: "string" },
    commodity: commodity,
    tenderMethod: tenderMethod,
    incoterms: { type: "string", enum: Incoterms },
    importation: { type: "boolean" },
    localContentLevel: { type: "number" },
    workOfLocation: { type: "array", items: { type: "string", enum: [] } },
    contractDateStart: { type: "date" },
    contractDateEnd: { type: "date" },
    poDateIssuance: { type: "date" },
    poDateDelivery: { type: "date" },
    durationType: { type: "string" },
    durationValue: { type: "string" },
    purchaseRequisition: { type: "array", items: PurchaseRequisition },
    tenderCode: { type: "string" },

    currency: { type: "string", enum: Currency },
    value: { type: "number" },

    procPlanCurrency: { type: "string", enum: Currency },
    procPlanValue: { type: "number" },

    budgetOwners: arrayOfString,
    budgetOwnerDetails: arrayOfEnum,

    oeReference: { type: "object", properties: {} },

    typeWork: { type: "string" },
    hseAssessment: { type: "string" },
    hseRiskCategory: {
      type: "array",
      items: {
        type: "object",
        properties: {
          areaOfRisk: { type: "string" },
          items: { type: "string" },
          remarks: { type: "string" },
          consequenceHuman: { type: "string", enum: Consequence },
          consequenceAsset: { type: "string", enum: Consequence },
          consequenceEnvironment: { type: "string", enum: Consequence },
          consequenceReputation: { type: "string", enum: Consequence },
        },
      },
    },

    userInsurance: { type: "object", properties: {} },
    legalInsurance: { type: "object", properties: {} },

    tenderBidder: { type: "object", properties: {} },
    supportingDocuments: { type: "object", properties: {} },
  },
};

export const userLogin: FuncType = { funcName: "contextData", input: "userLogin" };
export const vendorCIVDLogin: FuncType = { funcName: "contextData", input: "vendorCIVDLogin" };
