import { HTTPDataItem } from "../../framework/data_http.js";
import { approvalTemplates, documentType } from "./_reused_properties.js";

export const controllerApprovalTemplate: HTTPDataItem[] = [
  //
  {
    method: "get",
    path: "/approvaltemplates",
    usecase: "approvalTemplateGetAll",
    tag: "approval_templates",
    query: { documentType: documentType },
    security: "bearerAuth",
    response: {
      200: {
        content: {
          id: { type: "string" },
          sequence: { type: "number" },
          documentType: { type: "string" },
          approvalTemplates,
        },
      },
    },
    responseAsTable: true,
  },
  {
    method: "post",
    path: "/approvaltemplates",
    usecase: "approvalTemplateCreate",
    tag: "approval_templates",
    security: "bearerAuth",
    body: {
      documentType: documentType,
      approvalTemplateGroups: {
        type: "array",
        items: {
          type: "object",
          properties: {
            approvalTemplates,
          },
        },
      },
    },
  },
];
