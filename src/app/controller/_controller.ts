import { HTTPDataItem } from "../../framework/data_http.js";
import { controllerApproval } from "./controller_approval.js";
import { controllerApprovalTemplate } from "./controller_approval_template.js";
import { controllerDepartment } from "./controller_department.js";
import { controllerHello } from "./controller_hello.js";
import { controllerPosition } from "./controller_position.js";
import { controllerPrequalification } from "./controller_pq.js";
import { controllerProcplanApp } from "./controller_procplan_app.js";
import { controllerProcplanUpp } from "./controller_procplan_upp.js";
import { controllerRequisition } from "./controller_requisition.js";
import { controllerSection } from "./controller_section.js";
import { controllerTypework } from "./controller_typework.js";
import { controllerUser } from "./controller_user.js";
import { controllerVendor } from "./controller_vendor.js";
import { controllerVendorCIVD } from "./controller_vendor_civd.js";
import { controllerPrItem } from "./controller_pr_item.js";
import { controllerMailTest } from "./controller_mailtest.js";
import { controllerSettingValues } from "./controller_setting_values.js";
import { controllerDashboard } from "./controller_dashboard.js";
import { controllerBusinessFieldLicense } from "./controller_business_field_license.js";
import { controllerDelegation } from "./controller_delegation.js";

export const controllerCollection: HTTPDataItem[] = [
  //
  // ...controllerHello,
  ...controllerDashboard,
  ...controllerUser,
  ...controllerApproval,
  ...controllerProcplanUpp,
  ...controllerProcplanApp,
  ...controllerRequisition,
  ...controllerPrequalification,
  ...controllerDepartment,
  ...controllerPosition,
  ...controllerSection,
  ...controllerApprovalTemplate,
  ...controllerTypework,
  ...controllerVendor,
  ...controllerPrItem,
  ...controllerMailTest,
  ...controllerSettingValues,
  ...controllerVendorCIVD,
  ...controllerBusinessFieldLicense,
  ...controllerDelegation,
];
