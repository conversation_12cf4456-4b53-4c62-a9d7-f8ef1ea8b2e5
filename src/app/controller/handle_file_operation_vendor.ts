import express from "express";
import multer from "multer";
import path from "path";
import { createController } from "../../framework/core.js";
import { deleteFileFromPublicFolder, uploadFileToPublicFolder } from "../utility/fileupload.js";
import { deleteFileFromSharePoint, sanitizeFilename, tokenCookie, uploadFileToSharePoint } from "../utility/sharepoint.js";

export const handleFileOperationVendor = (router: express.IRouter) => {
  return createController(["emptyUsecase"], () => {
    router.post("/fileupload", multer().single("file"), async (req: express.Request, res: express.Response) => {
      const { path: uploadPath } = req.body;
      const file = req.file;

      if (!file) {
        return res.status(400).json({ error: "No file uploaded." });
      }

      if (!uploadPath) {
        return res.status(400).json({ error: "Path is required." });
      }

      try {
        const uniqueSuffix = (Math.floor(Date.now() / 1000) + Math.floor(Math.random() * 100)).toString();
        const newFileName = sanitizeFilename(`${uniqueSuffix}_${file.originalname}`);

        if (process.env.APPLICATION_MODE && process.env.APPLICATION_MODE === "development") {
          await uploadFileToPublicFolder(file.buffer, newFileName, uploadPath);
        } else {
          const token = await tokenCookie(req, res);
          await uploadFileToSharePoint(file.buffer, newFileName, uploadPath, token);
        }

        const result = {
          path: path.join(uploadPath, newFileName),
          name: sanitizeFilename(file.originalname),
        };

        return res.json(result);
      } catch (error) {
        console.error("Error uploading file:", error);
        res.status(500).json({ message: "Failed to upload file to SharePoint." });
      }
    });

    router.post("/filedelete", async (req: express.Request, res: express.Response) => {
      const { path: filePath } = req.body;

      if (!filePath) {
        return res.status(400).json({ error: "Path is required." });
      }

      try {
        if (process.env.APPLICATION_MODE && process.env.APPLICATION_MODE === "development") {
          await deleteFileFromPublicFolder(filePath);
        } else {
          const token = await tokenCookie(req, res);
          await deleteFileFromSharePoint(filePath, token);
        }

        return res.json({ message: "File deleted successfully." });
      } catch (error) {
        console.error("Error deleting file:", error);
        res.status(200).json({ message: "Failed to delete file from SharePoint." });
      }
    });
  });
};
