import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { Approval, FindApprovalGroup, FindApprovalGroupNative, getLastApprovalGroupDurationDays } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { UserLogin } from "../model/model_user.js";
import { ApprovalStatus, DateNowHandler, TypeOf } from "../model/vo.js";

class Gateways {
  findApprovalGroupNative: FindApprovalGroupNative;
  findApprovalGroup: FindApprovalGroup;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  relatedUserApproval: boolean;
  delegationUserApproval: boolean;
}

export class Response extends InputResponseWithCount<Approval> { }

export const approvalProcessing: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // 
    let status: TypeOf<typeof ApprovalStatus> = "PROCESSING";

    if (req.relatedUserApproval) {
      status = "DONE";
    }

    const [items] = await o.findApprovalGroupNative(ctx, { 
      ...req, 
      status, 
      userId: req.userLogin.id, 
      positionId: req.userLogin.position?.id, 
      delegationUserApproval: req.delegationUserApproval 
    });
    
    const newItems = items.filter((item) => item.sequence! > 1 || item.documentType === "PQ_REGISTRATION");

    if (newItems.length > 0) {
      // 
      let result = newItems;

      if (req.relatedUserApproval) {
        // 
        result = Object.values(
          newItems.reduce((acc, item) => {
            const key = `${item.documentId}-${item.documentType}`;
            if (!acc[key]) {
              acc[key] = item;
            }
            return acc;
          }, {} as Record<string, typeof newItems[number]>)
        );
      }

      result = await Promise.all(await result.map(async (item) => {
        item.durationDays = (await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, item, await o.dateNow(ctx), o.findCalendar));
        return item;
      }));

      return { items: result, count: newItems.length };
    }

    return { items: [], count: 0 };
  },
};
