import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindSection, FindSectionFilter, Section } from "../model/model_section.js";

class Gateways {
  findSection: FindSection;
}

export class Request extends FindSectionFilter {}

export class Response extends InputResponseWithCount<Section> {}

export const sectionGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findSection(ctx, req);
    return { items, count };
  },
};
