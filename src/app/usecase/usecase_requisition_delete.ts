import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups } from "../model/model_approval.js";
import { DeleteRequisition, FindRequisition } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";

class Gateways {
  findRequisition: FindRequisition;
  deleteRequisition: DeleteRequisition;
  deleteApprovalGroups: DeleteApprovalGroups;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
}

export class Response { }

export const requisitionDelete: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [rqs] = await o.findRequisition(ctx, { id: req.requisitionId });
    const rq = rqs.length > 0 ? rqs[0] : null;
    if (!rq) {
      throw new Error("requisition not found");
    }

    if (rq.status !== "DRAFT") {
      throw new Error("requisition must in DRAFT state");
    }

    await o.deleteRequisition(ctx, { id: rq.id! });

    await o.deleteApprovalGroups(ctx, { documentId: rq.id! });

    return {};
  },
};
