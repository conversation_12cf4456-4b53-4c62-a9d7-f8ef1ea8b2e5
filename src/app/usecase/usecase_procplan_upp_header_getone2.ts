import { Usecase } from "../../framework/core.js";
import { BaseFindManyFilter } from "../../framework/repository.js";
import { FindDelegation } from "../model/model_delegation.js";
import { FindProcPlanDetail, FindProcPlanHeader, ProcPlanHeader } from "../model/model_procplan.js";
import { UserLogin } from "../model/model_user.js";
import { Currency, EligibilityAction, TenderMethod, TypeOf, getEmptyValueCurrency, validateEligibilityUPPAction } from "../model/vo.js";

class Gateways {
  findProcPlanHeader: FindProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findDelegation: FindDelegation;
}

export class Request extends BaseFindManyFilter {
  userLogin: UserLogin;
  headerId?: string;
  year: number;
}

export class Response {
  procPlanHeader: (ProcPlanHeader & { details: Detail[] }) | null;
  eligibility: EligibilityAction;
}

export const procplanUppHeaderGetOne2: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [procPlanHeaders] = await o.findProcPlanHeader(ctx, {
      year: req.year,
      id: req.headerId,
      procPlanType: "UPP",
    });

    const procPlanHeader = procPlanHeaders.length > 0 ? procPlanHeaders[0] : null;

    const [procPlanDetails] = await o.findProcPlanDetail(ctx, {
      procPlanHeaderId: procPlanHeader?.id,
      page: req.page,
      size: req.size,
    });

    const procPlanDetail = procPlanDetails.length > 0 ? procPlanDetails[0] : null;

    const userIDs: string[] = [
      //
      ...(procPlanDetails.map((x) => x.creator?.id) as string[]),
      ...(procPlanDetails.map((x) => x.requestFor?.id) as string[]),
      ...(procPlanDetails.flatMap((x) => x.requesterBackToBack.map((x) => x.id)) as string[]),
    ];

    const [delegations] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "DELEGATION" });
    const eligibility = validateEligibilityUPPAction(procPlanHeader?.status!, req.userLogin, userIDs, procPlanHeader?.approvalGroup!, null, null, delegations[0]);

    // TODO validate that other user that can see this page such as
    // non executive users that still in the same department
    // executive users who is involved in the approval process

    // TODO test the executive user who is not involve in the approval process
    // TODO test the non executive user who is not in the same department
    // TODO try to test the potential issue. how about non structural user who is mention in approval list ?
    // so far there is no non structural user (cases) defined in UPP

    if (!procPlanHeader || !procPlanDetail) {
      return {
        procPlanHeader: {
          totalValueEstimation: getEmptyValueCurrency(),
          id: "",
          procPlanType: "UPP",
          status: "NOT_CREATED",
          isSendBack: false,
          submittedDate: null,
          year: 0,
          count: 0,
          section: null,
          department: null,
          approvalGroup: null,
          submitter: null,
          details: [],
          sections: [],
        },
        eligibility,
      };
    }

    // if (procPlanDetail?.department?.id !== req.userLogin.department?.id) {
    //   //
    //   const [users] = await findUserSupervisors(ctx, procPlanDetail?.requestFor?.supervisorPositionId!);
    //   if (!users.some((user) => user.id === req.userLogin.id)) {
    //     return { procPlanHeader: null };
    //   }
    // }

    return {
      procPlanHeader: {
        ...procPlanHeader,
        details: procPlanDetails.map(
          (x) =>
          ({
            id: x.id,
            title: x.title,
            tenderMethod: x.tenderMethod,
            contractDateStart: x.contractDateStart,
            contractDateEnd: x.contractDateEnd,
            poDateIssuance: x.poDateIssuance,
            poDateDelivery: x.poDateDelivery,
            currency: x.currency,
            valueEstimation: x.valueEstimation,
            localContentLevel: x.localContentLevel,
            canModify: false,
            masterList: x.masterList,
          } as Detail)
        ),
      },
      eligibility,
    };
  },
};

type Detail = {
  id?: string;
  title?: string;
  tenderMethod?: TypeOf<typeof TenderMethod>;
  contractDateStart?: Date | null;
  contractDateEnd?: Date | null;
  poDateIssuance?: Date | null;
  poDateDelivery?: Date | null;
  currency?: TypeOf<typeof Currency>;
  valueEstimation?: number;
  localContentLevel?: number;
  sequenceId?: string;
  canModify?: boolean;
  masterList?: boolean;
};
