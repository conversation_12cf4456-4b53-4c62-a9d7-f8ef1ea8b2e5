import { Usecase } from "../../framework/core.js";
import nodemailer, { SentMessageInfo } from "nodemailer";
import { approvalMailTemplate } from "../utility/mailer.js";
import { MailTableData, sendNotificationMail } from "../utility/mailer_2.js";
import { formatNumber } from "../model/vo.js";

class Gateways { }

export class Request { }

export class Response {
  message: string;
}

export const mailTest: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // Create a transporter object
    // const transporter = nodemailer.createTransport({
    //   host: "hcml-co-id.mail.protection.outlook.com", // Prisa
    //   port: 25,
    //   secure: false,
    // });

    // const transporter = nodemailer.createTransport({
    //   host: "sandbox.smtp.mailtrap.io", // Mailtrap
    //   port: 587,
    //   secure: false,
    //   auth: {
    //     user: "8b1b5d8dcce548",
    //     pass: "ac87d777f4ca2e",
    //   },
    // });

    // Define the email options
    // const mailOptions = {
    //   from: '"Test Mail HCML" <<EMAIL>>', // sender address
    //   to: "<EMAIL>", // list of receivers
    //   subject: "Hello from TypeScript", // Subject line
    //   text: "This is a test email sent from a TypeScript script.", // plain text body
    //   html: approvalMailTemplate("<EMAIL>", "Hello from TypeScript", []), // html body
    // };

    // Send the email
    // transporter.sendMail(mailOptions, (error: Error | null, info: SentMessageInfo) => {
    //   if (error) {
    //     // Log the error message
    //     console.error("Error occurred while sending email:", error.message);
    //     // Log the full error object for debugging
    //     console.error("Full error:", error);
    //   } else {
    //     console.log("Message sent successfully:", info.messageId);
    //     console.log("Preview URL:", nodemailer.getTestMessageUrl(info));
    //   }
    // });

    sendNotificationMail({
      sendToUserMail: "<EMAIL>",
      sendToUserName: "<EMAIL>",
      ccUserMail: "<EMAIL>,<EMAIL>",
      submittedByUserName: "<EMAIL>"
    }, [{
      title: "Test Title 1",
      department: "Test Department 1",
      section: "Test Section 1",
      quantity: 10,
      value: formatNumber(100000),
      docId: "12345678",
    }], "REQUISITION");
    
    sendNotificationMail({
      sendToUserMail: "<EMAIL>",
      sendToUserName: "<EMAIL>",
      ccUserMail: ["<EMAIL>", "<EMAIL>"],
      submittedByUserName: "<EMAIL>"
    }, [{
      title: "Test Title 2",
      department: "Test Department 2",
      section: "Test Section 2",
      quantity: 20,
      value: formatNumber(200000),
      docId: "12345678",
    }], "REQUISITION");
    
    sendNotificationMail({
      sendToUserMail: "<EMAIL>",
      sendToUserName: "<EMAIL>",
      ccUserMail: [{ name: 'DOMINO', address: '<EMAIL>' }, { name: 'SUGENG MULYONO', address: '<EMAIL>' }],
      submittedByUserName: "<EMAIL>"
    }, [{
      title: "Test Title 3",
      department: "Test Department 3",
      section: "Test Section 3",
      quantity: 30,
      value: formatNumber(300000),
      docId: "12345678",
    }], "REQUISITION");

    return {
      message: "Waw",
      mode: process.env.APPLICATION_MODE,
      employee: process.env.APP_EMPLOYEE_BASE_URL,
    };
  },
};
