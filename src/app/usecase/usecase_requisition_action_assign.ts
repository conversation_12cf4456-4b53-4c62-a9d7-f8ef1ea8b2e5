import { Usecase } from "../../framework/core.js";
import {
  DeleteApprovalGroups,
  FindApprovalGroup,
  SaveApprovalGroups,
  getApproval,
  getApprovalWithRequestForRuleList,
  moveApproval,
  validateApprovalAction
} from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindCalendar } from "../model/model_calendar.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { PrequalificationTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { FindProcPlanDetail, SaveProcPlanDetail } from "../model/model_procplan.js";
import { FindRequisition, getCcEmailRequisition, getEmailDataRequisition, SaveRequisition, setRequisitionAssignTenderCode } from "../model/model_requisition.js";
import { FindUser, FindUserSupervisors, UserLogin } from "../model/model_user.js";
import { DateNow<PERSON>and<PERSON>, RandomStringHandler } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly, toUSD } from "../utility/helper.js";
import { MailTableData, sendAssignmentMail, sendNotificationMail } from "../utility/mailer_2.js";

class Gateways {
  findRequisition: FindRequisition;
  findApprovalGroup: FindApprovalGroup;
  findUser: FindUser;
  findProcPlanDetail: FindProcPlanDetail;
  findCalendar: FindCalendar;
  saveRequisition: SaveRequisition;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  saveProcPlanDetail: SaveProcPlanDetail;
  savePQTemplate: SavePQTemplate;
  dateNow: DateNowHandler;
  randomString: RandomStringHandler;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  deleteApprovalGroups: DeleteApprovalGroups;
  findDelegation: FindDelegation;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
  comment: string;
  assignedUserId: string;
  assignedBackToBackIds: string[];
  // newPQId: string;
  // now: DateOrString;
}

export class Response { }

export const requisitionActionAssign: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);
    const newPQId = await o.randomString(ctx);

    const [rqs] = await o.findRequisition(ctx, { id: req.requisitionId });
    const rq = rqs.length > 0 ? rqs[0] : null;
    if (!rq) {
      throw new Error("requisition not found");
    }

    if (!rq.requestFor || !rq.requestFor.position?.id) {
      throw new Error("requisition requestFor user not found");
    }

    if (rq.status !== "ON_REVIEW") {
      throw new Error("requisition must in ON_REVIEW state");
    }

    // check assignedUserId head
    const [assignedUser] = await o.findUser(ctx, {
      ids: [req.assignedUserId],
    });

    if (!assignedUser || assignedUser.length === 0 || !assignedUser[0].position?.id) {
      throw new Error(`assignedUser ${req.assignedUserId} not found`);
    }

    const description = `${rq.title} PQ`;

    const approvals = await getApprovalWithRequestForRuleList(
      ctx,
      description,
      assignedUser[0].position.id,
      rq.requestFor.position.id,
      o.findUserSupervisors,
      o.findApprovalTemplateGroup,
      newPQId,
      "PQ_REQUIREMENT",
      rq.year
    );

    const pqPhasesRequirementApprovals = approvals.map((newApp) => newApp.approvals.map((app) => app.currentUserInPosition));

    await o.deleteApprovalGroups(ctx, { documentId: newPQId });

    // simpan approval
    await o.saveApprovalGroups(ctx, approvals);

    const [checkActing] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "ACTING" });
    const acting = checkActing.length > 0 ? checkActing[0] : null;

    validateApprovalAction(req.userLogin, rq.approvalGroup!, null, acting);

    const approval = getApproval(rq.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = rq.approvalGroup ? rq.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    const autoApprove = await moveApproval(ctx, paralelAND, rq, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    if (autoApprove) {
      if (rq.isAdditionalProcPlan) {
        const [rqs, rqsCount] = await o.findRequisition(ctx, {
          commodity: rq.commodity,
          departmentId: rq.department?.id,
          status: "APPROVED",
        });

        const ct: string = "A";
        const cm: string = rq.commodity === "GOODS" ? "1" : rq.commodity === "SERVICES" ? "2" : "Y";
        const sequence: number = rqsCount + 1;

        rq.procPlanDetailCode = `A075-${(rq.year + 1).toString().substring(2)}-${ct}-${rq.department?.code}${cm}${sequence.toString().padStart(3, "0")}`;
      }
    }

    const [rbtbs] = await o.findUser(ctx, {
      ids: req.assignedBackToBackIds,
    });

    if (!rbtbs || rbtbs.length === 0) {
      throw new Error(`assigned rbtb ${req.assignedBackToBackIds} not found`);
    }

    // check rbtb supervisor
    // if (!rbtbs.every((x) => x.supervisorPositionId === req.userLogin.position!.id || x.position!.id === req.userLogin.position!.id)) {
    //   throw new Error(`one of rbtb ${req.assignedBackToBackIds} is not supervised by ${req.userLogin.position?.id}`);
    // }

    rq.assignedBackToBack = rbtbs;

    // check assignedUserId supervisor
    // if (assignedUser[0].supervisorPositionId !== req.userLogin.position?.id) {
    //   throw new Error(`assignedUser ${req.assignedUserId} is not supervised by ${req.userLogin.position?.id}`);
    // }
    
    // generate tenderCode
    if (rq.approvalGroup === null) {
      rq.tenderCode = await setRequisitionAssignTenderCode(ctx, rq, o.findRequisition);
    }

    rq.assignedUser = assignedUser[0];

    await o.saveRequisition(ctx, rq);

    await o.saveDocumentHistory(ctx, {
      documentId: rq.id!,
      documentType: "REQUISITION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `Requisition Assignment PIC`,
      user: req.userLogin,
      id: `${rq.id.slice(4)}-${formatDateWithSecond(now)}`,
    });

    // TODO flag procplan used
    const [result, count] = await o.findProcPlanDetail(ctx, { id: rq.procPlanDetailId });
    if (count > 1) {
      result[0].usedByRequisitionIds.push(rq.id);
      await o.saveProcPlanDetail(ctx, [result[0]]);
    }

    if (rq.assignedUser) {
      // send email to assigned user
      const ccUsers = getCcEmailRequisition(rq, req.userLogin);
      let mailData: MailTableData[] = getEmailDataRequisition(rq, req.userLogin);
  
      sendAssignmentMail({
        sendToUserMail: rq.assignedUser!.email!,
        sendToUserName: rq.assignedUser!.name!,
        ccUserMail: ccUsers,
        submittedByUserName: req.userLogin.name
      }, mailData, "REQUISITION")
    }

    // CREATE PQ
    {
      const oeUSD = toUSD(rq.currency!, rq.value!);
      const businessClass = oeUSD >= 50_000_000 ? "LARGE" : oeUSD >= 15_000_000 ? "MEDIUM" : "SMALL";

      const pqObj: PrequalificationTemplate = {
        sourceRequisitionId: rq.id,
        assignmentDate: now as Date,
        tenderMethod: rq.tenderMethod,
        contractDateStart: rq.contractDateStart,
        contractDateEnd: rq.contractDateEnd,
        poDateDelivery: rq.poDateDelivery,
        poDateIssuance: rq.poDateIssuance,
        // businessType: "",
        basicCapabilityCalculationForm: null,
        businessClass: businessClass,
        commodity: rq.commodity,
        // businessFields: [],
        // businessLicense: "",
        // companyStatus: "ConsortiumOfLocalAndNationalCompanies",
        // domicile: "",
        financialDueDiligenceForm: null,
        generalProvisionsRequirementsForm: null,
        highRiskCategory: rq.hseAssessment,
        localCompanyStatementLetters: null,
        pqAnnouncementDoc: null,
        pqMeetingDate: null,
        pqRegistrationDate: null,
        pqStatementLetters: null,
        pqSubmissionDate: null,
        prequalificationType: "ANNOUNCEMENT",
        workOfLocation: rq.workOfLocation,
        // projectLocation: [],
        vhseMSQuisionerForm: null,
        assignedUser: assignedUser[0],
        requestedBackToBacks: rbtbs,
        additionalPQRequirements: rq.supportingDocuments?.aprFiles, // from requisition "aprFiles"
        announcementDate: null,
        currency: rq.currency,
        ownerEstimateValue: rq.value,
        title: rq.title,
        id: newPQId,
        generalScopeOfWorks: rq.generalScopeOfWork,
        localContentLevel: rq.localContentLevel,

        basicCapability: 0,
        // highestExperienceScore: "",

        financialDueDiligence: false,
        invitedVendors: [],
        pqMeeting: false,
        otherInformations: [],
        tenderCode: rq.tenderCode!,
        currentPhase: "REQUIREMENT",
        phasesRequirement: {
          status: "DRAFT",
          isSendBack: false,
          approvalGroup: approvals[0],
        },
      };

      await o.savePQTemplate(ctx, pqObj);
    }

    return {};
  },
};


