import { Usecase } from "../../framework/core.js";
import { FileResultRemarks, FindOnePQVendor, PQSubmissionInfo, SavePQTemplate, SavePQVendor } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  savePQTemplate: SavePQTemplate;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  vendorPqId: string;
  index: "1" | "2" | "3";

  suratPernyataanPQ: FileResultRemarks;
  suratSPDA: FileResultRemarks;
  dokumenBuktiStatusPDN: FileResultRemarks;
  dokumenDomisili: FileResultRemarks;
  suratIzinUsaha: FileResultRemarks;
  sertifikatTKDN: FileResultRemarks;
  suratPerjanjianKonsorsium: FileResultRemarks;
  dokumenK3LL: FileResultRemarks;
  dokumenEvaluasiKemampuanFinansial: FileResultRemarks;
  summaryExperiences: FileResultRemarks;
  otherDocuments: FileResultRemarks[];
}

export class Response {}

export const pqEvaluationVendorSave: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { vendorPqId: req.vendorPqId });

    if (!pqVendor) {
      throw new Error("pq not found");
    }

    if (pqVendor.prequalificationTemplate.currentPhase !== `EVALUATION_${req.index}`) {
      throw new Error("pq must be on Evaluation phase");
    }

    if (pqVendor.status !== `SUBMISSION_${req.index}_SUBMITTED` || pqVendor.status !== `SUBMISSION_${req.index}_EVALUATED`) {
      throw new Error("pq must be on Evaluation phase");
    }

    const submissionPhase = pqVendor.phaseSubmission![`submission${req.index}`];

    if (!submissionPhase) {
      throw new Error(`pq submission${req.index} not found`);
    }

    const surat2 = {
      suratPernyataanPQ: {
        ...submissionPhase.suratPernyataanPQ,
        result: req.suratPernyataanPQ?.result,
        remarks: req.suratPernyataanPQ?.remarks,
        notes: req.suratPernyataanPQ?.notes,
      },
      suratSPDA: {
        ...submissionPhase.suratSPDA,
        result: req.suratSPDA?.result,
        remarks: req.suratSPDA?.remarks,
        notes: req.suratSPDA?.notes,
      },
      dokumenBuktiStatusPDN: submissionPhase.dokumenBuktiStatusPDN
        ? {
            ...submissionPhase.dokumenBuktiStatusPDN,
            result: req.dokumenBuktiStatusPDN?.result,
            remarks: req.dokumenBuktiStatusPDN?.remarks,
            notes: req.dokumenBuktiStatusPDN?.notes,
          }
        : null,
      dokumenDomisili: submissionPhase.dokumenDomisili
        ? {
            ...submissionPhase.dokumenDomisili,
            result: req.dokumenDomisili?.result,
            remarks: req.dokumenDomisili?.remarks,
            notes: req.dokumenDomisili?.notes,
          }
        : null,
      suratIzinUsaha: {
        ...submissionPhase.suratIzinUsaha,
        result: req.suratIzinUsaha?.result,
        remarks: req.suratIzinUsaha?.remarks,
        notes: req.suratIzinUsaha?.notes,
      },
      sertifikatTKDN: submissionPhase.sertifikatTKDN
        ? {
            ...submissionPhase.sertifikatTKDN,
            result: req.sertifikatTKDN?.result,
            remarks: req.sertifikatTKDN?.remarks,
            notes: req.sertifikatTKDN?.notes,
          }
        : null,
      suratPerjanjianKonsorsium: submissionPhase.suratPerjanjianKonsorsium
        ? {
            ...submissionPhase.suratPerjanjianKonsorsium,
            result: req.suratPerjanjianKonsorsium?.result,
            remarks: req.suratPerjanjianKonsorsium?.remarks,
            notes: req.suratPerjanjianKonsorsium?.notes,
          }
        : null,
      dokumenK3LL: {
        ...submissionPhase.dokumenK3LL,
        result: req.dokumenK3LL?.result,
        remarks: req.dokumenK3LL?.remarks,
        notes: req.dokumenK3LL?.notes,
      },
      summaryExperiences: submissionPhase.summaryExperiences
        ? {
            ...submissionPhase.summaryExperiences,
            result: req.summaryExperiences?.result,
            remarks: req.summaryExperiences?.remarks,
            notes: req.summaryExperiences?.notes,
          }
        : null,
      dokumenEvaluasiKemampuanFinansial: submissionPhase.dokumenEvaluasiKemampuanFinansial
        ? {
            ...submissionPhase.dokumenEvaluasiKemampuanFinansial,
            result: req.dokumenEvaluasiKemampuanFinansial?.result,
            remarks: req.dokumenEvaluasiKemampuanFinansial?.remarks,
            notes: req.dokumenEvaluasiKemampuanFinansial?.notes,
          }
        : null,
      otherDocuments: (submissionPhase.otherDocuments ?? []).map((doc, index) => {
        return {
          ...doc,
          result: req.otherDocuments?.[index]?.result ?? doc.result,
          remarks: req.otherDocuments?.[index]?.remarks ?? doc.remarks,
          notes: req.otherDocuments?.[index]?.notes ?? doc.notes,
        };
      }),
    };

    pqVendor.phaseSubmission![`submission${req.index}`] = {
      ...submissionPhase,
      ...surat2,
    };
    pqVendor.phaseSubmission![`submission${req.index}`]!.resultSummary = checkResultSummary(submissionPhase);

    await o.savePQVendor(ctx, pqVendor);

    return {};
  },
};

const checkResultSummary = (p: PQSubmissionInfo): "PASS" | "FAIL" | "UNDECIDED" => {
  //

  const dokumenBuktiStatusPDNIsPass =
    p.dokumenBuktiStatusPDN?.result !== "FAIL" && p.dokumenBuktiStatusPDN?.result !== "UNDECIDED" && p.dokumenBuktiStatusPDN?.result !== undefined;

  const documents = [
    "suratPernyataanPQ",
    "suratSPDA",
    // "dokumenBuktiStatusPDN", // agak beda PDN PN PA Fail
    "dokumenDomisili",
    "suratIzinUsaha",
    "sertifikatTKDN",
    "suratPerjanjianKonsorsium",
    "dokumenK3LL",
    "dokumenEvaluasiKemampuanFinansial",
    "summaryExperiences",
    // "otherDocuments",
  ] as const;

  // otherDocuments
  let otherDocuments: "PASS" | "FAIL" | "UNDECIDED" = "PASS";
  if (p.otherDocuments && p.otherDocuments.length > 0) {
    const visibleOtherDocuments = p.otherDocuments.filter((doc) => doc.show);
    for (const document of visibleOtherDocuments) {
      if (document.result === "UNDECIDED") {
        otherDocuments = "UNDECIDED";
      }
    }
  }

  const isPass = documents.every((x) => !p[x] || p[x]?.result === "PASS");
  if (isPass && dokumenBuktiStatusPDNIsPass && otherDocuments === "PASS") return "PASS";

  const isUndecided = documents.some((x) => p[x]?.result === "UNDECIDED" || p[x]?.result === undefined);
  if (isUndecided && otherDocuments === "UNDECIDED") return "UNDECIDED";

  return "FAIL";
};
