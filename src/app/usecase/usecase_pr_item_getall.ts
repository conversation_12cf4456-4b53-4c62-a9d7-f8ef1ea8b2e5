import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindPrItem, FindPrItemFilter, PrItem } from "../model/model_pr_item.js";

class Gateways {
  findPrItem: FindPrItem;
}

export class Request extends FindPrItemFilter {}

export class Response extends InputResponseWithCount<PrItem> {}

export const prItemGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findPrItem(ctx, req);
    
    const prItem: PrItem[] = items.map((item) => ({
      id: item.id,
      prnumber: item.prnumber,
      item: item.item,
      material: item.material,
      materialdesc: item.materialdesc,
    }));

    return { items: prItem, count };
  },
};
