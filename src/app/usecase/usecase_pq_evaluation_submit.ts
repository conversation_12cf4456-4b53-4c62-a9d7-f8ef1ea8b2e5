import { Context, Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApproval, moveApproval, SaveApprovalGroups } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { EvaluationSubmission, FindOnePQTemplate, FindPQVendor, SavePQTemplate, SavePQVendor } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, TypeOf } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findPQVendor: FindPQVendor;
  savePQTemplate: SavePQTemplate;
  savePQVendor: SavePQVendor;
  dateNow: DateNowHandler;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  findCalendar: FindCalendar;
  saveDocumentHistory: SaveDocumentHistory;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  index: "1" | "2" | "3";

  response: "AD" | "AI" | "PF" | "OK";
  dueDate: Date;

  failedProcess?: string;
  failedNotes?: string;

  comment: string;

  meetingDates: {
    vendorId: string;
    date: Date;
  }[];
}

export class Response {}

export const pqEvaluationSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);
    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== `EVALUATION_${req.index}`) {
      throw new Error("pq must be on Evaluation phase");
    }

    if (!pqTemplate.phasesEvaluation || !pqTemplate.phasesEvaluation[`evaluation${req.index}`]) {
      throw new Error("pq phase evaluation not found");
    }

    const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });

    const filteredVendors = pqVendors.filter((x) => !!x.phaseSubmission?.[`submission${req.index}`]);
    const failedVendors = filteredVendors.filter((x) => x.phaseSubmission?.[`submission${req.index}`]?.resultSummary !== "PASS");

    if (failedVendors.length === 0) {
      // kalau semua pass
      pqTemplate.phasesEvaluation[`evaluation${req.index}`]!.response = "OK";
    } else {
      if (req.index === "1" || req.index === "2") {
        pqTemplate.phasesEvaluation[`evaluation${req.index}`]!.response = req.response;
        pqTemplate.phasesEvaluation = {
          ...pqTemplate.phasesEvaluation,
          [`evaluation${Number(req.index) + 1}`]: {
            response: "UNDECIDED",
            dueDate: req.dueDate,
            approval: null,
          },
        };

        // if (req.response === "AD") {}

        if (req.response === "AI") {
          req.meetingDates.forEach(async (x) => {
            const vendor = failedVendors.find((y) => Number(y.civdVendorId) === Number(x.vendorId));
            if (vendor) {
              vendor.phaseSubmission![`submission${req.index}`]!.meetingAdditionalInformationDate = x.date;
              await o.savePQVendor(ctx, vendor);
            }
          });
        }
      } else {
        if (failedVendors.length === filteredVendors.length) {
          pqTemplate.phasesEvaluation[`evaluation${req.index}`]!.response = "PF";
          pqTemplate.phasesEvaluation[`evaluation${req.index}`]!.failedProcess = req.failedProcess;
          pqTemplate.phasesEvaluation[`evaluation${req.index}`]!.failedNotes = req.failedNotes;
        } else {
          pqTemplate.phasesEvaluation[`evaluation${req.index}`]!.response = "CL";
        }
      }
    }

    await continueApproval(
      ctx,
      pqTemplate.phasesEvaluation[`evaluation${req.index}`]!,
      now,
      req.userLogin,
      o.findApprovalGroup,
      o.saveApprovalGroups,
      o.findCalendar
    );

    await o.savePQTemplate(ctx, pqTemplate);

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: ("PQ_EVALUATION_" + req.index) as TypeOf<typeof DocumentTemplate>,
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Evaluation ${req.index} Submitted`,
      user: req.userLogin,
      id: `${pqTemplate.id.slice(4)}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};

const continueApproval = async (
  ctx: Context,
  pqEvaluationSubmission: EvaluationSubmission,
  now: Date,
  userLogin: UserLogin,
  findApprovalGroup: FindApprovalGroup,
  saveApprovalGroups: SaveApprovalGroups,
  findCalendar: FindCalendar
) => {
  const approval = getApproval(pqEvaluationSubmission.approval?.approvalGroup!, userLogin);
  if (approval) {
    approval.date = getDateOnly(now);
    approval.signer = userLogin;
    approval.status = "DONE";
  }

  const paralelAND = pqEvaluationSubmission.approval?.approvalGroup
    ? pqEvaluationSubmission.approval?.approvalGroup.approvals.every((x) => x.status === "DONE")
    : false;

  const autoApprove = await moveApproval(ctx, paralelAND, pqEvaluationSubmission.approval!, findApprovalGroup, saveApprovalGroups, findCalendar);

  return approval;
};
