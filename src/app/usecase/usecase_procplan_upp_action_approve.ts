import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, FindApprovalGroup, SaveApprovalGroups, getApproval, moveApproval, validateApprovalAction } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindCalendar } from "../model/model_calendar.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindProcPlanDetail, FindProcPlanHeader, ProcPlanDetail, SaveProcPlanDetail, SaveProcPlanHeader } from "../model/model_procplan.js";
import { FindUser, FindUserSupervisors, UserLogin } from "../model/model_user.js";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RandomStringHandler, formatN<PERSON>ber, getEmptyValueCurrency } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly, toUSD } from "../utility/helper.js";
import { MailTemplateTableData, sendMail } from "../utility/mailer.js";

class Gateways {
  findUserSupervisors: FindUserSupervisors;
  findProcPlanHeader: FindProcPlanHeader;
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findProcPlanDetail: FindProcPlanDetail;
  findApprovalGroup: FindApprovalGroup;
  findCalendar: FindCalendar;
  findDelegation: FindDelegation;
  saveProcPlanHeader: SaveProcPlanHeader;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  saveProcPlanDetail: SaveProcPlanDetail;
  deleteApprovalGroups: DeleteApprovalGroups;
  dateNow: DateNowHandler;
  randomString: RandomStringHandler;
}

export class Request {
  userLogin: UserLogin;
  headerId: string;
  comment: string;
  // newAPPHeaderId: string;
  // now: DateOrString;
}

export class Response {}

export const procplanUppActionApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);
    const newAPPHeaderId = await o.randomString(ctx);

    const [pphs] = await o.findProcPlanHeader(ctx, { id: req.headerId, procPlanType: "UPP" });
    let pphUPP = pphs.length > 0 ? pphs[0] : null;

    if (!pphUPP) {
      throw new Error("procplan header not found");
    }

    if (pphUPP.status !== "ON_REVIEW") {
      throw new Error("proc plan must in ON_REVIEW state");
    }

    const [checkActing] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "ACTING" });
    const acting = checkActing.length > 0 ? checkActing[0] : null;

    validateApprovalAction(req.userLogin, pphUPP.approvalGroup, null, acting);

    // melalui procplan header, current approval kita remote dan kita set DONE
    // pphUPP.approval!.date = now;
    // pphUPP.approval!.signer = req.userLogin;
    // pphUPP.approval!.status = "DONE";

    const approval = getApproval(pphUPP.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = pphUPP.approvalGroup ? pphUPP.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    // if (pphUPP.approvalGroup!.nextApprovalGroupId && paralelAND) {
    //   //

    //   const nextApproval = await findNextApprovalObject(ctx, findApproval, pphUPP.approvalGroup!.nextApprovalGroupId!);

    //   nextApproval.status = "PROCESSING";

    //   await saveApprovals(ctx, [pphUPP.approvalGroup!, nextApproval]);

    //   pphUPP.status = "ON_REVIEW";
    //   pphUPP.isSendBack = false;
    //   pphUPP.approvalGroup = nextApproval;
    //   await saveProcPlanHeader(ctx, pphUPP);
    //   //
    // } else {
    //   // no next approval means that auto approve
    //   await saveApprovals(ctx, [pphUPP.approvalGroup!]);

    //   // auto approve!
    //   pphUPP.status = "APPROVED";\
    //   pphUPP.isSendBack = false;
    //   pphUPP.approvalGroup = null;
    //   await saveProcPlanHeader(ctx, pphUPP);
    // }

    await moveApproval(ctx, paralelAND, pphUPP, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    await o.saveProcPlanHeader(ctx, pphUPP);

    await o.saveDocumentHistory(ctx, {
      documentId: pphUPP.id!,
      documentType: "PROC_PLAN_UPP",
      comment: req.comment,
      date: getDateOnly(now),
      message: `UPP Approved`,
      user: req.userLogin,
      id: `${pphUPP.id}-${formatDateWithSecond(now)}`,
    });

    if (pphUPP.approvalGroup && pphUPP.approvalGroup.sequence! > 1) {
      // set data for email reminder
      const [ppds] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: pphUPP.id!, procPlanType: "UPP" });
      const approvalUser = pphUPP.approvalGroup.approvals[0].currentUserInPosition ?? pphUPP.approvalGroup.approvals[0].users![0];

      const ppdReminders: MailTemplateTableData[] = [
        {
          department: pphUPP.department?.name!,
          section: pphUPP.section?.name!,
          quantity: pphUPP.count ?? ppds.length,
          value: pphUPP.totalValueEstimation
            ? formatNumber(
                pphUPP.totalValueEstimation.find((x) => x.currency === "USD")?.value! +
                  toUSD("IDR", pphUPP.totalValueEstimation.find((x) => x.currency === "IDR")?.value!)
              )
            : "0",
          url: "/procurement-plan/request/upp?section=" + pphUPP.section?.id,
        },
      ];

      sendMail(
        {
          sendToUserMail: approvalUser.email!,
          sendToUserName: approvalUser.name!,
          mailSubject: "PRISA - Approval for Procurement Plan",
          mailTitle: "Approval for Procurement Plan",
        },
        ppdReminders,
        "APPROVAL"
      );
    }

    // ==============================================================================================================================
    // ==============================================================================================================================
    // ==============================================================================================================================
    // ==============================================================================================================================
    // ========================================== APP DIMULAI DISINI ================================================================
    // ==============================================================================================================================
    // ==============================================================================================================================
    // ==============================================================================================================================
    // ==============================================================================================================================

    // kalau masih ada next approval alias ini adalah bukan approver terakhir maka kita masih skip pembuatan APP
    if (pphUPP.approvalGroup) {
      return {};
    }

    // const requestForUser = pphUPP.requestFor as User;
    // const rbtbs = pphUPP.requesterBackToBack;

    const [ppdUPPs] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: pphUPP.id });
    if (ppdUPPs.length === 0) {
      throw new Error(`no procplan detail found in proc pplan header with id ${pphUPP.id}`);
    }

    let [pphAPPs] = await o.findProcPlanHeader(ctx, {
      procPlanType: "APP",
      departmentIds: [pphUPP.department?.id!],
      year: pphUPP.year,
    });
    let pphAPP = pphAPPs.length > 0 ? pphAPPs[0] : null;

    if (!pphAPP) {
      pphAPP = {
        id: newAPPHeaderId,
        year: pphUPP.year,
        procPlanType: "APP",
        status: "DRAFT",
        isSendBack: false,
        submittedDate: getDateOnly(now),
        count: 0,
        section: null,
        sections: [],
        department: pphUPP.department!,
        approvalGroup: null,
        totalValueEstimation: getEmptyValueCurrency(),
        submitter: req.userLogin,
        fromProcPlanHeaderId: pphUPP.id,
      };

      // // user including supervisors
      // const [listUser] = await o.findUserSupervisors(ctx, { positionId: req.userLogin.supervisorPositionId! });
      // if (listUser.length === 0) {
      //   throw new Error(`user supervisor for UPP is not found`);
      // }

      // const description = `${req.userLogin.department?.name}-${ppdUPPs.length}Qty`;

      // // approval list hanya dibuat sekali pada saat pertama kali dibuatkan APP header
      // const approvals = await getApprovalList(ctx, description, o.findUser, o.findApprovalTemplateGroup, listUser, pphAPP.id, "PROC_PLAN_APP");

      // await o.deleteApprovalGroups(ctx, { documentId: pphAPP.id });

      // // simpan approval
      // await o.saveApprovalGroups(ctx, approvals);

      // // pointer nunjuk ke approval pertama
      // pphAPP.approvalGroup = approvals[0];

      await o.saveProcPlanHeader(ctx, pphAPP);
    }

    // procplan header harus dalam DRAFT status
    if (pphAPP!.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    // let currentSequenceId = pphAPP!.nextProcPlanDetailSequenceId as number;

    await o.saveProcPlanDetail(
      ctx,
      ppdUPPs.map((x) => {
        //
        // pphAPP!.totalValueEstimation += Number(x.valueEstimation);

        pphAPP!.totalValueEstimation.find((v) => v.currency === x.currency)!.value += Number(x.valueEstimation);

        if (!pphAPP?.sections.some((sc) => sc.id === x.section!.id)) {
          pphAPP?.sections.push(x.section!);
        }

        return {
          ...x,
          procPlanHeader: pphAPP,
          procPlanType: "APP",
          id: `APP-${x.id.slice(4)}`,
          createdAt: now,
        } as ProcPlanDetail;
      })
    );

    pphAPP.count! += ppdUPPs.length;

    o.saveProcPlanHeader(ctx, pphAPP);

    // send email to vendor management
    {
      // const [ppds] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: pphUPP.id!, procPlanType: "APP", status: "DRAFT" });
      const approvalUser = pphAPP.approvalGroup?.approvals[0].currentUserInPosition ?? pphAPP.approvalGroup?.approvals[0].users![0] ?? req.userLogin;

      const ppdReminders: MailTemplateTableData[] = [
        {
          department: pphUPP.department?.name!,
          section: pphUPP.section?.name!,
          quantity: pphUPP.count,
          value: pphUPP.totalValueEstimation
            ? formatNumber(
                pphUPP.totalValueEstimation.find((x) => x.currency === "USD")?.value! +
                  toUSD("IDR", pphUPP.totalValueEstimation.find((x) => x.currency === "IDR")?.value!)
              )
            : "0",
          url: "/procurement-plan/request/app?department=" + pphAPP.department?.id,
        },
      ];

      sendMail(
        {
          sendToUserMail: approvalUser.email!,
          sendToUserName: approvalUser.name!,
          submittedByUserName: req.userLogin.name,
          mailSubject: "PRISA - Notification for Procurement Plan",
          mailTitle: "Notification for Procurement Plan",
        },
        ppdReminders,
        "NOTIFICATION"
      );
    }
    return {};
  },
};
