import { Usecase } from "../../framework/core.js";
import { FindOnePQVendor, PrequalificationVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  pqId: string;
  vendorId: number;
}

export class Response {
  prequalificationVendor: PrequalificationVendor | null;
}

export const vendorPQEvaluationGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { ...req, civdVendorId: req.vendorId });
    return { prequalificationVendor: pqVendor };
  },
};
