import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { CIVDVendor, FindVendorCIVD, FindVendorCIVDFilter } from "../model/model_civd_vendor.js";

class Gateways {
  findVendorCIVD: FindVendor<PERSON><PERSON>;
}

export class Request extends FindVendorCIVDFilter {}

export class Response extends InputResponseWithCount<CIVDVendor> {}

export const vendorVendorGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findVendorCIVD(ctx, req);
    return { items, count };
  },
};
