import { Usecase } from "../../framework/core.js";
import { ApprovalTemplateGroup, DeleteApprovalTemplateGroups, SaveApprovalTemplateGroups } from "../model/model_approval_template.js";
import { FindUser } from "../model/model_user.js";
import { DocumentTemplate, TypeOf, UserRole } from "../model/vo.js";

class Gateways {
  saveApprovalTemplateGroups: SaveApprovalTemplateGroups;
  deleteApprovalTemplateGroups: DeleteApprovalTemplateGroups;
  findUser: FindUser;
}

export class Request {
  approvalTemplateGroups: Omit<ApprovalTemplateGroup, "id" | "sequence" | "documentTemplateType">[];
  documentType: TypeOf<typeof DocumentTemplate>;
}

export class Response {}

export const approvalTemplateCreate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const userIds: string[] = [];
    req.approvalTemplateGroups.forEach((atg) => {
      atg.approvalTemplates.forEach((at) => {
        if (req.documentType === "REQUISITION" && !at.subDocumentType) {
          throw new Error("subDocumentType must be specified for documentType Requisition");
        }
        if (at.users && at.role) {
          throw new Error("you must specify one of users or role field. not both!");
        }
        if (at.users) {
          if (!at.as) {
            throw new Error("'as' must be defined for users field");
          }
          at.users.forEach((user) => userIds.push(user.id));
        }
        if (at.role) {
          if (!UserRole.some((role) => at.role === role)) {
            throw new Error(`Role must be one of ${UserRole}`);
          }
        }
      });
    });

    const userSet = new Set<string>();
    const [users] = await o.findUser(ctx, { ids: userIds });
    users.forEach((user) => userSet.add(user.id));

    userIds.forEach((userId) => {
      if (!userSet.has(userId)) {
        throw new Error(`User with id ${userId} `);
      }
    });

    const results = req.approvalTemplateGroups.map((atg, i) => {
      return {
        id: `${req.documentType}_${(i + 1).toString().padStart(2, "0")}`,
        sequence: i + 1,
        approvalTemplates: atg.approvalTemplates,
        documentType: req.documentType,
      };
    });

    await o.deleteApprovalTemplateGroups(ctx, {documentType: req.documentType});
    
    await o.saveApprovalTemplateGroups(ctx, results);

    return {};
  },
};
