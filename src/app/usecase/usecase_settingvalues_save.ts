import { Usecase } from "../../framework/core.js";
import { SaveSettingValues, SettingValues } from "../model/model_setting_values.js";

class Gateways {
  saveSettingValues: SaveSettingValues;
}

export class Request {
  yearlyRateUSDToIDR: number;
  currentProcPlanYear: string;
}

export class Response { }

export const settingValuesSave: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const results: SettingValues[] = [];

    for (const [key, value] of Object.entries(req)) {
      results.push({
        id: key,
        value,
      });
    }

    await o.saveSettingValues(ctx, results);

    return {};
  },
};
