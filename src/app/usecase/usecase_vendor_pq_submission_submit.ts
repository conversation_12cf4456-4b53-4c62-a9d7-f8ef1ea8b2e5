import { Usecase } from "../../framework/core.js";
import { FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQTemplate, FindOnePQVendor, SavePQVendor } from "../model/model_prequalification.js";
import { DateNowHandler } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  findOneVendorCIVD: FindOneVendorCIVD;
  findOnePQTemplate: FindOnePQTemplate;
  dateNow: DateNowHandler;
}

export class Request {
  vendorCIVDLogin: vendorCIVDLogin;
  pqId: string;
  submissionPhase: 1 | 2 | 3;
}

export class Response {}

export const vendorPQSubmissionSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== `EVALUATION_${req.submissionPhase}`) {
      throw new Error("pq must be on Submission phase");
    }

    const dueDate = pqTemplate.phasesEvaluation?.[`evaluation${req.submissionPhase}`]?.dueDate;
    if (!dueDate) throw new Error("pq submission date not found");

    if (getDateOnly(now) > dueDate) throw new Error("pq submission date is expired");

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    if (!pqVendor || !pqVendor.phaseSubmission || !pqVendor.phaseSubmission[`submission${req.submissionPhase}`]) {
      throw new Error("pq vendor not found");
    }

    if (pqVendor.status !== `SUBMISSION_${req.submissionPhase}_DRAFT`) {
      throw new Error("pq must be on Submission phase");
    }

    pqVendor.phaseSubmission[`submission${req.submissionPhase}`]!.resultSummary = "UNDECIDED";
    pqVendor.phaseSubmission[`submission${req.submissionPhase}`]!.submitDate = getDateOnly(now);
    pqVendor.phaseSubmission[`submission${req.submissionPhase}`]!.evaluationDate = null;

    pqVendor.status = `SUBMISSION_${req.submissionPhase}_SUBMITTED`;

    await o.savePQVendor(ctx, pqVendor);

    return { req: pqVendor.phaseSubmission };
  },
};
