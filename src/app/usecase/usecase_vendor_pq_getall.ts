import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindPQTemplate, FindVendorPQTemplate, PrequalificationTemplate } from "../model/model_prequalification.js";
import { PreQualificationPhase, TypeOf } from "../model/vo.js";

class Gateways {
  findPQTemplate: FindPQTemplate;
  findVendorPqTemplate: FindVendorPQTemplate;
}

export class Request {
  vendorCIVDLogin: vendorCIVDLogin;
  phase?: TypeOf<typeof PreQualificationPhase>;
  list: "pqInfo" | "myPq";
  title?: string;
}

export class Response extends InputResponseWithCount<TransformVendorPQGetAll> {}

export const vendorPQGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // "myPq" = all vendor PQ that has been submitted by vendor (from vendor PQ template)
    // "pqInfo" = all available PQ or currentPhase = REQUIREMENT & phasesRequirement.status = APPROVED

    const [items, count] = await o.findVendorPqTemplate(ctx, {
      title: req.title,
      civdVendorId: req.vendorCIVDLogin.civdVendorId,
      currentPhase: req.list === "pqInfo" ? "REGISTRATION" : req.phase,
      listType: req.list,
    });

    const results: TransformVendorPQGetAll[] = items.map((item) => ({
      id: item.id,
      tenderCode: item.tenderCode,
      title: item.title,
      generalScopeOfWorks: item.generalScopeOfWorks,
      businessClass: item.businessClass,
      businessLicenses: item.businessLicenses,
      // businessFields: item.businessFields,
      localContentLevel: item.localContentLevel,
      highRiskCategory: item.highRiskCategory,
      currentPhase: item.currentPhase,
      pqRegistrationDate: item.pqRegistrationDate,
      announcementDate: item.announcementDate,
      prequalificationType: item.prequalificationType,
      pqMeetingDate: item.pqMeetingDate,
      pqMeeting: item.pqMeeting,
    }));

    return { items: results, count };
  },
};

type TransformVendorPQGetAll = Pick<
  PrequalificationTemplate,
  | "id"
  | "tenderCode"
  | "title"
  | "generalScopeOfWorks"
  | "businessClass"
  | "businessLicenses"
  // 'businessFields' |
  | "localContentLevel"
  | "highRiskCategory"
  | "currentPhase"
  | "pqRegistrationDate"
  | "announcementDate"
  | "prequalificationType"
  | "pqMeetingDate"
  | "pqMeeting"
>;
