import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApproval, getLastApprovalGroupDurationDays, getSubDocumentWording, HasApprovalGroup, moveApproval, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { saveLogger } from "../model/model_log.js";
import { FindOnePQTemplate, FindPQTemplate, FindPQVendor } from "../model/model_prequalification.js";
import { FindProcPlanDetail, FindProcPlanHeader, ProcPlanHeader, SaveProcPlanHeader } from "../model/model_procplan.js";
import { FindRequisition, Requisition, SaveRequisition } from "../model/model_requisition.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, TypeOf } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly, isUserAdmin } from "../utility/helper.js";

class Gateways {
  dateNow: DateNowHandler;
  findApprovalGroup: FindApprovalGroup;
  findRequisition: FindRequisition;
  findProcPlanHeader: FindProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findOnePQTemplate: FindOnePQTemplate;
  findPQVendor: FindPQVendor;
  findUser: FindUser;
  saveApprovalGroups: SaveApprovalGroups;
  saveRequisition: SaveRequisition;
  saveProcPlanHeader: SaveProcPlanHeader;
  findCalendar: FindCalendar;
  saveDocumentHistory: SaveDocumentHistory;
}

export class Request {
  userLogin: UserLogin;
  documentId: string;
  documentType: TypeOf<typeof DocumentTemplate>;
  approvedDate: Date;
  comment: string;
  approveAsUserId: string;
}

export class Response {}

export const approvalAdminApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // Check if user is admin
    if (!req.userLogin.email || !isUserAdmin(req.userLogin.email)) {
      throw new Error("Only admin users can perform this operation");
    }

    const [approveAsUsers] = await o.findUser(ctx, { ids: [req.approveAsUserId] });
    const approveAsUser = approveAsUsers.length > 0 ? approveAsUsers[0] : null;
    if (!approveAsUser) {
      throw new Error("Approve As user not found");
    }

    // Get current date
    const now = await o.dateNow(ctx);
    const dateApproved = new Date(req.approvedDate);

    let docHasApproval: HasApprovalGroup | null = null;
    if (req.documentType === "REQUISITION") {
      const [requisitions] = await o.findRequisition(ctx, { id: req.documentId });
      docHasApproval = requisitions.length > 0 ? requisitions[0] : null;
    } else if (req.documentType === "PROC_PLAN_APP" || req.documentType === "PROC_PLAN_UPP") {
      const [pphs] = await o.findProcPlanHeader(ctx, { id: req.documentId });
      docHasApproval = pphs.length > 0 ? pphs[0] : null;
    } else {
      throw new Error("documentType not supported");
    }

    if (!docHasApproval || docHasApproval.approvalGroup === null) {
      throw new Error("document not found");
    }

    if (docHasApproval.status !== "ON_REVIEW") {
      throw new Error("document must in ON_REVIEW state");
    }

    if (docHasApproval.approvalGroup.nextApprovalGroupId === null) {
      throw new Error("approval not supported for this document");
    }

    validateApprovalAction(approveAsUser, docHasApproval.approvalGroup);

    const approval = getApproval(docHasApproval.approvalGroup!, approveAsUser);
    if (approval) {
      approval.date = getDateOnly(dateApproved);
      approval.signer = approveAsUser;
      approval.status = "DONE";
    }

    const approvalGroup = docHasApproval.approvalGroup!;
    const paralelAND = docHasApproval.approvalGroup ? docHasApproval.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    if (!paralelAND) {
      for (const approval of approvalGroup.approvals) {
        if (approval.users && approval.users.some((user) => user.id === approveAsUser.id)) {
          approval.date = getDateOnly(dateApproved);
          approval.signer = approveAsUser;
          approval.status = "DONE";
        }
      }

      if (approvalGroup.approvals.every((x) => x.status === "DONE")) {
        approvalGroup.durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, approvalGroup, dateApproved, o.findCalendar);
      }

      await o.saveApprovalGroups(ctx, [approvalGroup])
    } else {
      const autoApprove = await moveApproval(ctx, paralelAND, docHasApproval, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);
    }

    // Save document based on type
    if (req.documentType === "REQUISITION") {
      await o.saveRequisition(ctx, docHasApproval as Requisition);
    } else if (req.documentType === "PROC_PLAN_APP" || req.documentType === "PROC_PLAN_UPP") {
      await o.saveProcPlanHeader(ctx, docHasApproval as ProcPlanHeader);
    }

    await o.saveDocumentHistory(ctx, {
      documentId: req.documentId,
      documentType: req.documentType,
      comment: req.comment,
      date: getDateOnly(dateApproved),
      message: `${getSubDocumentWording(approval?.subDocumentType!)} Approved`,
      user: approveAsUser,
      id: `${req.documentId.slice(4)}-${formatDateWithSecond(dateApproved)}`,
    });

    await saveLogger("adminApprove", {
      documentId: req.documentId,
      documentType: req.documentType,
      comment: req.comment,
      date: getDateOnly(dateApproved),
      message: `${getSubDocumentWording(approval?.subDocumentType!)} Approved`,
      user: approveAsUser,
    }, "SUCCESS");

    return {};
  },
};
