import { Usecase } from "../../framework/core.js";
import { FindOnePQVendor, PrequalificationVendor, SavePQVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
}

export class Request {
  pqId: string;
  vendorId: number;
}

export class Response {}

export const vendorPQEvaluationSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { ...req, civdVendorId: req.vendorId });

    if (!pqVendor) {
      return {};
    }

    let index = 0;

    if (pqVendor.prequalificationTemplate.currentPhase === "EVALUATION_1") index = 1;
    else if (pqVendor.prequalificationTemplate.currentPhase === "EVALUATION_2") index = 2;
    else if (pqVendor.prequalificationTemplate.currentPhase === "EVALUATION_3") index = 3;
    else throw new Error("pq vendor must in EVALUATION phase");

    pqVendor.status = `SUBMISSION_${index}_SUBMITTED` as PrequalificationVendor["status"];

    await o.savePQVendor(ctx, pqVendor);

    return {};
  },
};
