import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { BusinessFieldLicense, FindAllBusinessFieldLicense, FindBusinessFieldLicenseFilter } from "../model/model_business_field_license.js";

class Gateways {
  findAllBusinessFieldLicense: FindAllBusinessFieldLicense;
}

export class Request extends FindBusinessFieldLicenseFilter { }

export class Response extends InputResponseWithCount<BusinessFieldLicense> { }

export const businessFieldLicenseGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findAllBusinessFieldLicense(ctx, req);
    return { items, count };
  },
};
