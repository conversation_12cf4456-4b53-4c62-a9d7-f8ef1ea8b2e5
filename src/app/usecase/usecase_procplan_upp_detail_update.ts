import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import {
  FindProcPlanDetail,
  ProcPlanDetail,
  ProcplanDetailPayload,
  SaveProcPlanDetail,
  SaveProcPlanHeader,
  getExistingProcPlanDetail,
  getUPPErrorFields,
} from "../model/model_procplan.js";
import { FindUser, FindUserSupervisors, UserLogin, getRBTBs } from "../model/model_user.js";
import { DateNowHandler, validateActionByID } from "../model/vo.js";
import { toUSD } from "../utility/helper.js";

class Gateways {
  saveProcPlanHeader: SaveProcPlanHeader;
  saveProcPlanDetail: SaveProcPlanDetail;
  saveApprovalGroups: SaveApprovalGroups;
  deleteApprovalGroups: DeleteApprovalGroups;
  findUser: FindUser;
  findProcPlanDetail: FindProcPlanDetail;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  // now: DateOrString;
  detailId: string;
  procPlanDetail: ProcplanDetailPayload & {
    requestForId: string;
    requesterBackToBackIds: string[];
  };
}

export class Response {}

export const procplanUppDetailUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //
    const now = await o.dateNow(ctx);

    // ambil detailnya
    const ppd = await getExistingProcPlanDetail(ctx, o.findProcPlanDetail, req.detailId, "UPP");

    // ambil headernya
    const pph = ppd.procPlanHeader;
    if (!pph) {
      throw new Error("Procplan Header UPP not found");
    }

    // make sure dalam status DRAFT
    if (pph.status !== "DRAFT" && pph.status !== "APPROVED") {
      throw new Error("proc plan must in DRAFT state");
    }

    validateActionByID(
      req.userLogin.id,
      [
        //
        ppd.creator!.id,
        ppd.requestFor!.id,
        ...ppd.requesterBackToBack.map((x) => x.id),
      ],
      req.userLogin.section!.id,
      pph.section!.id
    );

    const errorFields = getUPPErrorFields(req.procPlanDetail);
    // validateProcPlanRequest(req.procPlanDetail);

    ppd.errorFields = errorFields;

    // ambil requestFor
    // const requestForUser = await getRequestFor(
    //   //
    //   ctx,
    //   o.findUser,
    //   req.procPlanDetail.requestForId,
    //   ppd.department?.id!,
    //   ppd.section?.id!
    // );

    const [requestForUsers] = await o.findUser(ctx, { ids: [req.procPlanDetail.requestForId] });

    if (requestForUsers.length === 0 || !requestForUsers) {
      throw new Error(`requestFor ${req.procPlanDetail.requestForId} not found`);
    }

    const requestForUser = requestForUsers[0];

    ppd.department = requestForUser.department!;
    ppd.section = requestForUser.section!;

    // ambil rbtb
    const rbtbs = await getRBTBs(
      ctx,
      o.findUser,
      req.userLogin.id!,
      req.procPlanDetail.requestForId,
      req.procPlanDetail.requesterBackToBackIds,
      ppd.department?.id!
    );

    ppd.requesterBackToBack = rbtbs;

    // reduce first
    // pph.totalValueEstimation -= Number(ppd.valueEstimation);

    pph.totalValueEstimation.find((v) => v.currency === ppd.currency)!.value -= Number(ppd.valueEstimation);

    // add the new one
    // pph.totalValueEstimation += Number(req.procPlanDetail.valueEstimation);

    pph.totalValueEstimation.find((v) => v.currency === ppd.currency)!.value += Number(req.procPlanDetail.valueEstimation);

    // update approval group
    // if (req.procPlanDetail.requestForId !== ppd.requestFor?.id) {
    //   //

    //   ppd.requestFor = requestForUser;

    //   pph.approvalGroup = null;

    //   await o.saveProcPlanHeader(ctx, pph);

    //   const [listUser] = await o.findUserSupervisors(ctx, { positionId: requestForUser.position!.id! });
    //   if (listUser.length === 0) {
    //     throw new Error(`user supervisor for UPP is not found`);
    //   }

    //   const description = `${pph.department?.name}-${pph.section?.name}-${pph?.count!}Qty`;

    //   const approvals = await getApprovalList(ctx, description, o.findUser, o.findApprovalTemplateGroup, listUser, pph.id, "PROC_PLAN_UPP");

    //   // rule hasNoApprover
    //   if (approvals.length === 3) {
    //     approvals[1].nextApprovalGroupId = null;
    //     approvals.pop();
    //   }

    //   await o.deleteApprovalGroups(ctx, { documentId: pph.id! });

    //   await o.saveApprovalGroups(ctx, approvals);

    //   pph.approvalGroup = approvals[0];
    // }

    await o.saveProcPlanHeader(ctx, pph);

    const updatedPpd: ProcPlanDetail = {
      ...ppd,
      ...req.procPlanDetail,
      valueInUSD: toUSD(req.procPlanDetail.currency, req.procPlanDetail.valueEstimation),
    };

    await o.saveProcPlanDetail(ctx, [updatedPpd]);

    return {};
  },
};
