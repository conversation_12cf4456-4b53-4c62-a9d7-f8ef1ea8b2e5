import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { Approval, ApprovalGroup, FindApprovalGroup, FindApprovalGroupFilter } from "../model/model_approval.js";
import { ApprovalTemplateRule, getRequistionDocRule, requisitionDocRuleValue } from "../model/model_approval_template.js";
import { Position } from "../model/model_position.js";
import { FindRequisition } from "../model/model_requisition.js";
import { FindUser, User } from "../model/model_user.js";
import { ApprovalStatus, SubDocumentRequisition, TypeOf, UserRole } from "../model/vo.js";
import { toUSD } from "../utility/helper.js";

class Gateways {
  findApprovalGroup: FindApprovalGroup;
  findRequisition: FindRequisition;
  findUser: FindUser;
}

export class Request extends FindApprovalGroupFilter {}

export class Response extends InputResponseWithCount<ApprovalHeader> {}

export const approvalGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items] = await o.findApprovalGroup(ctx, req);
    const { minRQValue, maxRQValue, minOEValue, maxOEValue } = requisitionDocRuleValue;

    if (req.documentType === "REQUISITION") {
      //

      const [rqs, count] = await o.findRequisition(ctx, {
        id: req.documentId,
      });

      if (count === 0) {
        throw new Error("REQUISITION not found");
      }

      const requisition = rqs[0];
      const newApprovalGroup: ApprovalGroup[] = [];
      const newApprovalDetail: ApprovalDetail[] = [];

      const OEValue = toUSD(requisition.currency, requisition.value);
      const procPlanValue = toUSD(requisition.procPlanCurrency!, requisition.procPlanValue!);

      const docRule: ApprovalTemplateRule = getRequistionDocRule(
        requisition.commodity,
        requisition.department?.code!,
        requisition.budgetOwners,
        requisition.isAdditionalProcPlan,
        requisition.tenderMethod,
        requisition.contractTypeEngagement,
        OEValue,
        procPlanValue
      );

      let listUserBudgetOwners: User[] = [];

      if (docRule.additionalBudgetOwnerManager && requisition.budgetOwners.length > 0) {
        for (const budgetOwner of requisition.budgetOwners) {
          const [budgetOwnerUsers] = await o.findUser(ctx, { departmentId: budgetOwner });
          const budgetOwnerUser = budgetOwnerUsers.filter((user) => user.position?.role === "MGR" && user.department?.id !== requisition.department?.id);
          if (budgetOwnerUser.length > 0) {
            listUserBudgetOwners.push(...budgetOwnerUser);
          }
        }
      }

      // reconstruct approval list
      {
        for (const item of items) {
          const newApprovals: Approval[] = [];
          for (const app of item.approvals) {
            if (UserRole.some((x) => x === app.currentUserInPosition?.position?.role)) {
              newApprovalDetail.push({
                as: app.as,
                date: app.date,
                signer: app.signer ? app.signer.name : "",
                status: app.status,
                users: app.users ? app.users : [],
                position: app.position ? app.position : null,
                currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
                sequence: item.sequence,
              });
            } else {
              newApprovals.push(app);
            }
          }
          newApprovalGroup.push({
            approvals: newApprovals,
            sequence: item.sequence,
          });
        }
      }

      const result: ApprovalHeader[] = SubDocumentRequisition.map((subDocumentType) => {
        return {
          subDocumentType,
          approvals: [
            ...newApprovalDetail,
            ...newApprovalGroup.flatMap((it) =>
              it.approvals
                .filter((x) => x.subDocumentType === subDocumentType)
                .map((app) => ({
                  as: app.as,
                  date: app.date,
                  signer: app.signer ? app.signer.name : "",
                  status: app.status,
                  users: app.users ? app.users : [],
                  position: app.position ? app.position : null,
                  currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
                  sequence: it.sequence,
                }))
            ),
          ].sort((a, b) => a.sequence! - b.sequence!),
        };
      });

      const filteredByRuleResult: ApprovalHeader[] = result.map((header) => ({
        subDocumentType: header.subDocumentType,
        approvals: header.approvals
          .filter((app) => !(header.subDocumentType === "INSURANCE_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(header.subDocumentType === "HSE_RISK_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(header.subDocumentType === "DA_JUSTIFICATION" && !!app.currentUserInPosition && !docRule.isDaJustification))
          .filter(
            (app) =>
              !(
                header.subDocumentType === "OWNER_ESTIMATION" &&
                ((app.currentUserInPosition?.position?.role === "GM" && OEValue <= maxOEValue) ||
                  (app.currentUserInPosition?.position?.role === "SMVP" && OEValue <= minOEValue))
              )
          )
          .filter(
            (app) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                app.currentUserInPosition?.position?.role === "GM" &&
                OEValue <= maxRQValue
              )
          )
          .filter(
            (app) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                (app.currentUserInPosition?.position?.role === "SMVP" || app.currentUserInPosition?.position?.role === "GM") &&
                OEValue <= minRQValue
              )
          )
          .filter(
            (app, index) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                index !== 0 &&
                app.currentUserInPosition &&
                requisition.commodity === "GOODS" &&
                requisition.contractTypeEngagement === "FIRM_COMMITMENT"
              )
          )
          .filter(
            (app) =>
              !(
                header.subDocumentType !== "OWNER_ESTIMATION" &&
                app.currentUserInPosition?.position?.role === "MGR" &&
                requisition.budgetOwners.length > 0 &&
                listUserBudgetOwners.some((user) => user.id === app.currentUserInPosition?.id)
              )
          ),
      }));

      return {
        items: filteredByRuleResult,
        count: filteredByRuleResult.length,
      };
    }

    return {
      items: [
        {
          subDocumentType: null,
          approvals: items.flatMap((it) =>
            it.approvals.map((app) => ({
              as: app.as,
              date: app.date,
              signer: app.signer ? app.signer.name : "",
              status: app.status,
              users: app.users ? app.users : [],
              position: app.position ? app.position : null,
              currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
              sequence: it.sequence,
            }))
          ),
        },
      ],
      count: 1,
    };
  },
};

type ApprovalHeader = {
  subDocumentType: TypeOf<typeof SubDocumentRequisition> | null;
  approvals: ApprovalDetail[];
};

type ApprovalDetail = {
  as?: string | null;
  date?: Date | null;
  signer?: string | null;
  status?: TypeOf<typeof ApprovalStatus>;
  users?: User[];
  position?: Position | null;
  currentUserInPosition?: User | null;
  sequence?: number;
};
