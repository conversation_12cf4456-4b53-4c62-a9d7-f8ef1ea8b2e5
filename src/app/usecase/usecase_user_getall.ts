import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindUser, FindUserFilter, FindUserNative, User } from "../model/model_user.js";

class Gateways {
  findUser: FindUser;
  findUserNative: FindUserNative;
}

export class Request extends FindUserFilter { }

export class Response extends InputResponseWithCount<User> { }

export const userGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    let items: User[] = [];
    let count = 0;

    if (req.isRequestFor) {
      [items, count] = await o.findUserNative(ctx, req);
    } else {
      [items, count] = await o.findUser(ctx, req);
    }
    return { items, count };
  },
};
