import { Usecase } from "../../framework/core.js";
import { BaseFindManyFilter } from "../../framework/repository.js";
import { FindRequisitionByFromExcludeDepartment, Requisition } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";

class Gateways {
  findRequisitionByFromExcludeDepartment: FindRequisitionByFromExcludeDepartment;
}

export class Request extends BaseFindManyFilter {
  userLogin: UserLogin;
  year: number;
  title?: string;
  tenderCode?: string;
}

export class Response {
  items: Requisition[];
  count: number;
}

export const requisitionExternalDepartmentGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [requisition, count] = await o.findRequisitionByFromExcludeDepartment(ctx, {
      userId: req.userLogin.id,
      departmentId: req.userLogin.department?.id,
      year: req.year,
      page: req.page,
      size: req.size,
      title: req.title,
      tenderCode: req.tenderCode,
    });

    return {
      items:
        requisition.map((req) => ({
          ...req,
        })) || [],
      count: count,
    };
  },
};

// type Detail = {
//   id?: string;
//   title?: string;
//   tenderMethod?: TypeOf<typeof TenderMethod>;
//   contractDateStart?: Date | null;
//   contractDateEnd?: Date | null;
//   poDateIssuance?: Date | null;
//   poDateDelivery?: Date | null;
//   currency?: TypeOf<typeof Currency>;
//   valueEstimation?: number;
//   localContentLevel?: number;
//   sequenceId?: string;
//   canModify?: boolean;
//   section?: Section | null;
//   department?: Department | null;
// };
