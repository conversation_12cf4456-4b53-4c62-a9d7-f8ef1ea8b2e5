import { Usecase } from "../../framework/core.js";
import { BaseFindManyFilter, InputResponseWithCount } from "../../framework/repository.js";
import { FindRequisitionByRbtb, Requisition } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";
import { DocumentStatus, TypeOf } from "../model/vo.js";

class Gateways {
  findRequisitionByRbtb: FindRequisitionByRbtb;
}

export class Request extends BaseFindManyFilter {
  userLogin: UserLogin;
  departmentId?: string;
  year?: number;
  title?: string;
  status?: TypeOf<typeof DocumentStatus>;
  tenderCode?: string;
}

export class Response extends InputResponseWithCount<Requisition> {}

export const requisitionGetAllRbtb: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [rqs, count] = await o.findRequisitionByRbtb(ctx, {
      userId: req.userLogin.id,
      year: req.year,
      page: req.page,
      size: req.size,
      title: req.title,
      tenderCode: req.tenderCode,
    });

    return {
      items: rqs,
      count: count,
    };
  },
};
