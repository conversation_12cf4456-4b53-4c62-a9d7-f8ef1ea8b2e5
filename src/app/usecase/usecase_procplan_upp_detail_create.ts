import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import {
  FindProcPlanHeader,
  ProcPlanDetail,
  ProcPlanHeader,
  ProcplanDetailPayload,
  SaveProcPlanDetail,
  SaveProcPlanHeader,
  createNewProcPlanHeader,
  getLastProcplanHeader,
  getUPPErrorFields,
} from "../model/model_procplan.js";
import { FindSection } from "../model/model_section.js";
import { FindUser, FindUserSupervisors, User, UserLogin, getRBTBs, getRequestFor } from "../model/model_user.js";
import { DateNowHandler, RandomStringHandler } from "../model/vo.js";
import { DateOrString, getDateOnly, toUSD } from "../utility/helper.js";

class Gateways {
  saveProcPlanHeader: SaveProcPlanHeader;
  saveProcPlanDetail: SaveProcPlanDetail;
  saveApprovalGroups: SaveApprovalGroups;
  deleteApprovalGroups: DeleteApprovalGroups;
  findUser: FindUser;
  findUserSupervisors: FindUserSupervisors;
  findProcPlanHeader: FindProcPlanHeader;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findSection: FindSection;
  dateNow: DateNowHandler;
  randomString: RandomStringHandler;
}

export class Request {
  userLogin: UserLogin;
  // now: DateOrString;
  // newUPPHeaderId: string;
  // newUPPDetailId: string;
  procPlanDetail: ProcplanDetailPayload & {
    year: number;
    sectionId: string;
    requestForId: string;
    requesterBackToBackIds: string[];
  };
}

export class Response {
  procPlanHeaderId: string;
  procPlanDetailId: string;
}

export const procplanUppDetailCreate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // pastikan tahun pembuatan adalah tahun sekarang
    //
    const now = await o.dateNow(ctx);
    const newUPPHeaderId = await o.randomString(ctx);
    const newUPPDetailId = await o.randomString(ctx);

    if (!req.procPlanDetail.year) {
      throw new Error("year not specified");
    }

    // const year = getYear(req.procPlanDetail.year, now);
    const year = req.procPlanDetail.year;

    if (!req.procPlanDetail.sectionId) {
      throw new Error("sectionId not specified");
    }
    const [sections] = await o.findSection(ctx, { ids: [req.procPlanDetail.sectionId] });
    const section = sections.length > 0 ? sections[0] : null;
    if (!section) {
      throw new Error(`section ${req.procPlanDetail.sectionId} not found`);
    }

    // ambil requestFor
    const requestForUser = await getRequestFor(
      //
      ctx,
      o.findUser,
      req.procPlanDetail.requestForId,
      section.department?.id!,
      section.id
    );

    // ambil rbtb
    const rbtbs = await getRBTBs(
      //
      ctx,
      o.findUser,
      req.userLogin.id!,
      req.procPlanDetail.requestForId,
      req.procPlanDetail.requesterBackToBackIds,
      section.department?.id!
    );

    // ambil procplan header sebelumnya
    let pph = await getLastProcplanHeader(
      //
      ctx,
      o.findProcPlanHeader,
      section.department?.id!,
      section.id,
      year,
      "UPP"
    );

    let isFirstTimePPH = false;

    // kalau tidak ketemu
    if (!pph) {
      //
      isFirstTimePPH = true;

      // buat ProcplanHeader baru
      pph = createNewProcPlanHeader(
        //
        now,
        year,
        section.id,
        section.department?.id!,
        newUPPHeaderId,
        "UPP"
      );

      // simpan pph
      await o.saveProcPlanHeader(ctx, pph);
    }

    // procplan header harus dalam DRAFT status
    if (pph!.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    pph.sections.push(section);

    const errorFields = getUPPErrorFields(req.procPlanDetail);
    // validateProcPlanRequest(req.procPlanDetail);

    // create new procplan detail
    const ppd = createNewProcplanDetail(
      //
      req,
      pph,
      req.userLogin,
      requestForUser,
      rbtbs,
      now,
      section.id,
      section.department?.id!,
      `UPP-${newUPPDetailId}`,
      errorFields
    );

    // pada dasarnya kita ingin menyimpan procplan detail
    await o.saveProcPlanDetail(ctx, [ppd]);

    // increment count
    pph.count! += 1;

    // add totalValueEstimation
    // pph.totalValueEstimation += Number(ppd.valueEstimation);

    pph.totalValueEstimation.find((v) => v.currency === ppd.currency)!.value += Number(ppd.valueEstimation);

    // create approval group
    // if (isFirstTimePPH || ppd.requestFor?.id !== req.procPlanDetail.requestForId) {
    //   //

    //   // user including supervisors
    //   const [listUser] = await o.findUserSupervisors(ctx, { positionId: requestForUser.position!.id! });
    //   if (listUser.length === 0) {
    //     throw new Error(`user supervisor for UPP is not found`);
    //   }

    //   const description = `${section.department?.name}-${section.name}-${pph?.count!}Qty`;

    //   // buat approval
    //   const approvals = await getApprovalList(ctx, description, o.findUser, o.findApprovalTemplateGroup, listUser, pph.id, "PROC_PLAN_UPP");

    //   // rule hasNoApprover
    //   if (approvals.length === 3) {
    //     approvals[1].nextApprovalGroupId = null;
    //     approvals.pop();
    //   }

    //   await o.deleteApprovalGroups(ctx, { documentId: pph.id });

    //   // simpan approval
    //   await o.saveApprovalGroups(ctx, approvals);

    //   // pointer nunjuk ke approval pertama
    //   pph.approvalGroup = approvals[0];
    // }

    // update ProcplanHeader
    await o.saveProcPlanHeader(ctx, pph);

    return {
      procPlanHeaderId: pph.id,
      procPlanDetailId: ppd.id,
    };
  },
};

const createNewProcplanDetail = (
  req: Request,
  pph: ProcPlanHeader,
  userLogin: User,
  requestForUser: User,
  rbtbs: User[],
  now: DateOrString,
  sectionId: string,
  departmentId: string,
  newProcplanDetailID: string,
  errorFields: string[] | []
): ProcPlanDetail => {
  //

  now = getDateOnly(now);

  return {
    ...req.procPlanDetail,
    id: newProcplanDetailID,
    createdAt: now,
    creator: userLogin,
    requestFor: requestForUser,
    requesterBackToBack: rbtbs,
    year: req.procPlanDetail.year,
    procPlanHeader: pph,
    procPlanCode: "", // tidak didefine disini
    section: { id: sectionId },
    department: { id: departmentId },
    procPlanType: "UPP",
    usedByRequisitionIds: [],
    valueInUSD: toUSD(req.procPlanDetail.currency, req.procPlanDetail.valueEstimation),
    errorFields: errorFields,
  };
};
