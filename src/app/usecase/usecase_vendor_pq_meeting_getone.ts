import { Usecase } from "../../framework/core.js";
import { vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQTemplate, FindOnePQVendor, PQMeetingInfo } from "../model/model_prequalification.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  vendorCIVDLogin: vendorCIVDLogin;
  pqId: string;
}

export class Response {
  pqMeetingInfo: PQMeetingInfo | null;
}

export const vendorPQMeetingGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pq = await o.findOnePQTemplate(ctx, { pqId: req.pqId });

    if (!pq) {
      throw new Error(`PQ Template with id ${req.pqId} is not found`);
    }

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });

    if (!pqVendor) { 
      return { pqMeetingInfo: null };
    }

    if (!pq.pqMeetingInfo || pq.pqMeetingInfo.status !== "SUBMITTED" || pqVendor.phaseRegistration!.resultSummary !== "PASS") {
      return { pqMeetingInfo: null };
    } 

    return { pqMeetingInfo: pq.pqMeetingInfo };
  },
};
