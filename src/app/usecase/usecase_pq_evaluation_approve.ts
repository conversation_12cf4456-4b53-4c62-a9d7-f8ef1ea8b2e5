import { Context, Usecase } from "../../framework/core.js";
import {
  DeleteApprovalGroups,
  FindApprovalGroup,
  SaveApprovalGroups,
  getApproval,
  getApprovalWithRequestForRuleList,
  moveApproval,
  validateApprovalAction,
} from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import {
  FindOnePQTemplate,
  FindPQVendor,
  PrequalificationTemplate,
  PrequalificationVendor,
  SavePQTemplate,
  SavePQVendor,
} from "../model/model_prequalification.js";
import { FindRequisition, Requisition } from "../model/model_requisition.js";
import { FindUserSupervisors, UserLogin } from "../model/model_user.js";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DocumentTemplate, TypeOf } from "../model/vo.js";
import { markHistoryFiles } from "../utility/fileupload.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findPQVendor: FindPQVendor;
  savePQTemplate: SavePQTemplate;
  savePQVendor: SavePQVendor;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findRequisition: FindRequisition;
  deleteApprovalGroups: DeleteApprovalGroups;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  comment: string;
  index: "1" | "2" | "3";
}

export class Response {}

export const pqEvaluationApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== `EVALUATION_${req.index}`) {
      throw new Error("pq must be on Evaluation phase");
    }

    if (pqTemplate.phasesEvaluation![`evaluation${req.index}`]?.approval?.status !== "ON_REVIEW") {
      throw new Error("pq must in ON_REVIEW state");
    }

    validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.approvalGroup!);

    const approval = getApproval(pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.approvalGroup
      ? !!pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.approvalGroup?.approvals.every((x) => x.status === "DONE")
      : false;

    const autoApprove = await moveApproval(
      ctx,
      paralelAND,
      pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!,
      o.findApprovalGroup,
      o.saveApprovalGroups,
      o.findCalendar
    );

    if (autoApprove) {
      const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });
      for (const pqVendor of pqVendors) {
        pqVendor.phaseSubmission![`submission${req.index}`]!.evaluationDate = getDateOnly(now);

        await o.savePQVendor(ctx, pqVendor);
      }

      // generate next approvals
      const [rqs] = await o.findRequisition(ctx, { tenderCode: pqTemplate.tenderCode });
      if (!rqs || rqs.length === 0) {
        throw new Error("Requisition not found");
      }

      if (!rqs[0].requestFor) {
        throw new Error("Requisition requestFor user not found");
      }

      let description: string = "";
      let docType: TypeOf<typeof DocumentTemplate> = "PQ_FINAL_RESULT";

      if (pqTemplate.phasesEvaluation![`evaluation${req.index}`]?.response === "OK") {
        description = `${pqTemplate.title} - PQ Final Evaluation Result`;
        docType = "PQ_FINAL_RESULT";
        // TODO: logic on Evaluation Result???
      } else {
        if (req.index === "1" || req.index === "2") {
          await copyToNextEvaluation(ctx, o, rqs, pqTemplate, Number(req.index) as 1 | 2 | 3);
        } else {
          if (pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.response === "CL") {
            description = `${pqTemplate.title} - PQ Clarification`;
            docType = "PQ_CLARIFICATION";
            // TODO: logic for Clarification???
          }
        }
      }
    }

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: ("PQ_EVALUATION_" + req.index) as TypeOf<typeof DocumentTemplate>,
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Evaluation ${req.index} Approve`,
      user: req.userLogin,
      id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};

async function copyToNextEvaluation(ctx: Context, o: Gateways, rqs: Requisition[], pqTemplate: PrequalificationTemplate, index: 1 | 2 | 3) {
  const description = `${pqTemplate.title} - PQ Evaluation Phase ${index + 1}`;
  const docType = `PQ_EVALUATION_${index + 1}` as TypeOf<typeof DocumentTemplate>;

  const approvals = await getApprovalWithRequestForRuleList(
    ctx,
    description,
    pqTemplate.assignedUser!.position!.id!,
    rqs[0].requestFor!.position!.id!,
    o.findUserSupervisors,
    o.findApprovalTemplateGroup,
    pqTemplate.id,
    docType,
    new Date().getFullYear()
  );

  await o.deleteApprovalGroups(ctx, { documentId: pqTemplate.id, documentType: docType });
  await o.saveApprovalGroups(ctx, approvals);

  pqTemplate.phasesEvaluation![`evaluation${index + 1}` as "evaluation2" | "evaluation3"] = {
    ...pqTemplate.phasesEvaluation![`evaluation${index}`]!,
    response: "UNDECIDED",
    approval: {
      status: "DRAFT",
      isSendBack: false,
      approvalGroup: approvals[0],
    },
  };

  await o.savePQTemplate(ctx, pqTemplate);

  // Copy PQ Vendors Submission to Next Submission
  const [pqVendors] = await o.findPQVendor(ctx, { pqId: pqTemplate.id });
  for (const pqVendor of pqVendors) {
    const submission = pqVendor.phaseSubmission![`submission${index}`]!;

    pqVendor.phaseSubmission![`submission${index + 1}` as "submission2" | "submission3"] = {
      ...submission,
      suratPernyataanPQ: {
        ...submission.suratPernyataanPQ,
        files: markHistoryFiles(submission.suratPernyataanPQ.files),
      },
      suratKuasaPernyataanPQ: submission.suratKuasaPernyataanPQ
        ? {
            ...submission.suratKuasaPernyataanPQ,
            files: markHistoryFiles(submission.suratKuasaPernyataanPQ.files),
          }
        : null,
      suratSPDA: {
        ...submission.suratSPDA,
        files: markHistoryFiles(submission.suratSPDA.files),
      },
      dokumenBuktiStatusPDN: submission.dokumenBuktiStatusPDN
        ? {
            ...submission.dokumenBuktiStatusPDN,
            files: markHistoryFiles(submission.dokumenBuktiStatusPDN.files),
          }
        : null,
      dokumenDomisili: submission.dokumenDomisili
        ? {
            ...submission.dokumenDomisili,
            files: markHistoryFiles(submission.dokumenDomisili.files),
          }
        : null,
      suratIzinUsaha: {
        ...submission.suratIzinUsaha,
        files: markHistoryFiles(submission.suratIzinUsaha.files),
      },
      sertifikatTKDN: submission.sertifikatTKDN
        ? {
            ...submission.sertifikatTKDN,
            files: markHistoryFiles(submission.sertifikatTKDN.files),
          }
        : null,
      suratPerjanjianKonsorsium: submission.suratPerjanjianKonsorsium
        ? {
            ...submission.suratPerjanjianKonsorsium,
            files: markHistoryFiles(submission.suratPerjanjianKonsorsium.files),
          }
        : null,
      dokumenK3LL: {
        ...submission.dokumenK3LL,
        files: markHistoryFiles(submission.dokumenK3LL.files),
      },
      summaryExperiences: submission.summaryExperiences
        ? {
            ...submission.summaryExperiences,
            experiences: submission.summaryExperiences.experiences.map((x) => ({
              ...x,
              salinanKontrak: markHistoryFiles(x.salinanKontrak),
              dokumenBAST: markHistoryFiles(x.dokumenBAST),
              suratRingkasanPenagihan: markHistoryFiles(x.suratRingkasanPenagihan),
              dokumenKemampuanDasar: markHistoryFiles(x.dokumenKemampuanDasar),
            })),
          }
        : null,
      dokumenEvaluasiKemampuanFinansial: submission.dokumenEvaluasiKemampuanFinansial
        ? {
            ...submission.dokumenEvaluasiKemampuanFinansial,
            files: markHistoryFiles(submission.dokumenEvaluasiKemampuanFinansial.files),
          }
        : null,
      dokumenLaporanKeuangan: submission.dokumenLaporanKeuangan
        ? {
            ...submission.dokumenLaporanKeuangan,
            files: markHistoryFiles(submission.dokumenLaporanKeuangan.files),
          }
        : null,
      otherDocuments: (submission.otherDocuments ?? []).map((x) => ({
        ...x,
        files: markHistoryFiles(x.files),
      })),

      resultSummary: "UNDECIDED",
      submitDate: null,
      evaluationDate: null,
    };

    pqVendor.status = `SUBMISSION_${index + 1}_DRAFT` as PrequalificationVendor["status"];

    await o.savePQVendor(ctx, pqVendor);
  }
}
