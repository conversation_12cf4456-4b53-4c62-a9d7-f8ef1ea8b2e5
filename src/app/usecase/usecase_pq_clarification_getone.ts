import { Usecase } from "../../framework/core.js";
import { FindOnePQVendor, PrequalificationVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  pqId: string;
  vendorId: string;
}

export class Response {
  prequalificationVendor: PrequalificationVendor | null;
}

export const pqClarificationGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, req);
    return { prequalificationVendor: pqVendor };
  },
};
