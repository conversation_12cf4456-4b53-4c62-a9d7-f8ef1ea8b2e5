import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApproval } from "../model/model_approval.js";
import { FindRequisition, getCcEmailRequisition, getEmailDataRequisition, sendApprovalEmailRequisition } from "../model/model_requisition.js";
import { User, UserLogin } from "../model/model_user.js";
import { DocumentTemplate, TypeOf } from "../model/vo.js";
import { sendGeneralApprovalReminderMail } from "../utility/cron.js";
import { isUserAdmin } from "../utility/helper.js";
import { MailTableData, sendApprovalMail, sendAssignmentMail } from "../utility/mailer_2.js";

class Gateways {
  findApprovalGroup: FindApprovalGroup;
  findRequisition: FindRequisition;
  dataSource: () => Promise<any>;
}

export class Request {
  documentId: string;
  documentType: TypeOf<typeof DocumentTemplate>;
  resendGeneralApprovalMail: boolean;
  userLogin: UserLogin;
}

export class Response {}

export const approvalResendEmail: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // Check if user is admin
    if (!req.userLogin.email || !isUserAdmin(req.userLogin.email)) {
      throw new Error("Only admin users can perform this operation");
    }

    if (req.resendGeneralApprovalMail) {
      const ds = await o.dataSource();
      await sendGeneralApprovalReminderMail(ds);
      return {
        message: "General approval mail is resend",
      };
    }

    // Validate document type
    const validDocumentTypes = [
      // "PROC_PLAN_UPP",
      // "PROC_PLAN_APP",
      "REQUISITION",
      // "PQ_REQUIREMENT",
      // "PQ_REGISTRATION",
      // "PQ_EVALUATION_1",
      // "PQ_EVALUATION_2",
      // "PQ_EVALUATION_3",
      // "PQ_CLARIFICATION",
      // "PQ_EVALUATION_FINAL",
    ];

    if (!validDocumentTypes.includes(req.documentType)) {
      throw new Error("Invalid document type");
    }

    // Find current approval group for the document
    const [approvalGroups] = await o.findApprovalGroup(ctx, {
      documentId: req.documentId,
      documentType: req.documentType,
      // status: "PROCESSING"
    });

    // Handle different document types
    if (req.documentType === "REQUISITION") {
      //
      const [requisitions] = await o.findRequisition(ctx, { id: req.documentId });
      const requisition = requisitions.length > 0 ? requisitions[0] : null;

      if (!requisition) {
        throw new Error("Requisition not found");
      }

      // Check if requisition is approved (all approval groups are done)
      const isRequisitionApproved = requisition.status === "APPROVED";

      if (isRequisitionApproved && requisition.assignedUser) {
        const lastApprovalGroup = approvalGroups[approvalGroups.length - 1];
        const submittedByUser = lastApprovalGroup.approvals[0].signer!;
        const ccUsers = getCcEmailRequisition(requisition, submittedByUser);
        let mailData: MailTableData[] = getEmailDataRequisition(requisition, null);

        // If all approval groups are done, send assignment email to assigned user
        sendAssignmentMail(
          {
            sendToUserMail: requisition.assignedUser.email!,
            sendToUserName: requisition.assignedUser.name!,
            ccUserMail: ccUsers,
            submittedByUserName: submittedByUser.name,
          },
          mailData,
          "REQUISITION"
        );
      } else {
        // If still in approval process, send approval emails to current approvers
        const activeApprovalGroups = approvalGroups.filter((ag) => ag.status === "PROCESSING");

        if (activeApprovalGroups.length === 0) {
          throw new Error("No active approval found for this document");
        }

        const activeApprovalGroup = activeApprovalGroups[0];
        const prevApprovalGroup = approvalGroups.find((ag) => ag.sequence === activeApprovalGroup.sequence! - 1);

        if (activeApprovalGroup.approvals.some((app) => app.signer === null)) {
          let ccUsers = getCcEmailRequisition(requisition, null);
          sendApprovalEmailRequisition(activeApprovalGroup, requisition, ccUsers, req.userLogin);
        }
      }
    } else {
      throw new Error(`Email resend not yet implemented for document type: ${req.documentType}`);
    }

    return {};
  },
};
