import { Usecase } from "../../framework/core.js";
import { Delegation, FindDelegation, SaveDelegation } from "../model/model_delegation.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { DateNowHandler } from "../model/vo.js";
import { getDateOnly, isUserAdmin } from "../utility/helper.js";

class Gateways {
  findUser: FindUser;
  findDelegation: FindDelegation;
  dateNow: DateNowHandler;
  saveDelegation: SaveDelegation;
}

export class Request {
  userLogin: UserLogin;
  delegatorUserId: string;
  delegateToUserId: string;
  startDate: Date;
  endDate: Date;
  type: "DELEGATION" | "ACTING";
  remarks: string;
}

export class Response {}

export const delegationCreate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => { 
    // Check if user is admin
    if (!req.userLogin.email || !isUserAdmin(req.userLogin.email)) {
      throw new Error("Only admin users can perform this operation");
    }

    const now = await o.dateNow(ctx);
    let delegation: Delegation | null = null;

    if (!req.startDate) {
      throw new Error("startDate is required for DELEGATION type");
    }
    
    if (!req.endDate) {
      throw new Error("endDate is required for DELEGATION type");
    }

    if (req.type === "DELEGATION") {
      const [delegatorUsers] = await o.findUser(ctx, { ids: [req.delegatorUserId] });
      if (delegatorUsers.length === 0) {
        throw new Error("delegator user not found");
      }

      const delegatorUser = delegatorUsers[0];

      const [delegationExists] = await o.findDelegation(ctx, { ids: [delegatorUser.position?.id!], type: req.type });
      if (delegationExists.length > 0) {
        throw new Error("delegation already exists");
      }

      const [delegateToUsers] = await o.findUser(ctx, { ids: [req.delegateToUserId] });
      
      if (delegateToUsers.length === 0) {
        throw new Error("delegate to user not found");
      }

      const delegateToUser = delegateToUsers[0];
      
      delegation = {
        id: delegatorUser.position?.id!, // positionId
        delegateToUserId: delegateToUser.id, // userId
        delegatorPositionName: delegatorUser.position?.name!,
        delegatorEmail: delegatorUser.email,
        delegatorName: delegatorUser.name!,
        delegateToName: delegateToUser.name!,
        delegateToEmail: delegateToUser.email,
        delegateToUserPositionId: delegateToUser.position?.id,
        delegateToUserPositionName: delegateToUser.position?.name,
        startDate: req.startDate,
        endDate: req.endDate,
        type: req.type,
        remarks: req.remarks,
        syncAt: getDateOnly(now),
      }

    }

    if (delegation || delegation !== null) {
      await o.saveDelegation(ctx, delegation);
      return { message: `Delegation from ${delegation.delegatorName} to ${delegation.delegateToName} has been created`};
    }

    return {};
  },
};
