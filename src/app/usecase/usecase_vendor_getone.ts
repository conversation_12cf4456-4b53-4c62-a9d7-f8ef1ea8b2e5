import { Usecase } from "../../framework/core.js";
import { FindOneVendor, Vendor } from "../model/model_vendor.js";

class Gateways {
  findOneVendor: FindOneVendor;
}

export class Request {
  vendorId: string;
}

export class Response {
  vendor: Vendor;
}

export const vendorGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const vendor = await o.findOneVendor(ctx, req.vendorId);

    if (vendor === null) {
      throw new Error("vendor not found");
    }

    return { vendor };
  },
};
