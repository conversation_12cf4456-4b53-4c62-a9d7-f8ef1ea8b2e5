import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, TypeOf } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  comment: string;
  index: "1" | "2" | "3";
}

export class Response {}

export const pqEvaluationSendback: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== `EVALUATION_${req.index}`) {
      throw new Error("pq must be on Evaluation phase");
    }

    if (pqTemplate.phasesEvaluation![`evaluation${req.index}`]?.approval?.status !== "ON_REVIEW") {
      throw new Error("pq must in ON_REVIEW state");
    }

    validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.approvalGroup!);

    const docType = ("PQ_EVALUATION_" + req.index) as TypeOf<typeof DocumentTemplate>;
    const [approvals] = await o.findApprovalGroup(ctx, { documentId: pqTemplate.id, documentType: docType });
    approvals.forEach((apps, i) => {
      apps.approvals.forEach((app) => {
        app.date = null;
        app.signer = null;
        app.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
      });
      apps.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
    });

    await o.saveApprovalGroups(ctx, approvals);

    pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.status = "DRAFT";
    pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.isSendBack = true;
    pqTemplate.phasesEvaluation![`evaluation${req.index}`]!.approval!.approvalGroup = approvals[0];

    await o.savePQTemplate(ctx, pqTemplate);

    if (req.comment === "") {
      throw new Error("comment is required");
    }

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: ("PQ_EVALUATION_" + req.index) as TypeOf<typeof DocumentTemplate>,
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Evaluation ${req.index} Sent Back`,
      user: req.userLogin,
      id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};
