import { Usecase } from "../../framework/core.js";
import { FindUser, User } from "../model/model_user.js";
import { isUserAdmin, isUserAdminProcPlan, isUserAdminRequisition } from "../utility/helper.js";

class Gateways {
  findUser: FindUser;
}

export class Request {
  username: string;
  password: string;
}

export class Response {
  user: User;
  isAdmin: boolean;
  isAdminProcPlan: boolean;
  isAdminRequisition: boolean;
}

export const loginSimple: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //
    if (!req.username || !req.password) {
      throw new Error("missing username or password");
    }

    if (req.password !== "12345") {
      throw new Error("Invalid username or password");
    }

    let [users] = await o.findUser(ctx, {
      emails: [req.username],
    });

    if (users.length === 0) {
      [users] = await o.findUser(ctx, {
        nameLike: req.username,
      });
    }

    if (users.length === 0) {
      throw new Error("User not found");
    }

    return {
      user: users[0],
      isAdmin: isUserAdmin(users[0].email!),
      isAdminProcPlan: isUserAdminProcPlan(users[0]),
      isAdminRequisition: isUserAdminRequisition(users[0]),
    };
  },
};
