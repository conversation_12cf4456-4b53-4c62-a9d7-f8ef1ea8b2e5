import { Usecase } from "../../framework/core.js";
import { FindOnePQTemplate, FindOnePQVendor, PrequalificationVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  vendorPqId: string;
}

export class Response {
  prequalificationVendor: PrequalificationVendor | null;
}

export const pqRegistrationGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { vendorPqId: req.vendorPqId });
    if (!pqVendor) {
      return { prequalificationVendor: null };
    }

    const pq = pqVendor.prequalificationTemplate;
    if (!pq) {
      throw new Error(`PQ Vendor with id ${pqVendor.id} is not found`);
    }

    return { prequalificationVendor: pqVendor };
  },
};

// TODO:: pq vendor registration only