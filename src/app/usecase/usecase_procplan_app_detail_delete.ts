import { Usecase } from "../../framework/core.js";
import { validateApprovalAction } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { DeleteProcPlanDetail, FindProcPlanDetail, SaveProcPlanHeader, getExistingProcPlanDetail } from "../model/model_procplan.js";
import { UserLogin } from "../model/model_user.js";

class Gateways {
  saveProcPlanHeader: SaveProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  deleteProcPlanDetail: DeleteProcPlanDetail;
}

export class Request {
  userLogin: UserLogin;
  detailId: string;
}

export class Response { }

export const procplanAppDetailDelete: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const ppd = await getExistingProcPlanDetail(ctx, o.findProcPlanDetail, req.detailId, "APP");

    let pph = ppd?.procPlanHeader;
    if (!pph) {
      throw new Error("procplan header APP not found");
    }

    if (pph.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    const [atg] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });
    validateApprovalAction(req.userLogin, pph.approvalGroup, atg[0]);

    pph.totalValueEstimation.find((v) => v.currency === ppd.currency)!.value -= Number(ppd.valueEstimation);

    await o.deleteProcPlanDetail(ctx, { id: req.detailId });

    pph.count! -= 1;

    await o.saveProcPlanHeader(ctx, pph);

    return {};
  },
};
