import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApprovalWithRequestForRuleList, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQTemplate, FindOnePQVendor, SavePQTemplate, SavePQVendor } from "../model/model_prequalification.js";
import { FindRequisition } from "../model/model_requisition.js";
import { FindUser, FindUserSupervisors } from "../model/model_user.js";
import { DateNowHandler, RandomStringHandler, SharepointFile } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findOneVendorCIVD: FindOneVendorCIVD;
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  savePQTemplate: SavePQTemplate;
  dateNow: DateNowHandler;
  randomString: RandomStringHandler;
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  findRequisition: FindRequisition;
  findUserSupervisors: FindUserSupervisors;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
  clarificationPayload: {
    files: SharepointFile[];
    date: Date;
    number: string;
    subject: string;
  };
}

export class Response {}

export const vendorPQClarificationSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const vendor = await o.findOneVendorCIVD(ctx, req.vendorCIVDLogin.civdVendorId);
    if (!vendor) {
      throw new Error("vendor not found");
    }

    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqTemplate.pqRegistrationDate) {
      throw new Error("pq registration date not found");
    }

    // validate pq registration due date
    if (getDateOnly(now) > pqTemplate.pqRegistrationDate) {
      // throw new Error("pq registration date is expired");
    }

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    if (!pqVendor || !pqVendor.phaseRegistration) {
      throw new Error("pq vendor not found");
    }

    if (!pqVendor.phaseSubmission) {
      throw new Error("pq vendor does not SUBMIT the Submission phase");
    }

    const isAnyPhaseNotPass = Object.keys(pqVendor.phaseSubmission || {}).some((phase) => {
      const phaseSubmission = pqVendor.phaseSubmission![phase as keyof typeof pqVendor.phaseSubmission];
      if (phaseSubmission && phaseSubmission.resultSummary !== "PASS") {
        return true;
      }
      return false;
    });

    if (isAnyPhaseNotPass) {
      throw new Error("pq vendor does not PASS the Submission phase");
    }

    if (pqVendor.status === "CLARIFICATION_SUBMIT") {
      throw new Error("pq clarification already submitted");
    }

    var phaseSubmission = null;
    if (pqVendor.phaseSubmission!.submission3 && pqVendor.phaseSubmission!.submission3!.resultSummary === "FAIL") {
      phaseSubmission = pqVendor.phaseSubmission!.submission3;
    } else if (pqVendor.phaseSubmission!.submission2 && pqVendor.phaseSubmission!.submission2!.resultSummary === "FAIL") {
      phaseSubmission = pqVendor.phaseSubmission!.submission2;
    } else if (pqVendor.phaseSubmission!.submission1 && pqVendor.phaseSubmission!.submission1!.resultSummary === "FAIL") {
      phaseSubmission = pqVendor.phaseSubmission!.submission1;
    } else {
      throw new Error("pq vendor cannot submit Clarification phase");
    }

    pqVendor.phaseClarification = {
      ...phaseSubmission,
      clarificationDocuments: {
        ...phaseSubmission,
        submitDate: getDateOnly(now),
        resultSummary: "UNDECIDED",
      },
      clarificationLetter: req.clarificationPayload,
      meetingInfo: null,
    };

    await o.savePQVendor(ctx, pqVendor);

    const [approvalGroup, countApprovalGroup] = await o.findApprovalGroup(ctx, { documentId: pqTemplate.id, documentType: "PQ_CLARIFICATION" });

    if (!approvalGroup || countApprovalGroup === 0) {
      const newPqId = `PQ-${await o.randomString(ctx)}`;
      const [rqs] = await o.findRequisition(ctx, { tenderCode: pqTemplate.tenderCode });

      const assignedUser = pqTemplate.assignedUser;
      const requestForUser = rqs[0].requestFor;

      // const approvals = await getApprovalList(ctx, `Approval for PQ Clarification Evaluation ${newPqId}`, o.findUser, o.findApprovalTemplateGroup, [], newPqId, "PQ_CLARIFICATION");
      const approvals = await getApprovalWithRequestForRuleList(
        ctx,
        `Approval for PQ Clarification Evaluation ${newPqId}`,
        assignedUser?.position?.id!,
        requestForUser?.position?.id!,
        o.findUserSupervisors,
        o.findApprovalTemplateGroup,
        newPqId,
        "PQ_CLARIFICATION",
        new Date().getFullYear()
      );

      approvals[0].status = "NOT_STARTED";
      approvals[0].approvals[0].status = "NOT_STARTED";

      await o.saveApprovalGroups(ctx, approvals);

      pqTemplate.phasesClarification = {
        response: "UNDECIDED",
        dueDate: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days after today
        approval: {
          status: "DRAFT",
          isSendBack: false,
          approvalGroup: approvals[0],
        },
      };

      await o.savePQTemplate(ctx, pqTemplate);
    }

    return {};
  },
};
