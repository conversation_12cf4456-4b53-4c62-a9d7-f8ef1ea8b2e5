import { Usecase } from "../../framework/core.js";
import { BaseFindManyFilter, InputResponseWithCount } from "../../framework/repository.js";
import { Department } from "../model/model_department.js";
import { FindProcPlanDetailByFromExcludeDepartment } from "../model/model_procplan.js";
import { Section } from "../model/model_section.js";
import { UserLogin } from "../model/model_user.js";
import { Currency, TenderMethod, TypeOf } from "../model/vo.js";

class Gateways {
  findProcPlanDetailByFromExcludeDepartment: FindProcPlanDetailByFromExcludeDepartment;
}

export class Request extends BaseFindManyFilter {
  userLogin: UserLogin;
  year: number;
}

export class Response extends InputResponseWithCount<Detail> { }

export const procplanUppHeaderGetOne4: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [procPlanDetail] = await o.findProcPlanDetailByFromExcludeDepartment(ctx, {
      userId: req.userLogin.id,
      departmentId: req.userLogin.department?.id,
      year: req.year,
      page: req.page,
      size: req.size,
    });

    return {
      items:
        procPlanDetail.map((x) => ({
          id: x.id,
          title: x.title,
          tenderMethod: x.tenderMethod,
          contractDateStart: x.contractDateStart,
          contractDateEnd: x.contractDateEnd,
          poDateIssuance: x.poDateIssuance,
          poDateDelivery: x.poDateDelivery,
          currency: x.currency,
          valueEstimation: x.valueEstimation,
          localContentLevel: x.localContentLevel,
          section: x.section,
          department: x.department,
          canModify: x.procPlanHeader?.status === "DRAFT",
          masterList: x.masterList,
        })) || [],
      count: procPlanDetail.length,
    };
  },
};

type Detail = {
  id?: string;
  title?: string;
  tenderMethod?: TypeOf<typeof TenderMethod>;
  contractDateStart?: Date | null;
  contractDateEnd?: Date | null;
  poDateIssuance?: Date | null;
  poDateDelivery?: Date | null;
  currency?: TypeOf<typeof Currency>;
  valueEstimation?: number;
  localContentLevel?: number;
  sequenceId?: string;
  section?: Section | null;
  department?: Department | null;
  canModify?: boolean;
  masterList?: boolean;
};
