import { Usecase } from "../../framework/core.js";
import { FindOnePQTemplate, PrequalificationTemplate } from "../model/model_prequalification.js";
import { FindRequisition } from "../model/model_requisition.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findRequisition: FindRequisition;
}

export class Request {
  pqId: string;
  headerOnly: boolean;
}

export class Response {
  prequalification: PrequalificationTemplate | null;
  header: PQHeaderOnly | null;
}

export const pqGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pq = await o.findOnePQTemplate(ctx, req);

    if (!pq) {
      return {
        prequalification: null,
        header: null,
      };
    }

    const [rqs, countRqs] = await o.findRequisition(ctx, {
      tenderCode: pq.tenderCode,
    });

    const headerOnly: PQHeaderOnly = {
      requisitionId: rqs[0].id,
      tenderCode: pq.tenderCode,
      title: pq.title,
      generalScopeOfWorks: pq.generalScopeOfWorks,
      currentPhase: pq.currentPhase,
      contractDateStart: pq.contractDateStart,
      contractDateEnd: pq.contractDateEnd,
      poDateIssuance: pq.poDateIssuance,
      poDateDelivery: pq.poDateDelivery,
      assignmentDate: pq.assignmentDate,
      announcementDate: pq.announcementDate,
      pqMeetingDate: pq.pqMeetingDate,
      pqRegistrationDate: pq.pqRegistrationDate,
      pqSubmissionDate: pq.pqSubmissionDate,
    }

    const result: PrequalificationTemplate | null = !req.headerOnly ? pq : null;
    const header: PQHeaderOnly | null = req.headerOnly ? headerOnly : null;

    return {
      prequalification: result,
      header: header,
    };
  },
};

type PQHeaderOnly = PQHeader & { requisitionId: string; }

type PQHeader = Pick<
  PrequalificationTemplate,
  | "tenderCode"
  | "title"
  | "generalScopeOfWorks"
  | "currentPhase"
  | "contractDateStart"
  | "contractDateEnd"
  | "poDateIssuance"
  | "poDateDelivery"
  | "assignmentDate"
  | "announcementDate"
  | "pqMeetingDate"
  | "pqRegistrationDate"
  | "pqSubmissionDate"
>;