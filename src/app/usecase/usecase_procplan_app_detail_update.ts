import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, isCurrentUserIsPBR, validateApprovalAction } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import {
  FindProcPlanDetail,
  ProcPlanDetail,
  ProcplanDetailPayload,
  SaveProcPlanDetail,
  SaveProcPlanHeader,
  getExistingProcPlanDetail,
  getUPPErrorFields,
} from "../model/model_procplan.js";
import { UserLogin } from "../model/model_user.js";
import { toUSD } from "../utility/helper.js";

class Gateways {
  findProcPlanDetail: FindProcPlanDetail;
  findApprovalGroup: FindApprovalGroup;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  saveProcPlanHeader: SaveProcPlanHeader;
  saveProcPlanDetail: SaveProcPlanDetail;
}

export class Request {
  userLogin: UserLogin;
  detailId: string;
  procPlanDetail: ProcplanDetailPayload & {
    year: number;
    departmentId: string;
    sectionId: string;
  };
}

export class Response {}

export const procplanAppDetailUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const ppd = await getExistingProcPlanDetail(ctx, o.findProcPlanDetail, req.detailId, "APP");

    let pph = ppd.procPlanHeader;
    if (!pph) {
      throw new Error("procplan Header APP not found");
    }

    if (pph.year !== Number(req.procPlanDetail.year)) {
      throw new Error("Invalid year for this procplan");
    }

    if (!(await isCurrentUserIsPBR(ctx, req.userLogin, o.findApprovalGroup, pph.id, "PROC_PLAN_APP"))) {
      if (pph!.status !== "DRAFT") {
        throw new Error("proc plan must in DRAFT state");
      }
    }

    const [approvalTemplateGroup] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });

    validateApprovalAction(req.userLogin, pph.approvalGroup, approvalTemplateGroup[0]);

    const errorFields = getUPPErrorFields(req.procPlanDetail);
    // validateProcPlanRequest(req.procPlanDetail);

    ppd.errorFields = errorFields;

    pph.totalValueEstimation.find((v) => v.currency === ppd.currency)!.value -= Number(ppd.valueEstimation);

    pph.totalValueEstimation.find((v) => v.currency === ppd.currency)!.value += Number(req.procPlanDetail.valueEstimation);

    await o.saveProcPlanHeader(ctx, pph);

    const updatedPpd: ProcPlanDetail = {
      ...ppd,
      ...req.procPlanDetail,
      valueInUSD: toUSD(req.procPlanDetail.currency, req.procPlanDetail.valueEstimation),
    };

    await o.saveProcPlanDetail(ctx, [updatedPpd]);

    return {};
  },
};
