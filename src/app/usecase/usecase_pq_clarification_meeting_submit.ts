import { Usecase } from "../../framework/core.js";
import { getA<PERSON><PERSON><PERSON><PERSON>ist, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindOnePQTemplate, FindOnePQVendor, FindPQVendor, SavePQVendor } from "../model/model_prequalification.js";
import { FindUser } from "../model/model_user.js";
import { DateNowHandler, RandomStringHandler } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
  findPQVendor: FindPQVendor;
  savePQVendor: SavePQVendor;
  dateNow: DateNowHandler;
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  randomString: RandomStringHandler;
  saveApprovalGroups: SaveApprovalGroups;
}

export class Request {
  pqId: string;
  civdVendorId: number;
  meetingInfo: {
    place: string;
    meetingDate: Date;
    meetingTime: string;
    minutesOfMeeting: string[];
  };
}

export class Response {}

export const pqClarificationMeetingSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.civdVendorId });
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqVendor) {
      throw new Error("pq vendor not found");
    }

    // if (pqVendor.status !== "EVALUATION_APPROVED") {
    //   throw new Error("pq vendor must be EVALUATION_APPROVED state");
    // }

    if (pqVendor.phaseClarification?.meetingInfo && pqVendor.phaseClarification?.meetingInfo.status !== "SUBMITTED") {
      throw new Error("pq vendor meeting already be submitted");
    }

    pqVendor.status = "CLARIFICATION_APPROVED";
    pqVendor.phaseClarification = {
      ...pqVendor.phaseClarification!,
      meetingInfo: {
        ...req.meetingInfo,
        status: "SUBMITTED",
      },
    };

    await o.savePQVendor(ctx, pqVendor);

    return {};
  },
};
