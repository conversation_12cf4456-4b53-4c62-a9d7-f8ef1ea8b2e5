import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { collectDepartments, Department, FindDepartment } from "../model/model_department.js";
import { FindUser, UserLogin } from "../model/model_user.js";

class Gateways {
  findDepartment: FindDepartment;
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
}

export class Request {
  userLogin: UserLogin;
  isApp: boolean;
  isRequisition: boolean;
}

export class Response extends InputResponseWithCount<Department> {
  isUserVM: boolean;
}

export const departmentGetAllByUser: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // vendor management & admin procplan
    if (req.isApp) {
      const [atgs] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });
      const firstApprovalTemplateGroup = atgs[0].approvalTemplates.find((at) => at.users?.find((x) => x.id === req.userLogin.id));

      if (!!firstApprovalTemplateGroup || req.userLogin.isAdminProcPlan) {
        const [items, count] = await o.findDepartment(ctx, {});
        return { items, count, isUserVM: true };
      }
    }

    // admin requisition
    if (req.isRequisition) {
      if (req.userLogin.isAdminRequisition) {
        const [items, count] = await o.findDepartment(ctx, {});
        return { items, count, isUserVM: true };
      }
    }

    const deptSet = new Set<string>();
    await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);
    const [items, count] = deptSet.size > 0 ? await o.findDepartment(ctx, { ids: [...deptSet] }) : [[], 0];

    return { items, count, isUserVM: false };
  },
};
