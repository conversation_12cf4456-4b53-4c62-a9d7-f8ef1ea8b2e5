import { Usecase } from "../../framework/core.js";
import { FindUser, SaveEmailUser } from "../model/model_user.js";

class Gateways {
  findUser: FindUser;
  saveEmailUser: SaveEmailUser;
}

export class Request {
  userId: string;
  email: string;
}

export class Response { }

export const userEmailUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [users] = await o.findUser(ctx, { ids: [req.userId] });
    const user = users[0];

    if (!user) {
      return {};
    }

    if (!user.email || user.email === "") {
      user.email = req.email;
      await o.saveEmailUser(ctx, user);
    }

    return {};
  },
};
