import { Usecase } from "../../framework/core.js";
import { FindOneVendor, Vendor } from "../model/model_vendor.js";
import jwt from "jsonwebtoken";

class Gateways {
  findOneVendor: FindOneVendor
}

export class Request {
  idVendor: string;
  password: string;
}

export class Response {
  // vendor: Vendor;
  // token: string;
  user: Vendor;
}

export const loginVendor: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {

    if (!req.idVendor) {
      throw new Error("missing idVendor");
    }
    
    if (!req.password) {
      throw new Error("missing password");
    }

    const vendor = await o.findOneVendor(ctx, req.idVendor);

    if (!vendor) {
      throw new Error("Vendor not found");
    }

    // validate access apps
    if (!vendor.accessApps || !vendor.accessApps.includes("procurement")) {
      throw new Error("Vendor does not have access to procurement app"); 
    }

    // const payload = { data: vendor };
    // const expiration = { expiresIn: process.env.TOKEN_EXPIRATION };
    // const token = jwt.sign(payload, process.env.TOKEN_SECRET_KEY_VENDOR as string, expiration);

    return {
      user: vendor,
      // token: token,
    };
  },
};
