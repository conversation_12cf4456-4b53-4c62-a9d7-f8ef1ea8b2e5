import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { Department, FindDepartment, FindDepartmentFilter } from "../model/model_department.js";

class Gateways {
  findDepartment: FindDepartment;
}

export class Request extends FindDepartmentFilter {}

export class Response extends InputResponseWithCount<Department> {}

export const departmentGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findDepartment(ctx, req);
    return { items, count };
  },
};
