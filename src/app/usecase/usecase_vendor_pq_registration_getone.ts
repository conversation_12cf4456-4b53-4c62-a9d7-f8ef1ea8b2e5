import { Usecase } from "../../framework/core.js";
import { vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQVendor, VendorPhasePQRegistration } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
}

export class Response {
  prequalificationVendor: VendorPhasePQRegistration | null;
}

export const vendorPQRegistrationGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });

    if (!pqVendor) { 
      return { prequalificationVendor: null };
    }

    const pqVendorResult: VendorPhasePQRegistration = {
      suratMinat: pqVendor.phaseRegistration!.suratMinat,
      suratKuasaMinat: pqVendor.phaseRegistration!.suratKuasaMinat ? pqVendor.phaseRegistration!.suratKuasaMinat : null,
      suratSPDA: pqVendor.phaseRegistration!.suratSPDA,
      dokumenDomisili: pqVendor.phaseRegistration!.dokumenDomisili,
      suratIzinUsaha: pqVendor.phaseRegistration!.suratIzinUsaha,
      sertifikatTKDN: pqVendor.phaseRegistration!.sertifikatTKDN,
      resultSummary: pqVendor.phaseRegistration!.resultSummary,
      submitDate: pqVendor.phaseRegistration!.submitDate,
      evaluationDate: pqVendor.phaseRegistration!.evaluationDate
    }

    return { prequalificationVendor: pqVendorResult };
  },
};
