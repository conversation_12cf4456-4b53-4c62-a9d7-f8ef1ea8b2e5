import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup } from "../model/model_approval.js";
import { FindProcPlanDetail, ProcPlanDetail } from "../model/model_procplan.js";
import { UserLogin } from "../model/model_user.js";

class Gateways {
  findProcPlanDetail: FindProcPlanDetail;
  findApprovalGroup: FindApprovalGroup;
}

export class Request {
  userLogin: UserLogin;
  detailId: string;
}

export class Response {
  procPlanDetail: ProcPlanDetail | null;
}

export const procplanAppDetailGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const [procPlanDetails] = await o.findProcPlanDetail(ctx, { id: req.detailId, procPlanType: "APP" });

    const procPlanDetail = procPlanDetails.length > 0 ? procPlanDetails[0] : null;

    // const departmentId = procPlanDetail?.procPlanHeader?.department?.id;

    // if (departmentId !== req.userLogin.department?.id) {
    //   //
    //   const [approvalGroups] = await o.findApprovalGroup(ctx, { documentId: procPlanDetail?.procPlanHeader?.id });

    //   if (
    //     approvalGroups.some((ag) =>
    //       ag.approvals.some((app) => app.users?.some((u) => u.id === req.userLogin.id) || app.position?.id === req.userLogin.position?.id)
    //     )
    //   ) {
    //     return { procPlanDetail };
    //   }

    //   return { procPlanDetail: null };
    // }

    return { procPlanDetail };
  },
};
