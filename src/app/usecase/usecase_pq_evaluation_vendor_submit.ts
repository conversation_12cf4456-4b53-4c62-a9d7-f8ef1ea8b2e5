import { Usecase } from "../../framework/core.js";
import { FindOnePQVendor, SavePQVendor } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  vendorPqId: string;
  index: "1" | "2" | "3";
}

export class Response {}

export const pqEvaluationVendorSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { vendorPqId: req.vendorPqId });

    if (!pqVendor) {
      throw new Error("pq not found");
    }

    if (pqVendor.prequalificationTemplate.currentPhase !== `EVALUATION_${req.index}`) {
      throw new Error("pq must be on Evaluation phase");
    }

    if (pqVendor.status !== `SUBMISSION_${req.index}_SUBMITTED` || pqVendor.status !== `SUBMISSION_${req.index}_EVALUATED`) {
      throw new Error("pq must be on Evaluation phase");
    }

    if (pqVendor.phaseSubmission![`submission${req.index}`]?.resultSummary === "UNDECIDED") {
      throw new Error("There is document with 'Undecided' result");
    }

    pqVendor.status = `SUBMISSION_${req.index}_EVALUATED`;

    await o.savePQVendor(ctx, pqVendor);

    return {};
  },
};
