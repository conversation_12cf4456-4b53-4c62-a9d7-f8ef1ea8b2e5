import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { CIVDVendor, FindVendorCIVD, transformCIVDVendors } from "../model/model_civd_vendor.js";
import { FindVendorFilter } from "../model/model_vendor.js";
import { BusinessClass, TypeOf } from "../model/vo.js";
import { translateBusinessClassToEn } from "../utility/helper.js";

class Gateways {
  findVendorCIVD: FindVendorCIVD;
}

export class Request extends FindVendorFilter { }

export class Response extends InputResponseWithCount<CIVDVendor> { }

export const vendorCivdGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findVendorCIVD(ctx, req);

    const results = transformCIVDVendors(items);

    return {
      items: results,
      count,
    };
  },
};