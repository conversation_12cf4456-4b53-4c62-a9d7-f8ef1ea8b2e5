import { Usecase } from "../../framework/core.js";
import { DeleteProcPlanDetail, FindProcPlanDetail, SaveProcPlanHeader, getExistingProcPlanDetail } from "../model/model_procplan.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { validateActionByID } from "../model/vo.js";

class Gateways {
  findUser: FindUser;
  findProcPlanDetail: FindProcPlanDetail;
  saveProcPlanHeader: SaveProcPlanHeader;
  deleteProcPlanDetail: DeleteProcPlanDetail;
}

export class Request {
  userLogin: UserLogin;
  detailId: string;
}

export class Response { }

export const procplanUppDetailDelete: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // ambil detailnya
    // const ppd = await getExistingProcPlanDetail(ctx, findProcPlanDetail, req.userLogin.department?.id!, req.sectionId, req.sequenceId, req.year);
    const ppd = await getExistingProcPlanDetail(ctx, o.findProcPlanDetail, req.detailId, "UPP");

    // ambil headernya
    const pph = ppd.procPlanHeader;
    if (!pph) {
      throw new Error("Procplan Header UPP not found");
    }

    // make sure dalam status DRAFT
    if (pph.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    const [sectionUsers] = await o.findUser(ctx, {
      sectionId: pph.submitter?.section!.id!,
    });

    validateActionByID(req.userLogin.id, [
      //
      ...(sectionUsers.map((x) => x.id) as string[]),
      ppd.creator!.id,
      ppd.requestFor!.id,
      ...ppd.requesterBackToBack.map((x) => x.id),
    ],
      req.userLogin.section!.id,
      pph.section!.id
    );

    // reduce total
    // pph.totalValueEstimation -= Number(ppd.valueEstimation);

    pph.totalValueEstimation.find((v) => v.currency === ppd.currency)!.value -= Number(ppd.valueEstimation);

    await o.deleteProcPlanDetail(ctx, { id: req.detailId });

    pph.count! -= 1;

    await o.saveProcPlanHeader(ctx, pph);

    return {};
  },
};
