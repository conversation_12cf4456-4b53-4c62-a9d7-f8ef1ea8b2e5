import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindVendor, FindVendorFilter, Vendor } from "../model/model_vendor.js";

class Gateways {
  findVendor: FindVendor;
}

export class Request extends FindVendorFilter {}

export class Response extends InputResponseWithCount<Vendor> {}

export const vendorGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findVendor(ctx, req);
    return { items, count };
  },
};
