import { Context, Usecase } from "../../framework/core.js";
import { BaseFindManyFilter, InputResponseWithCount } from "../../framework/repository.js";
import { FindApprovalGroup, getLastApprovalGroupDurationDays } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindCalendar } from "../model/model_calendar.js";
import { Department, FindDepartment, collectDepartments } from "../model/model_department.js";
import { FindProcPlanDetail, FindProcPlanHeader, ProcPlanHeader } from "../model/model_procplan.js";
import { FindUser, User, UserLogin } from "../model/model_user.js";
import { DateNowHandler, getEmptyValueCurrency } from "../model/vo.js";

class Gateways {
  findDepartment: FindDepartment;
  findProcPlanHeader: FindProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findApprovalGroup: FindApprovalGroup;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request extends BaseFindManyFilter {
  userLogin: UserLogin;
  year?: number;
}

export class Response extends InputResponseWithCount<ResultProcPlanHeader> { }

export const procplanAppHeaderGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const [atgs] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });

    const firstApprovalTemplateGroup =
      atgs[0].approvalTemplates.find((at) => at.users?.find((x) => x.id === req.userLogin.id)) ||
      req.userLogin.department?.name === "Planning, Bidding & Reporting" || req.userLogin.isAdminProcPlan;
    // console.log(req.userLogin);
    if (!!firstApprovalTemplateGroup) {
      //
      const [departments] = await o.findDepartment(ctx, {});

      const [pphs] = await o.findProcPlanHeader(ctx, {
        year: req.year,
        departmentIds: departments.map((d) => d.id),
        procPlanType: "APP",
        useSelect: true,
      });

      const results: ResultProcPlanHeader[] = [];
      for (const department of departments) {
        const pph = pphs.find((d) => d.department?.id === department.id);
        if (!pph) {
          results.push(emptyProcplanHeader(department));
        } else {
          pph.durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, pph.approvalGroup, now, o.findCalendar);
          const valueInUSD = await sumPphDetailValueInUSD(ctx, pph, o.findProcPlanDetail);
          results.push({ ...pph, valueInUSD: valueInUSD });
        }
      }

      return {
        items: results,
        count: results.length,
      };
    } else {
      const deptSet = new Set<string>();
      await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);
      const [departments] = deptSet.size > 0 ? await o.findDepartment(ctx, { ids: [...deptSet] }) : [[]];

      const [pphs] = await o.findProcPlanHeader(ctx, {
        year: req.year,
        departmentIds: departments.map((d) => d.id),
        procPlanType: "APP",
        useSelect: true,
      });

      const results: ResultProcPlanHeader[] = [];
      for (const department of departments) {
        //
        const pph = pphs.find((d) => d.department?.id === department.id);
        if (!pph) {
          results.push(emptyProcplanHeader(department));
        } else {
          pph.durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, pph.approvalGroup, now, o.findCalendar);
          const valueInUSD = await sumPphDetailValueInUSD(ctx, pph, o.findProcPlanDetail);
          results.push({ ...pph, valueInUSD: valueInUSD });
        }
        //
      }

      return {
        items: results,
        count: results.length,
      };
    }
  },
};

type ResultProcPlanHeader = ProcPlanHeader & { valueInUSD: number };

const emptyProcplanHeader = (department: Department): ProcPlanHeader & { valueInUSD: number } => {
  return {
    id: "",
    procPlanType: "APP",
    status: "NOT_CREATED",
    isSendBack: false,
    submittedDate: null,
    year: 0,
    count: 0,
    section: null,
    department: department,
    approvalGroup: null,
    totalValueEstimation: getEmptyValueCurrency(),
    valueInUSD: 0,
    submitter: null,
    sections: [],
  };
};

const sumPphDetailValueInUSD = async (ctx: Context, pph: ProcPlanHeader, findProcPlanDetail: FindProcPlanDetail): Promise<number> => {
  const [ppds] = await findProcPlanDetail(ctx, { procPlanHeaderId: pph.id });
  let value: number = 0;
  for (const ppd of ppds) {
    value += Number(ppd.valueInUSD);
  }
  return value;
};
