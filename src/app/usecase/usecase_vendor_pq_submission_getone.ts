import { Usecase } from "../../framework/core.js";
import { vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQTemplate, FindOnePQVendor, PQSubmissionInfo } from "../model/model_prequalification.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
  submissionPhase: number | 1 | 2 | 3;
}

export class Response {
  prequalificationVendor: {
    id: string;
    status: string;
    phaseSubmission: PQSubmissionInfo | null;
  } | null;
}

export const vendorPQSubmissionGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error(`pq with ${req.pqId} not found`);
    }

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });

    if (!pqVendor || !pqVendor.phaseSubmission) {
      return { prequalificationVendor: null };
    }

    let submissionPhase: PQSubmissionInfo | null = null;
    if (req.submissionPhase == 1) {
      submissionPhase = pqVendor.phaseSubmission.submission1!;
    } else if (req.submissionPhase == 2) {
      submissionPhase = pqVendor.phaseSubmission.submission2!;
    } else if (req.submissionPhase == 3) {
      submissionPhase = pqVendor.phaseSubmission.submission3!;
    } else {
      throw new Error("invalid submission phase");
    }

    const prequalificationVendor = {
      id: pqVendor.id,
      status: pqVendor.status,
      phaseSubmission: submissionPhase,
    }

    return { prequalificationVendor: prequalificationVendor };
  },
};

