import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindApprovalGroup, getLastApprovalGroupDurationDays } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { collectDepartments } from "../model/model_department.js";
import { FindRequisition, Requisition } from "../model/model_requisition.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentStatus, TypeOf, validateActionByID } from "../model/vo.js";

class Gateways {
  findRequisition: FindRequisition;
  findUser: FindUser;
  findApprovalGroup: FindApprovalGroup;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  departmentId?: string;
  sectionId?: string;
  year?: number;
  title?: string;
  page?: number;
  size?: number;
  status?: TypeOf<typeof DocumentStatus>;
  assigned?: boolean;
  assignedBTB?: boolean;
  tenderCode?: string;
}

export class Response extends InputResponseWithCount<Requisition> {}

export const requisitionGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const deptSet = new Set<string>();
    if ((req.departmentId ?? "").length === 0 && !(req.userLogin.isAdminRequisition || req.assigned || req.assignedBTB)) {
      await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);
    }

    const [rqs, count] = await o.findRequisition(ctx, {
      ...(deptSet.size > 0 ? { departmentIds: [...deptSet] } : { departmentId: req.departmentId }),
      sectionId: req.sectionId,
      year: req.year,
      title: req.title,
      page: req.page,
      size: req.size,
      status: req.status,
      useSelect: true,
      assigned: req.assigned,
      assignedBTB: req.assignedBTB,
      userId: req.userLogin.id,
      tenderCode: req.tenderCode,
    });

    const result = await Promise.all(
      rqs.map(async (r) => {
        //
        let canModify = false;

        try {
          validateActionByID(req.userLogin.id, [
            //
            r.creator!.id,
            r.requestFor!.id,
            ...r.requesterBackToBack.map((x) => x.id),
          ]);
          canModify = r.status === "DRAFT";
        } catch {}

        return {
          ...r,
          durationDays: (await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, r.approvalGroup, await o.dateNow(ctx), o.findCalendar)) ?? 0,
          canModify,
        };
      })
    );

    return {
      items: result,
      count: count,
    };
  },
};
