import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, getApproval, moveApproval } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, validateActionByID } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  pqId: string;
  userLogin: UserLogin;
  comment: string;
}

export class Response { }

export const pqRequirementSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);

    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== "REQUIREMENT") {
      throw new Error("pq must in REQUIREMENT PHASE");
    }

    if (pqTemplate.phasesRequirement?.status !== "DRAFT") {
      throw new Error("pq must in DRAFT state");
    }

    validateActionByID(req.userLogin.id, [
      pqTemplate.assignedUser?.id!,
      ...(pqTemplate.requestedBackToBacks?.map((x) => x.id) as string[]),
    ]);

    var approval = getApproval(pqTemplate.phasesRequirement.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = pqTemplate.phasesRequirement.approvalGroup ? pqTemplate.phasesRequirement.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    await moveApproval(ctx, paralelAND, pqTemplate.phasesRequirement, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    await o.savePQTemplate(ctx, pqTemplate);

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: "PQ_REQUIREMENT",
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Requirement Submitted`,
      user: req.userLogin,
      id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
    });

    // check if next approval having same user with current then approve
    if (pqTemplate.phasesRequirement.approvalGroup && pqTemplate.phasesRequirement.approvalGroup.nextApprovalGroupId
      && pqTemplate.phasesRequirement.approvalGroup.approvals[0].currentUserInPosition?.id === req.userLogin.id) {
      approval = getApproval(pqTemplate.phasesRequirement.approvalGroup!, req.userLogin);
      if (approval) {
        approval.date = getDateOnly(now);
        approval.signer = req.userLogin;
        approval.status = "DONE";
      }

      await moveApproval(ctx, paralelAND, pqTemplate.phasesRequirement, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

      await o.savePQTemplate(ctx, pqTemplate);

      await o.saveDocumentHistory(ctx, {
        documentId: pqTemplate.id!,
        documentType: "PQ_REQUIREMENT",
        comment: req.comment,
        date: getDateOnly(now),
        message: `PQ Requirement Approved`,
        user: req.userLogin,
        id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
      });
    }

    return {};
  },
};

// TODO::SUBMIT oleh BTB tidak terapprove dan di melanjutkan proses ke tahap approval selanjutnya
// TODO::SUBMIT oleh BTB status menjadi done tapi signer nya masih processing

