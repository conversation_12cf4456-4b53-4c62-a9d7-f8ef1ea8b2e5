import { Usecase } from "../../framework/core.js";
import { BaseFindManyFilter } from "../../framework/repository.js";
import { validateApprovalAction, validateEligibilityAPPAction } from "../model/model_approval.js";
import { ApprovalTemplateGroup, FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindDelegation } from "../model/model_delegation.js";
import { FindDepartment } from "../model/model_department.js";
import { FindProcPlanDetail, FindProcPlanHeader, ProcPlanHeader } from "../model/model_procplan.js";
import { Section } from "../model/model_section.js";
import { UserLogin } from "../model/model_user.js";
import { Currency, EligibilityAction, TenderMethod, TypeOf, getEmptyValueCurrency } from "../model/vo.js";

class Gateways {
  findProcPlanHeader: FindProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findDepartment: FindDepartment;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findDelegation: FindDelegation;
}

export class Request extends BaseFindManyFilter {
  userLogin: UserLogin;
  departmentId: string;
  year?: number;
}

export class Response {
  procPlanHeader: (ProcPlanHeader & { details: Detail[] }) | null;
  eligibility: EligibilityAction;
}

export const procplanAppHeaderGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // const [items, count] = await o.findManyReward(ctx, req);
    // return { items, count };

    const [procPlanHeaders] = await o.findProcPlanHeader(ctx, {
      departmentIds: [req.departmentId],
      year: req.year,
      procPlanType: "APP",
    });

    const procPlanHeader = procPlanHeaders.length > 0 ? procPlanHeaders[0] : null;

    const [procPlanDetails] = await o.findProcPlanDetail(ctx, {
      procPlanHeaderId: procPlanHeader?.id,
      page: req.page,
      size: req.size,
    });

    const procPlanDetail = procPlanDetails.length > 0 ? procPlanDetails[0] : null;

    const [ats] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });
    if (ats.length === 0) {
      throw new Error("approval template for APP not found");
    }

    const [delegations] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "DELEGATION" });
    const eligibility = validateEligibilityAPPAction(procPlanHeader?.status!, req.userLogin, procPlanHeader?.approvalGroup!, ats[0] as ApprovalTemplateGroup, delegations[0]);

    if (!procPlanHeader || !procPlanDetail) {
      //
      const [departments] = await o.findDepartment(ctx, { ids: [req.departmentId as string] });

      const department = departments.length > 0 ? departments[0] : null;
      if (department === null) {
        throw new Error("no department found");
      }

      return {
        procPlanHeader: {
          totalValueEstimation: getEmptyValueCurrency(),
          id: "",
          procPlanType: "APP",
          status: "NOT_CREATED",
          isSendBack: false,
          submittedDate: null,
          year: 0,
          count: 0,
          section: null,
          department: department,
          approvalGroup: null,
          submitter: null,
          details: [],
          sections: [],
        },
        eligibility,
      };
    }

    // if (procPlanDetail?.department?.id !== req.userLogin.department?.id) {
    //   //
    //   const [users] = await findUserSupervisors(ctx, procPlanDetail?.requestFor?.supervisorPositionId!);
    //   if (!users.some((user) => user.id === req.userLogin.id)) {
    //     return { procPlanHeader: null };
    //   }
    // }

    return {
      procPlanHeader: {
        ...procPlanHeader,
        details: procPlanDetails.map((x) => {
          //
          let canModify = false;
          try {
            validateApprovalAction(req.userLogin, procPlanHeader.approvalGroup, ats[0] as ApprovalTemplateGroup);
            canModify = procPlanHeader.status === "DRAFT";
          } catch { }

          return {
            id: x.id,
            title: x.title,
            tenderMethod: x.tenderMethod,
            contractDateStart: x.contractDateStart,
            contractDateEnd: x.contractDateEnd,
            poDateIssuance: x.poDateIssuance,
            poDateDelivery: x.poDateDelivery,
            currency: x.currency,
            valueEstimation: x.valueEstimation,
            localContentLevel: x.localContentLevel,
            section: x.section,
            canModify,
            masterList: x.masterList,
          } as Detail;
        }),
      },
      eligibility,
    };
  },
};

interface Detail {
  id?: string;
  title?: string;
  tenderMethod?: TypeOf<typeof TenderMethod>;
  contractDateStart?: Date | null;
  contractDateEnd?: Date | null;
  poDateIssuance?: Date | null;
  poDateDelivery?: Date | null;
  currency?: TypeOf<typeof Currency>;
  valueEstimation?: number;
  localContentLevel?: number;
  sequenceId?: string;
  section?: Section;
  canModify?: boolean;
  masterList?: boolean;
}
