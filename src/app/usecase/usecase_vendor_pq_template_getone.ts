import { Usecase } from "../../framework/core.js";
import { FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQTemplate, FindOnePQVendor, PrequalificationTemplate } from "../model/model_prequalification.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
  findOneVendorCIVD: FindOneVendorCIVD;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
}

export class Response {
  pqTemplate: PrequalificationTemplate | null;
}

export const vendorPQTemplateGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const vendor = await o.findOneVendorCIVD(ctx, req.vendorCIVDLogin.civdVendorId);

    if (!vendor) {
      throw new Error("Vendor does not have permission to access data");
    }
    
    const pq = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    return { pqTemplate: pq };
  },
};