import { Usecase } from "../../framework/core.js";
import { ApprovalTemplateGroup, FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindRequisition, Requisition, validateEligibilityRequisitionAction } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";
import { EligibilityActionWithCanAssign } from "../model/vo.js";

class Gateways {
  findRequisition: FindRequisition;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
}

export class Response {
  requisition: Requisition | null;
  eligibility: EligibilityActionWithCanAssign;
}

export const requisitionGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [rqs, count] = await o.findRequisition(ctx, {
      id: req.requisitionId,
    });

    if (count === 0) {
      return {
        requisition: null,
        eligibility: {
          canApproveOrSendBack: false,
          canCreate: false,
          canSubmit: false,
          canAssign: false,
        },
      };
    }

    const [ats] = await o.findApprovalTemplateGroup(ctx, { documentType: "REQUISITION" });
    if (ats.length === 0) {
      throw new Error("approval template for REQUISITION not found");
    }

    const userIDs: string[] = [
      //
      rqs[0].creator?.id as string,
      rqs[0].requestFor?.id as string,
      ...(rqs[0].requesterBackToBack.map((r) => r.id) as string[]),
    ];

    const eligibility = validateEligibilityRequisitionAction(rqs[0]?.status!, req.userLogin, userIDs, rqs[0].approvalGroup, ats[0] as ApprovalTemplateGroup);

    return {
      requisition: rqs[0],
      eligibility,
    };
  },
};
