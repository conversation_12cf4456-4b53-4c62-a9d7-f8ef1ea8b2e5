import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getApproval, SaveApprovalGroups } from "../model/model_approval.js";
import { FindOnePQVendor, RegistrationResultPayload, SavePQVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  findApprovalGroup: FindApprovalGroup;
  savePQVendor: SavePQVendor;
  saveApprovalGroups: SaveApprovalGroups;
}

export class Request {
  vendorPqId: string;
  registrationResult: RegistrationResultPayload;
}

export class Response {}

export const pqRegistrationUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { vendorPqId: req.vendorPqId });

    if (!pqVendor) {
      throw new Error(`PQ Vendor with id ${req.vendorPqId} is not found`);
    }

    if (resultSummary(req.registrationResult) === "UNDECIDED") {
      throw new Error("Response must be PASS or FAIL");
    }

    pqVendor.phaseRegistration!.suratMinat.result = req.registrationResult.suratMinat.result;
    pqVendor.phaseRegistration!.suratMinat.remarks = req.registrationResult.suratMinat.remarks;

    if (pqVendor.phaseRegistration!.suratKuasaMinat) pqVendor.phaseRegistration!.suratKuasaMinat.result = "PASS";

    pqVendor.phaseRegistration!.suratSPDA.result = req.registrationResult.suratSPDA.result;
    pqVendor.phaseRegistration!.suratSPDA.remarks = req.registrationResult.suratSPDA.remarks;

    if (req.registrationResult.dokumenDomisili) {
      pqVendor.phaseRegistration!.dokumenDomisili!.result = req.registrationResult.dokumenDomisili.result;
      pqVendor.phaseRegistration!.dokumenDomisili!.remarks = req.registrationResult.dokumenDomisili.remarks;
    }

    pqVendor.phaseRegistration!.suratIzinUsaha!.result = req.registrationResult.suratIzinUsaha.result;
    pqVendor.phaseRegistration!.suratIzinUsaha!.remarks = req.registrationResult.suratIzinUsaha.remarks;

    if (req.registrationResult.sertifikatTKDN) {
      pqVendor.phaseRegistration!.sertifikatTKDN!.result = req.registrationResult.sertifikatTKDN.result;
      pqVendor.phaseRegistration!.sertifikatTKDN!.remarks = req.registrationResult.sertifikatTKDN.remarks;
    }

    // status on review update
    pqVendor.status = "REGISTRATION_EVALUATED";
    pqVendor.phaseRegistration!.resultSummary = resultSummary(req.registrationResult); // "PASS" | "FAIL";

    await o.savePQVendor(ctx, pqVendor);

    // start approval
    const [approval] = await o.findApprovalGroup(ctx, { documentId: pqVendor.id, documentType: "PQ_REGISTRATION" });
    if (approval[0] && approval[0].approvals) {
      approval[0].status = "PROCESSING";
      approval[0].approvals[0].status = "PROCESSING";
    }

    await o.saveApprovalGroups(ctx, approval);

    return {};
  },
};

const resultSummary = (resultPayload: RegistrationResultPayload): "PASS" | "FAIL" | "UNDECIDED" => {
  for (const key in resultPayload) {
    if (resultPayload.hasOwnProperty(key)) {
      const item = resultPayload[key as keyof RegistrationResultPayload];
      if (item) {
        if (item.result !== "FAIL" && item.result !== "PASS") {
          return "UNDECIDED";
        }

        if (item.result === "FAIL") {
          return "FAIL";
        }
      }
    }
  }
  return "PASS";
};
