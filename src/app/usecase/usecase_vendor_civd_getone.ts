import { Usecase } from "../../framework/core.js";
import { C<PERSON>VDVendor, FindOneVendorCIVD } from "../model/model_civd_vendor.js";
import { BusinessClass } from "../model/vo.js";
import { translateBusinessClassToEn } from "../utility/helper.js";

class Gateways {
  findOneVendorCIVD: FindOneVendorCIVD;
}

export class Request {
  civdVendorId: number;
}

export class Response {
  vendorCivd: Partial<CIVDVendor>;
}

export const vendorCivdGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const vendorCivd = await o.findOneVendorCIVD(ctx, req.civdVendorId);

    if (vendorCivd === null) {
      throw new Error("vendor civd not found");
    }

    if (vendorCivd.businessClass) {
      let businessClass = vendorCivd.businessClass.sort((a, b) => b.year - a.year)[0];
      businessClass.class = translateBusinessClassToEn(businessClass.class!) as any;
      vendorCivd.businessClass = [
        businessClass
      ];
    }

    return { vendorCivd };
  },
};
