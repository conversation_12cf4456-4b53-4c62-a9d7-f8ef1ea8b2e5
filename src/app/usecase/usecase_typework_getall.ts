import { Usecase } from "../../framework/core.js";
import { BaseFindManyFilter, InputResponseWithCount } from "../../framework/repository.js";
import { FindTypeWork, TypeWork } from "../model/model_typework.js";

class Gateways {
  findTypeWork: FindTypeWork;
}

export class Request extends BaseFindManyFilter {}

export class Response extends InputResponseWithCount<TypeWork> {}

export const typeworkGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findTypeWork(ctx, req);
    return { items, count };
  },
};
