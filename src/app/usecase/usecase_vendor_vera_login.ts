import { Usecase } from "../../framework/core.js";
import { FindOneVendor, Vendor } from "../model/model_vendor.js";
import jwt from "jsonwebtoken";
import { VendorVera, veraLogin } from "../utility/vera_vendor.js";
import { C<PERSON>VDVendor, FindOneVendorCIVD, FindVendorCIVD } from "../model/model_civd_vendor.js";

class Gateways {
  findOneVendor: FindOneVendor;
  findVendorCIVD: FindVendorCIVD;
}

export class Request {
  email: string;
  password: string;
}

export class Response {
  vendor: Partial<CIVDVendor>;
}

export const loginVendorVera: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    if (!req.email) {
      throw new Error("missing email");
    }

    if (!req.password) {
      throw new Error("missing password");
    }

    let vendor: CIVDVendor | null;

    // TODO: bila npwp dari vera tidak ditemukan di civd?
    if (process.env.APPLICATION_MODE === "development") {
      const [vendors] = await o.findVendorCIVD(ctx, { email: req.email });
      if (vendors.length === 0) {
        throw new Error("Vendor not found");
      }
      vendor = vendors[0];
    } else {
      const vendorVera = await veraLogin({
        // email: "<EMAIL>", // dummy
        // password: "J@karta123",
        email: req.email,
        password: req.password,
      });

      const [vendors] = await o.findVendorCIVD(ctx, { npwp: vendorVera.npwp });
      if (vendors.length === 0) {
        throw new Error("Vendor not found");
      }

      vendor = vendors[0];

      // validate accountAccess
      if (!vendorVera.accountAccess || !vendorVera.accountAccess.includes("procurement") || !vendorVera.accountAccess.includes("prisa")) {
        // throw new Error("Vendor does not have access to prisa");
      }
    }

    const result = {
      civdVendorId: vendor.civdVendorId,
      name: vendor.name,
      npwp: vendor.npwp,
      email: vendor.email,
      companyType: vendor.companyType,
    }

    return { vendor: result };
  },
};
