import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, SaveApprovalGroups, getApprovalList } from "../model/model_approval.js";
import { ApprovalTemplate, FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindDelegation } from "../model/model_delegation.js";
import { FindDepartment } from "../model/model_department.js";
import {
  FindProcPlanHeader,
  ProcPlanDetail,
  ProcplanDetailPayload,
  SaveProcPlanDetail,
  SaveProcPlanHeader,
  createNewProcPlanHeader,
  getLastProcplanHeader,
  getUPPErrorFields,
} from "../model/model_procplan.js";
import { FindSection } from "../model/model_section.js";
import { FindUser, FindUserSupervisorsRoleDepartment, UserLogin } from "../model/model_user.js";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>StringHandler } from "../model/vo.js";
import { getDateOnly, toUSD } from "../utility/helper.js";

class Gateways {
  saveProcPlanHeader: SaveProcPlanHeader;
  saveProcPlanDetail: SaveProcPlanDetail;
  saveApprovalGroups: SaveApprovalGroups;
  deleteApprovalGroups: DeleteApprovalGroups;
  findUser: FindUser;
  findDelegation: FindDelegation;
  findDepartment: FindDepartment;
  findSection: FindSection;
  findProcPlanHeader: FindProcPlanHeader;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findUserSupervisorsRoleDepartment: FindUserSupervisorsRoleDepartment;
  dateNow: DateNowHandler;
  randomString: RandomStringHandler;
}

export class Request {
  userLogin: UserLogin;
  // now: DateOrString;
  // newAPPHeaderId: string;
  // newAPPDetailId: string;
  procPlanDetail: ProcplanDetailPayload & {
    year: number;
    departmentId: string;
    sectionId: string;
    requestForId: string;
    requesterBackToBackIds: string[];
  };
}

export class Response {
  procPlanHeaderId: string;
  procPlanDetailId: string;
}

export const procplanAppDetailCreate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);
    const newAPPHeaderId = await o.randomString(ctx);
    const newAPPDetailId = await o.randomString(ctx);

    // const year = getYear(req.procPlanDetail.year, now);
    const year = req.procPlanDetail.year;

    let department;

    const [sections] = await o.findSection(ctx, { ids: [req.procPlanDetail.sectionId] });
    const section = sections.length > 0 ? sections[0] : null;
    if (!section) {
      const [departments] = await o.findDepartment(ctx, { ids: [req.procPlanDetail.departmentId] });
      department = departments.length > 0 ? departments[0] : null;
    } else {
      department = section.department;
    }

    let pph = await getLastProcplanHeader(
      //
      ctx,
      o.findProcPlanHeader,
      department?.id!,
      null,
      req.procPlanDetail.year,
      "APP"
    );

    let isFirstTimePPH = false;

    if (!pph) {
      //
      isFirstTimePPH = true;

      // buat ProcplanHeader baru
      pph = createNewProcPlanHeader(
        //
        now,
        year,
        section?.id ?? null,
        department?.id!,
        newAPPHeaderId,
        "APP"
      );

      // simpan pph
      await o.saveProcPlanHeader(ctx, pph);
    }

    if (section && !pph?.sections.some((sc) => sc.id === section.id)) {
      pph?.sections.push(section);
    }

    if (pph!.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    const [ats] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });
    if (ats.length === 0) {
      throw new Error("approval template for APP not found");
    }

    if (!ats[0].approvalTemplates.some((at) => at.users?.some((u) => u.id === req.userLogin.id) || at.role === req.userLogin.position?.role)) {
      throw new Error("you are not allowed to create APP");
    }

    const errorFields = getUPPErrorFields(req.procPlanDetail);
    // validateProcPlanRequest(req.procPlanDetail);

    if (pph.year !== Number(req.procPlanDetail.year)) {
      throw new Error("Invalid year for this procplan");
    }

    const newPPD: ProcPlanDetail = {
      ...req.procPlanDetail,
      id: `APP-${newAPPDetailId}`,
      year: req.procPlanDetail.year,
      procPlanHeader: pph,
      procPlanCode: "", // tidak didefine disini
      section,
      department: department!,
      procPlanType: "APP",
      createdAt: getDateOnly(now),
      creator: req.userLogin,
      requestFor: null,
      requesterBackToBack: [],
      usedByRequisitionIds: [],
      valueInUSD: toUSD(req.procPlanDetail.currency, req.procPlanDetail.valueEstimation),
      errorFields: errorFields,
    };

    await o.saveProcPlanDetail(ctx, [newPPD]);

    // increment count
    pph.count! += 1;

    // add totalValueEstimation
    // pph.totalValueEstimation += Number(newPPD.valueEstimation);

    pph.totalValueEstimation.find((v) => v.currency === newPPD.currency)!.value += Number(newPPD.valueEstimation);

    if (isFirstTimePPH) {
      //

      // TODO potential issue. require to check since we no longer depend on role anymore
      const firstStructural = ats.find((x) => x.approvalTemplates.find((y) => y.role)) as ApprovalTemplate;
      if (!firstStructural) {
        throw new Error("not found any structural user");
      }

      const [listUser] = await o.findUserSupervisorsRoleDepartment(ctx, { role: firstStructural.role!, departmentId: department?.id! });

      const description = `${department?.name}-${pph?.count!}Qty`;

      // buat approval
      const approvals = await getApprovalList(
        ctx,
        description,
        o.findUser,
        o.findApprovalTemplateGroup,
        o.findDelegation,
        listUser,
        pph.id,
        "PROC_PLAN_APP",
        pph.year
      );

      await o.deleteApprovalGroups(ctx, { documentId: pph.id });

      // simpan approval
      await o.saveApprovalGroups(ctx, approvals);

      // pointer nunjuk ke approval pertama
      pph.approvalGroup = approvals[0];
    }

    // update ProcplanHeader
    await o.saveProcPlanHeader(ctx, pph);

    return {
      procPlanHeaderId: pph.id,
      procPlanDetailId: newPPD.id,
    };
  },
};
