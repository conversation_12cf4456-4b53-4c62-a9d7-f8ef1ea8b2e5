import { Usecase } from "../../framework/core.js";
import { FindApprovalGroupNative } from "../model/model_approval.js";
import { UserLogin } from "../model/model_user.js";
import { ApprovalStatus, TypeOf } from "../model/vo.js";

class Gateways {
  findApprovalGroupNative: FindApprovalGroupNative;
}

export class Request {
  userLogin: UserLogin;
}

export class Response { }

export const dashboardApproval: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const status: TypeOf<typeof ApprovalStatus> = "PROCESSING";
    const [items] = await o.findApprovalGroupNative(ctx, { status, userId: req.userLogin.id, positionId: req.userLogin.position?.id });

    const filteredItems = items.filter((item) => item.sequence! > 1 || item.documentType === "PQ_REGISTRATION");

    const [delegations] = await o.findApprovalGroupNative(ctx, { status, userId: req.userLogin.id, positionId: req.userLogin.position?.id, delegationUserApproval: true }); 

    const filteredDelegations = delegations.filter((delegation) => delegation.sequence! > 1 || delegation.documentType === "PQ_REGISTRATION");

    const result = {
      approval: filteredItems.reduce((acc, item) => {
        if (!item.documentType) return acc;
        acc[item.documentType] = (acc[item.documentType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      delegation: filteredDelegations.reduce((acc, item) => {
        if (!item.documentType) return acc;
        acc[item.documentType] = (acc[item.documentType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    }

    return result;
  },
};
