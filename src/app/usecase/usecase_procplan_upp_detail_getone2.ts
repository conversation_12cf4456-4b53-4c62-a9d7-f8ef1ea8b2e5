import { Usecase } from "../../framework/core.js";
import { FindProcPlanDetail, ProcPlanDetail } from "../model/model_procplan.js";
import { FindUserSupervisors, UserLogin } from "../model/model_user.js";

class Gateways {
  findProcPlanDetail: FindProcPlanDetail;
  findUserSupervisors: FindUserSupervisors;
}

export class Request {
  userLogin: UserLogin;
  detailId: string;
}

export class Response {
  procPlanDetail: ProcPlanDetail | null;
}

export const procplanUppDetailGetOne2: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // const [items, count] = await o.findManyReward(ctx, req);
    // return { items, count };

    // TODO validate that other user that can see this page such as
    // non executive users that still in the same department
    // executive users who is involved in the approval process

    // TODO test the executive user who is not involve in the approval process
    // TODO test the non executive user who is not in the same department
    // TODO try to test the potential issue. how about non structural user who is mention in approval list ?
    // so far there is no non structural user (cases) defined in UPP

    const [procPlanDetails] = await o.findProcPlanDetail(ctx, { id: req.detailId, procPlanType: "UPP" });

    const procPlanDetail = procPlanDetails.length > 0 ? procPlanDetails[0] : null;
    if (!procPlanDetail) {
      return { procPlanDetail: null };
    }

    // const departmentId = procPlanDetail?.procPlanHeader?.department?.id;
    // if (departmentId !== req.userLogin.department?.id) {
    //   //
    //   const [users] = await o.findUserSupervisors(ctx, { positionId: procPlanDetail?.requestFor?.supervisorPositionId! });
    //   if (!users.some((user) => user.id === req.userLogin.id)) {
    //     return { procPlanDetail: null };
    //   }
    // }

    return { procPlanDetail };
  },
};
