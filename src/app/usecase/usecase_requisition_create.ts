import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { Requisition, RequisitionPayload, SaveRequisition } from "../model/model_requisition.js";
import { FindUser, FindUserSupervisors, User, UserLogin } from "../model/model_user.js";
import { DateNowHandler, RandomStringHandler } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findUser: FindUser;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  saveRequisition: SaveRequisition;
  saveApprovalGroups: SaveApprovalGroups;
  deleteApprovalGroups: DeleteApprovalGroups;
  dateNow: DateNowHandler;
  randomString: RandomStringHandler;
}

export class Request {
  userLogin: UserLogin;
  // now: DateOrString;
  // newRequisitionId: string;
  requisition: RequisitionPayload & {
    requestForId: string;
    requesterBackToBackIds: string[];
    membersInvolvedIds: string[];
    departmentId: string;
    sectionId: string;
  };
}

export class Response {
  id: string;
}

export const requisitionCreate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);
    const newRequisitionId = await o.randomString(ctx); // penambahan timestamp agar unique?

    const [ats] = await o.findApprovalTemplateGroup(ctx, {
      //
      documentType: "REQUISITION",
    });
    if (ats.length === 0) {
      throw new Error("approval template for REQUISITION not found");
    }

    // if (!ats[0].approvalTemplates.some((at) => at.users?.some((u) => u.id === req.userLogin.id) || at.role === req.userLogin.position?.role)) {
    //   throw new Error("you are not allowed to create Requisition");
    // }

    const [requestForUsers] = await o.findUser(ctx, {
      ids: [req.requisition.requestForId],
      // departmentId: req.userLogin.department?.id,
      size: 1,
    });
    const requestForUser = requestForUsers.length > 0 ? requestForUsers[0] : null;
    if (!requestForUser) {
      throw new Error(`request for id ${req.requisition.requestForId} not found`);
    }

    let rbtbs: User[] = [];
    if (req.requisition.requesterBackToBackIds.length > 0) {
      [rbtbs] = await o.findUser(ctx, {
        ids: req.requisition.requesterBackToBackIds.filter((x, i, a) => a.indexOf(x) == i), // find unique only
      });
      if (rbtbs.length === 0) {
        throw new Error(`requester back to back for ids ${req.requisition.requesterBackToBackIds} not found`);
      }
    }

    let memberInvolveds: User[] = [];
    if (req.requisition.membersInvolvedIds.length > 0) {
      [memberInvolveds] = await o.findUser(ctx, {
        ids: req.requisition.membersInvolvedIds.filter((x, i, a) => a.indexOf(x) == i), // find unique only
      });
      if (memberInvolveds.length === 0) {
        throw new Error(`member involved for ids ${req.requisition.membersInvolvedIds} not found`);
      }
    }

    let rqId = `RQS-${newRequisitionId}`;

    const obj: Requisition = {
      ...req.requisition,
      id: rqId,
      submittedDate: getDateOnly(now),
      creator: req.userLogin,
      submitter: null,
      isSendBack: false,
      department: { id: req.requisition.departmentId! },
      section: { id: req.requisition.sectionId! },
      requestFor: requestForUser,
      requesterBackToBack: rbtbs,
      membersInvolved: memberInvolveds,
      status: "DRAFT",
      year: now.getFullYear(),
      title: "",
      generalScopeOfWork: "",
      justificationOfRequiredWorks: "",
      localContentLevel: 0,
      commodity: "GOODS",
      tenderMethod: "SELF_MANAGE",
      incoterms: "NR",
      importation: false,
      currency: "IDR",
    };

    await o.saveRequisition(ctx, obj);

    return { id: rqId };
  },
};
