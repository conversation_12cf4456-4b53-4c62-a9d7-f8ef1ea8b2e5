import { Usecase } from "../../framework/core.js";
import { saveLogger } from "../model/model_log.js";
import { FindPQTemplate, PrequalificationTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { AdminUpdateRequisitionPayload, FindRequisition, Requisition, SaveRequisition, validateRequisitionRequest } from "../model/model_requisition.js";
import { FindUser, User, UserLogin } from "../model/model_user.js";
import { isUserAdmin, toUSD } from "../utility/helper.js";

class Gateways {
  findRequisition: FindRequisition;
  findPQTemplate: FindPQTemplate;
  findUser: FindUser;
  saveRequisition: SaveRequisition;
  savePQTemplate: SavePQTemplate;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
  requisition: AdminUpdateRequisitionPayload;
}

export class Response { }

export const requisitionAdminUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // Check if user is admin
    if (!req.userLogin.email || !isUserAdmin(req.userLogin.email)) {
      throw new Error("Only admin users can perform this operation");
    }

    const [rqs] = await o.findRequisition(ctx, { id: req.requisitionId });
    const rq = rqs.length > 0 ? rqs[0] : null;

    if (!rq) {
      throw new Error("requisition not found");
    }

    if (!["ON_REVIEW", "APPROVED"].includes(rq.status)) {
      throw new Error("requisition must in ON_REVIEW or APPROVED state");
    }

    if (req.requisition.checkValidation) {
      validateRequisitionRequest(req.requisition);
    }

    const updatedRq: Requisition = {
      ...rq,
      ...(req.requisition as unknown as Requisition),
    };

    await o.saveRequisition(ctx, updatedRq);

    // update PQ if exist
    const [pqs] = await o.findPQTemplate(ctx, { tenderCode: rq.tenderCode! });
    if (rq.status === "APPROVED" && pqs.length > 0) {
      const pq = pqs[0];

      if (pq) {
        
        // prevent update PQ when phases is requirement is not draft
        if (pq.phasesRegistration && pq.phasesRegistration.status !== "DRAFT") {
          return {};
        }

        const oeUSD = toUSD(updatedRq.currency!, updatedRq.value!);
        const businessClass = oeUSD >= 50_000_000 ? "LARGE" : oeUSD >= 15_000_000 ? "MEDIUM" : "SMALL";
  
        const updatedPQ: PrequalificationTemplate = {
          id: pq.id,
          sourceRequisitionId: updatedRq.id,
          assignmentDate: pq.assignmentDate,
          tenderMethod: updatedRq.tenderMethod,
          contractDateStart: updatedRq.contractDateStart,
          contractDateEnd: updatedRq.contractDateEnd,
          poDateDelivery: updatedRq.poDateDelivery,
          poDateIssuance: updatedRq.poDateIssuance,
          // businessType: "",
          basicCapabilityCalculationForm: pq.basicCapabilityCalculationForm,
          businessClass: businessClass,
          commodity: updatedRq.commodity,
          // businessFields: [],
          // businessLicense: "",
          // companyStatus: "ConsortiumOfLocalAndNationalCompanies",
          // domicile: "",
          financialDueDiligenceForm: pq.financialDueDiligenceForm,
          generalProvisionsRequirementsForm: pq.generalProvisionsRequirementsForm,
          highRiskCategory: updatedRq.hseAssessment,
          localCompanyStatementLetters: pq.localCompanyStatementLetters,
          pqAnnouncementDoc: pq.pqAnnouncementDoc,
          pqMeetingDate: pq.pqMeetingDate,
          pqRegistrationDate: pq.pqRegistrationDate,
          pqStatementLetters: pq.pqStatementLetters,
          pqSubmissionDate: pq.pqSubmissionDate,
          prequalificationType: pq.prequalificationType,
          workOfLocation: updatedRq.workOfLocation,
          // projectLocation: [],
          vhseMSQuisionerForm: pq.vhseMSQuisionerForm,
          assignedUser: pq.assignedUser,
          requestedBackToBacks: pq.requestedBackToBacks,
          additionalPQRequirements: updatedRq.supportingDocuments?.aprFiles, // from requisition "aprFiles"
          announcementDate: pq.announcementDate,
          currency: updatedRq.currency,
          ownerEstimateValue: updatedRq.value,
          title: updatedRq.title,
          generalScopeOfWorks: updatedRq.generalScopeOfWork,
          localContentLevel: updatedRq.localContentLevel,
  
          basicCapability: pq.basicCapability,
          // highestExperienceScore: "",
  
          financialDueDiligence: pq.financialDueDiligence,
          invitedVendors: pq.invitedVendors,
          pqMeeting: pq.pqMeeting,
          otherInformations: pq.otherInformations,
          tenderCode: updatedRq.tenderCode!,
          currentPhase: pq.currentPhase,
          phasesRequirement: pq.phasesRequirement,
        }

        await o.savePQTemplate(ctx, {
          ...pq,
          ...updatedPQ
        });

      }
    }
    
    await saveLogger("requisitionAdminUpdate", {
      id: updatedRq.id,
      email: req.userLogin.email,
      message: `Requisition ${updatedRq.id} Updated`,
    }, "SUCCESS");
    
    return {};
  },
};
