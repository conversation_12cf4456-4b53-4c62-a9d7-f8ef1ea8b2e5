import { Context, Usecase } from "../../framework/core.js";
import { FindApprovalGroup, getLastApprovalGroupDurationDays } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindCalendar } from "../model/model_calendar.js";
import { collectDepartments, Department, FindDepartment } from "../model/model_department.js";
import { FindProcPlanHeader, ProcPlanHeader } from "../model/model_procplan.js";
import { FindSection, Section } from "../model/model_section.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { DateNowHandler, getEmptyValueCurrency } from "../model/vo.js";

class Gateways {
  findDepartment: FindDepartment;
  findSection: FindSection;
  findProcPlanHeader: FindProcPlanHeader;
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findApprovalGroup: FindApprovalGroup;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
}

export class Response { }

export const dashboardProcplan: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const deptSet = new Set<string>();
    await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);

    const [atgs] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });

    const firstApprovalTemplateGroup = atgs[0].approvalTemplates.find((at) => at.users?.find((x) => x.id === req.userLogin.id));

    return {
      procplan: {
        upp: await uppHandler(ctx, o, deptSet, now),
        app: await appHandler(ctx, o, deptSet, now, !!firstApprovalTemplateGroup),
      },
    };
  },
};

const uppHandler = async (ctx: Context<Record<string, any>>, o: Gateways, deptSet: Set<string>, now: Date) => {
  const results: ProcPlanHeader[] = [];

  const [sections] = deptSet.size > 0 ? await o.findSection(ctx, { departmentIds: [...deptSet] }) : [[]];

  const [pphsUPP] = await o.findProcPlanHeader(ctx, {
    year: new Date(now).getFullYear(),
    sectionIds: sections.map((d) => d.id),
    procPlanType: "UPP",
    useSelect: true,
  });

  for (const sc of sections) {
    //
    const pph = pphsUPP.find((d) => d.section?.id === sc.id);
    if (!pph) {
      results.push(emptyProcplanHeaderUPP(sc, sc.department!));
    } else {
      pph.durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, pph.approvalGroup, now, o.findCalendar);
      results.push(pph);
    }
  }

  return results.reduce((prev: { [key: string]: number }, curr) => ({ ...prev, [curr.status]: (prev[curr.status] || 0) + 1 }), {});
};

const appHandler = async (ctx: Context<Record<string, any>>, o: Gateways, deptSet: Set<string>, now: Date, isFirstApprovalTemplateGroup: boolean) => {
  const results: ProcPlanHeader[] = [];

  if (!!isFirstApprovalTemplateGroup) {
    const [departments] = await o.findDepartment(ctx, {});

    const [pphs] = await o.findProcPlanHeader(ctx, {
      year: new Date(now).getFullYear(),
      departmentIds: departments.map((d) => d.id),
      procPlanType: "APP",
      useSelect: true,
    });

    for (const department of departments) {
      const pph = pphs.find((d) => d.department?.id === department.id);
      if (!pph) {
        results.push(emptyProcplanHeaderAPP(department));
      } else {
        pph.durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, pph.approvalGroup, now);
        results.push(pph);
      }
    }
  } else {
    const [departments] = deptSet.size > 0 ? await o.findDepartment(ctx, { ids: [...deptSet] }) : [[]];

    const [pphs] = await o.findProcPlanHeader(ctx, {
      year: new Date(now).getFullYear(),
      departmentIds: departments.map((d) => d.id),
      procPlanType: "APP",
      useSelect: true,
    });

    for (const department of departments) {
      //
      const pph = pphs.find((d) => d.department?.id === department.id);
      if (!pph) {
        results.push(emptyProcplanHeaderAPP(department));
      } else {
        pph.durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, pph.approvalGroup, now);
        results.push(pph);
      }
      //
    }
  }

  return results.reduce((prev: { [key: string]: number }, curr) => ({ ...prev, [curr.status]: (prev[curr.status] || 0) + 1 }), {});
};

const emptyProcplanHeaderUPP = (section: Section, department: Department): ProcPlanHeader => {
  return {
    id: "",
    procPlanType: "UPP",
    status: "NOT_CREATED",
    isSendBack: false,
    submittedDate: null,
    year: 0,
    count: 0,
    section,
    department,
    approvalGroup: null,
    totalValueEstimation: getEmptyValueCurrency(),
    submitter: null,
    sections: [],
  };
};

const emptyProcplanHeaderAPP = (department: Department): ProcPlanHeader => {
  return {
    id: "",
    procPlanType: "APP",
    status: "NOT_CREATED",
    isSendBack: false,
    submittedDate: null,
    year: 0,
    count: 0,
    section: null,
    department: department,
    approvalGroup: null,
    totalValueEstimation: getEmptyValueCurrency(),
    submitter: null,
    sections: [],
  };
};
