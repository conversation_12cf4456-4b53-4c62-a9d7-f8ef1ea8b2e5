import { Usecase } from "../../framework/core.js";
import { collectDepartments } from "../model/model_department.js";
import { FindRequisition } from "../model/model_requisition.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { DateNowHandler } from "../model/vo.js";

class Gateways {
  findRequisition: FindRequisition;
  findUser: FindUser;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
}

export class Response { }

export const dashboardRequisition: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const deptSet = new Set<string>();
    await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);

    const [results] = await o.findRequisition(ctx, {
      departmentIds: [...deptSet],
      year: new Date(now).getFullYear(),
      useSelect: true,
    });

    return {
      requisition: results.reduce((prev: { [key: string]: number }, curr) => ({ ...prev, [curr.status]: (prev[curr.status] || 0) + 1 }), {}),
    };
  },
};
