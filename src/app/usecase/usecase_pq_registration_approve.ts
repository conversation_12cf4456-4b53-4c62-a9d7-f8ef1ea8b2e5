import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, FindOnePQVendor, SavePQTemplate, SavePQVendor } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
  savePQTemplate: SavePQTemplate;
  savePQVendor: SavePQVendor;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  vendorPqId: string;
  comment: string;
}

export class Response {}

export const pqRegistrationApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { vendorPqId: req.vendorPqId });
    if (!pqVendor) {
      throw new Error("pq vendor not found");
    }

    const pqTemplate = pqVendor.prequalificationTemplate;
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== "REGISTRATION") {
      throw new Error("pq must in REGISTRATION PHASE");
    }

    if (pqVendor.status !== "REGISTRATION_EVALUATED") {
      throw new Error("pq vendor must in REGISTRATION_EVALUATED state");
    }

    const [approval] = await o.findApprovalGroup(ctx, { documentId: pqVendor.id, documentType: "PQ_REGISTRATION" });

    validateApprovalAction(req.userLogin, approval[0]);

    pqVendor.status = "REGISTRATION_APPROVED";
    pqVendor.phaseRegistration!.evaluationDate = getDateOnly(now); // add evaluation date

    if (pqVendor.phaseRegistration!.resultSummary !== "PASS") {
      pqVendor.phaseRegistration!.submitDate = null; // reset registration date
    }

    await o.savePQVendor(ctx, pqVendor);

    if (approval.length > 0) {
      approval[0].approvals[0].date = getDateOnly(now);
      approval[0].approvals[0].signer = req.userLogin;
      approval[0].approvals[0].status = "DONE";
    }

    await o.saveApprovalGroups(ctx, approval);

    // const paralelAND = pqTemplate.phasesRegistration.approvalGroup ? pqTemplate.phasesRegistration.approvalGroup.approvals.every((x) => x.status === "DONE") : false;
    // await moveApproval(ctx, paralelAND, pqTemplate.phasesRegistration, o.findApprovalGroup, o.saveApprovalGroups);
    // await o.savePQTemplate(ctx, pqTemplate);

    await o.saveDocumentHistory(ctx, {
      documentId: pqVendor.id,
      documentType: "PQ_REGISTRATION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Registration Approve`,
      user: req.userLogin,
      id: `${pqVendor.id.slice(4)}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};
