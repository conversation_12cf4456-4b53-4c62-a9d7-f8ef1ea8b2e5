import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups } from "../model/model_approval.js";
import { FindRequisition, SaveRequisition } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, TypeOf } from "../model/vo.js";
import { isUserAdmin } from "../utility/helper.js";

class Gateways {
  findApprovalGroup: FindApprovalGroup;
  findRequisition: FindRequisition;
  dateNow: DateNowHandler;
  saveApprovalGroups: SaveApprovalGroups;
  saveRequisition: SaveRequisition;
}

export class Request {
  userLogin: UserLogin;
  documentId: string;
  documentType: TypeOf<typeof DocumentTemplate>;
}

export class Response { }

export const approvalAdminRevert: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // Check if user is admin
    if (!req.userLogin.email || !isUserAdmin(req.userLogin.email)) {
      throw new Error("Only admin users can perform this operation");
    }

    let document = null;

    if (req.documentType === "REQUISITION") {
      const [rqs] = await o.findRequisition(ctx, { id: req.documentId });
      const rq = rqs.length > 0 ? rqs[0] : null;

      if (!rq) {
        throw new Error("requisition not found");
      }

      if (rq.status !== "ON_REVIEW") {
        throw new Error("requisition must in ON_REVIEW state");
      }

      document = rq;

    } else {
      throw new Error("documentType not supported");
    }

    const [approvalGroups] = await o.findApprovalGroup(ctx, { ...req });

    if (approvalGroups.length === 0) {
      throw new Error("Approval group not found");
    }

    // current approval
    const currentApprovalGroups = approvalGroups.find((ag) => ag.status === "PROCESSING");

    if (!currentApprovalGroups) {
      throw new Error("No processing approval group found");
    }

    if (currentApprovalGroups.sequence! <= 2) {
      throw new Error("Cannot revert approval group");
    }

    currentApprovalGroups.status = "NOT_STARTED";

    // revert to previous approval
    const revertedToApprovalGroups = approvalGroups.find((ag) => ag.sequence === currentApprovalGroups.sequence! - 1);

    if (!revertedToApprovalGroups) {
      throw new Error("No reverted approval group found");
    }

    // change status into PROCESSING
    revertedToApprovalGroups.status = "PROCESSING";

    // nullify approvals.signer, approvals.date, approvals.status = "PROCESSING"
    if (revertedToApprovalGroups.approvals.length > 0) {
      // type users & role
      for (const approval of revertedToApprovalGroups.approvals) {
        approval.signer = null;
        approval.date = null;
        approval.status = "PROCESSING";
      }
    }

    // Save both approval groups
    const approvalGroupsToSave = [currentApprovalGroups, revertedToApprovalGroups];
    await o.saveApprovalGroups(ctx, approvalGroupsToSave);

    // update document approvalGroupId
    document.approvalGroup = revertedToApprovalGroups;
    await o.saveRequisition(ctx, document);

    return {
      sequence: revertedToApprovalGroups.sequence, // current sequence
    };
  },
};
