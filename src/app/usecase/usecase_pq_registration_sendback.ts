import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, FindOnePQVendor, SavePQTemplate, SavePQVendor } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  findApprovalGroup: FindApprovalGroup;
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  vendorPqId: string;
  comment: string;
}

export class Response {}

export const pqRegistrationSendback: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqVendor = await o.findOnePQVendor(ctx, { vendorPqId: req.vendorPqId });
    if (!pqVendor) {
      throw new Error("pq vendor not found");
    }

    if (pqVendor.status !== "REGISTRATION_EVALUATED") {
      throw new Error("pq vendor must in REGISTRATION_EVALUATED state");
    }

    const pqTemplate = pqVendor.prequalificationTemplate;
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== "REGISTRATION") {
      throw new Error("pq must in REGISTRATION PHASE");
    }

    // if (pqTemplate.phasesRegistration?.status !== "ON_REVIEW") {
    //   throw new Error("pq must in ON_REVIEW state");
    // }

    const [approval] = await o.findApprovalGroup(ctx, { documentId: pqVendor.id, documentType: "PQ_REGISTRATION" });

    validateApprovalAction(req.userLogin, approval[0]);

    if (approval[0] && approval[0].approvals) {
      approval[0].status = "NOT_STARTED";
      approval[0].approvals[0].status = "NOT_STARTED";
    }

    await o.saveApprovalGroups(ctx, approval);

    pqVendor.status = "REGISTRATION_SUBMITTED";

    await o.savePQVendor(ctx, pqVendor);

    if (req.comment === "") {
      throw new Error("comment is required");
    }

    await o.saveDocumentHistory(ctx, {
      documentId: pqVendor.id,
      documentType: "PQ_REGISTRATION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Registration Sent Back`,
      user: req.userLogin,
      id: `${pqVendor.id.slice(4)}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};
