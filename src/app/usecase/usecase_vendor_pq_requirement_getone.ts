import { Usecase } from "../../framework/core.js";
import { C<PERSON>VDVendor, FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQVendor, PrequalificationVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  findOneVendorCIVD: FindOneVendorCIVD;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
}

export class Response {
  prequalificationVendor: PrequalificationVendor | null;
}

export const vendorPQRequirementGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {

    const vendor = await o.findOneVendorCIVD(ctx, req.vendorCIVDLogin.civdVendorId);

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    if (!pqVendor) {
      return { prequalificationVendor: null };
    }
    
    return { prequalificationVendor: pqVendor };
  },
};
