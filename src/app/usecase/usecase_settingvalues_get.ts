import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindSettingValues, SettingValues } from "../model/model_setting_values.js";

class Gateways {
  findSettingValues: FindSettingValues;
}

export class Request {}

export class Response extends InputResponseWithCount<SettingValues> {}

export const settingValuesGet: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx) => {
    const [items, _] = await o.findSettingValues(ctx, {});

    const result = items.reduce<Record<string, string>>((acc, item) => {
      acc[item.id] = item.value;
      return acc;
    }, {});

    return result as any;
  },
};
