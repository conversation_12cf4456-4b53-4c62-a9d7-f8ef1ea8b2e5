import { Usecase } from "../../framework/core.js";
import { FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindOnePQVendor, PrequalificationVendor } from "../model/model_prequalification.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  findOneVendorCIVD: FindOneVendorCIVD;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
}

export class Response {
  prequalificationVendor: PrequalificationVendor | null;
}

export const vendorPQGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // add phase in request
    const vendor = await o.findOneVendorCIVD(ctx, req.vendorCIVDLogin.civdVendorId);

    if (!vendor) {
      throw new Error("Vendor does not have permission to access data");
    }
    
    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    return { prequalificationVendor: pqVendor };
  },
};
