import { Usecase } from "../../framework/core.js";
import { FindUserAssigned } from "../model/model_requisition.js";
import { FindUser, User, UserLogin } from "../model/model_user.js";

class Gateways {
  findUser: FindUser;
  findUserAssigned: FindUserAssigned;
}

export class Request {
  nameLike: string;
  userLogin: UserLogin;
}

export class Response {
  users: UserWithCount[];
}

export const requisitionUserPIC: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [users] = await o.findUser(ctx, {
      onlyIsSectionUser: true,
      role: "STAFF",
      departmentId: "70210000",
    });

    const userWithCount = await o.findUserAssigned(ctx, {
      users,
    });

    const result: UserWithCount[] = userWithCount.map((u) => {
      return {
        user: u.user,
        count: u.count,
      };
    });

    return {
      users: result,
    };
  },
};

type UserWithCount = {
  user: User;
  count: number;
};
