import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { collectDepartments, FindDepartment } from "../model/model_department.js";
import { FindAllProcPlanDetailFilter, FindProcPlanDetail, ProcPlanDetail } from "../model/model_procplan.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { DateNowHandler, ProcPlan, TypeOf } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findProcPlanDetail: FindProcPlanDetail;
  dateNow: DateNowHandler;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findDepartment: FindDepartment;
  findUser: FindUser;
}

export class Request extends FindAllProcPlanDetailFilter {
  userLogin: UserLogin;
  isHistory: boolean;
}

export class Response extends InputResponseWithCount<ProcPlanDetail> {}

export const procplanGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    if (req.ids && req.ids.length > 0) {
      const [ppds, count] = await o.findProcPlanDetail(ctx, {
        ids: req.ids,
      });

      return {
        items: ppds,
        count: count,
      };
    }

    const now = await o.dateNow(ctx);
    let [departments] = await o.findDepartment(ctx, {});
    let firstApprovalTemplateGroup = null;

    if (req.procPlanType!.toUpperCase() === "APP") {
      if (req.isHistory) {
        const [atgs] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });
        firstApprovalTemplateGroup = atgs[0].approvalTemplates.find((at) => at.users?.find((x) => x.id === req.userLogin.id)) || req.userLogin.isAdminProcPlan;
      }

      if (!firstApprovalTemplateGroup) {
        const deptSet = new Set<string>();
        await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);
        [departments] = deptSet.size > 0 ? await o.findDepartment(ctx, { ids: [...deptSet], size: 9999 }) : [[]];
      }
    }

    if (req.procPlanType!.toUpperCase() === "UPP") {
      const deptSet = new Set<string>();
      await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);
      [departments] = deptSet.size > 0 ? await o.findDepartment(ctx, { ids: [...deptSet], size: 9999 }) : [[]];
    }

    if (req.departmentId) {
      departments = departments.filter((dep) => dep.id === req.departmentId);
    }

    const [ppds, count] = await o.findProcPlanDetail(ctx, {
      procPlanType: req.procPlanType!.toUpperCase() as TypeOf<typeof ProcPlan>,
      departmentIds: departments.map((d) => d.id),
      sectionId: req.sectionId,
      year: req.year,
      title: req.title,
      currentYear: getDateOnly(now).getFullYear(),
      includeCurrentYear: req.includeCurrentYear,
      status: !!firstApprovalTemplateGroup ? "" : req.status,
      page: req.page,
      size: req.size,
      procPlanCode: req.procPlanCode,
    });

    return {
      items: ppds,
      count: count,
    };
  },
};
