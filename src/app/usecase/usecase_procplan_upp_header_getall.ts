import { Usecase } from "../../framework/core.js";
import { BaseFindManyFilter, InputResponseWithCount } from "../../framework/repository.js";
import { FindApprovalGroup, getLastApprovalGroupDurationDays } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { collectDepartments, Department } from "../model/model_department.js";
import { FindProcPlanHeader, ProcPlanHeader } from "../model/model_procplan.js";
import { FindSection, Section } from "../model/model_section.js";
import { FindUser, UserLogin } from "../model/model_user.js";
import { DateNowHandler, getEmptyValueCurrency } from "../model/vo.js";

class Gateways {
  findSection: FindSection;
  findProcPlanHeader: FindProcPlanHeader;
  findUser: FindUser;
  findApprovalGroup: FindApprovalGroup;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request extends BaseFindManyFilter {
  userLogin: UserLogin;
  year: number;
}

export class Response extends InputResponseWithCount<ProcPlanHeader> { }

export const procplanUppHeaderGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const deptSet = new Set<string>();
    await collectDepartments(ctx, o.findUser, req.userLogin, deptSet);
    const [sections] = deptSet.size > 0 ? await o.findSection(ctx, { departmentIds: [...deptSet] }) : [[]];

    const [pphs] = await o.findProcPlanHeader(ctx, {
      year: req.year,
      sectionIds: sections.map((d) => d.id),
      procPlanType: "UPP",
      useSelect: true,
    });

    const results: ProcPlanHeader[] = [];
    for (const sc of sections) {
      //
      const pph = pphs.find((d) => d.section?.id === sc.id);
      if (!pph) {
        results.push(emptyProcplanHeader(sc, sc.department!));
      } else {
        pph.durationDays = await getLastApprovalGroupDurationDays(ctx, o.findApprovalGroup, pph.approvalGroup, now, o.findCalendar);
        results.push(pph);
      }
    }

    return {
      items: results,
      count: results.length,
    };
  },
};

const emptyProcplanHeader = (section: Section, department: Department): ProcPlanHeader => {
  return {
    id: "",
    procPlanType: "UPP",
    status: "NOT_CREATED",
    isSendBack: false,
    submittedDate: null,
    year: 0,
    count: 0,
    section,
    department,
    approvalGroup: null,
    totalValueEstimation: getEmptyValueCurrency(),
    submitter: null,
    sections: [],
  };
};
