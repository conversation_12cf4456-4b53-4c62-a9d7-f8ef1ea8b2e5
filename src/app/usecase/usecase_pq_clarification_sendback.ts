import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  comment: string;
}

export class Response { }

export const pqClarificationSendback: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== "CLARIFICATION") {
      throw new Error("pq must in CLARIFICATION PHASE");
    }

    if (pqTemplate.phasesClarification?.approval?.status !== "ON_REVIEW") {
      throw new Error("pq must in ON_REVIEW state");
    }

    validateApprovalAction(req.userLogin, pqTemplate.phasesClarification.approval?.approvalGroup!);

    const [approvals] = await o.findApprovalGroup(ctx, { documentId: pqTemplate.id });
    approvals.forEach((apps, i) => {
      apps.approvals.forEach((app) => {
        app.date = null;
        app.signer = null;
        app.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
      });
      apps.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
    });

    await o.saveApprovalGroups(ctx, approvals);

    pqTemplate.phasesClarification.approval.status = "DRAFT";
    pqTemplate.phasesClarification.approval.isSendBack = true;
    pqTemplate.phasesClarification.approval.approvalGroup = approvals[0];

    await o.savePQTemplate(ctx, pqTemplate);

    if (req.comment === "") {
      throw new Error("comment is required");
    }

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: "PQ_CLARIFICATION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Clarification Sent Back`,
      user: req.userLogin,
      id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};
