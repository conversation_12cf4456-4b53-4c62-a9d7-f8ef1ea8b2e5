import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindDocumentHistory, FindDocumentHistoryFilter } from "../model/model_document_history.js";
import { User } from "../model/model_user.js";

class Gateways {
  findDocumentHistory: FindDocumentHistory;
}

export class Request extends FindDocumentHistoryFilter {}

export class Response extends InputResponseWithCount<Item> {}

export const documentHistoryGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findDocumentHistory(ctx, req);

    return {
      items: items.map((x) => ({
        date: x.date,
        user: x.user!,
        message: x.message,
        comment: x.comment,
      })),
      count,
    };
  },
};

type Item = {
  date: Date | null;
  user: User;
  message: string;
  comment: string;
};
