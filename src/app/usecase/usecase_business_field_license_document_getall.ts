import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { sheetNamesList } from "../utility/worksheets.js";

class Gateways { }

export class Request {
  search: string;
}

export class Response extends InputResponseWithCount<string> { }

export const businessFieldLicenseDocumentGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const items = sheetNamesList().filter((item) => {
      if (req.search) {
        return item.toLowerCase().includes(req.search.toLowerCase())
      }

      return item;
    });

    const count = items.length;

    return { items, count };
  },
};
