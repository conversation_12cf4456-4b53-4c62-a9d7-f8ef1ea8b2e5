import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, SaveApprovalGroups, getApprovalList } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { vendorCIVDLogin } from "../model/model_civd_vendor.js";
import { FindDelegation } from "../model/model_delegation.js";
import { FindOnePQTemplate, FindOnePQVendor, PrequalificationVendor, SavePQVendor } from "../model/model_prequalification.js";
import { FindUser } from "../model/model_user.js";
import { DateNowHandler, RandomStringHandler, SharepointFile, TypeOf, deskripsiDokumenDomisili } from "../model/vo.js";
import { getDateOnly, toUSD } from "../utility/helper.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  findOnePQTemplate: FindOnePQTemplate;
  findUser: FindUser;
  findDelegation: FindDelegation;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  saveApprovalGroups: SaveApprovalGroups;
  deleteApprovalGroups: DeleteApprovalGroups;
  randomString: RandomStringHandler;
  dateNow: DateNowHandler;
}

export class Request {
  pqId: string;
  vendorCIVDLogin: vendorCIVDLogin;
  suratMinat: SharepointFile[];
  suratKuasaMinat?: SharepointFile[];
  suratSPDA: SharepointFile[];
  suratIzinUsaha: SharepointFile[];
  sertifikatTKDN?: SharepointFile[];
  dokumenDomisili?: SharepointFile[];
  dokumenDomisiliDeskripsi?: TypeOf<typeof deskripsiDokumenDomisili>;
}

export class Response {}

export const vendorPQRegistrationSubmit: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqTemplate.pqRegistrationDate) {
      throw new Error("pq registration date not found");
    }

    if (getDateOnly(now) > pqTemplate.pqRegistrationDate) {
      throw new Error("pq registration date is expired");
    }

    const useDokumenDomisili = toUSD(pqTemplate.currency, pqTemplate.ownerEstimateValue) < 1_000_000;
    const useSertifikatTKDN = pqTemplate.commodity === "GOODS" && (pqTemplate.localContentLevel ?? 0) >= 10;

    if (useSertifikatTKDN && !req.sertifikatTKDN) throw new Error("sertifikat TKDN is required");
    if (useDokumenDomisili && !req.dokumenDomisili) throw new Error("dokumen domisili is required for national OE");

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    if (pqVendor) {
      throw new Error("pq vendor already submitted");
    }

    const newPqVendorId = `PQV-${await o.randomString(ctx)}`;

    const approvals = await getApprovalList(
      ctx,
      `Approval for PQ Vendor ${newPqVendorId}`,
      o.findUser,
      o.findApprovalTemplateGroup,
      o.findDelegation,
      [],
      newPqVendorId,
      "PQ_REGISTRATION",
      new Date().getFullYear()
    );
    approvals[0].status = "NOT_STARTED";
    approvals[0].approvals[0].status = "NOT_STARTED";

    await o.deleteApprovalGroups(ctx, { documentId: newPqVendorId, documentType: "PQ_REGISTRATION" });
    await o.saveApprovalGroups(ctx, approvals);

    let newPQVendor: PrequalificationVendor = {
      id: newPqVendorId,
      prequalificationTemplate: pqTemplate,
      civdVendorId: req.vendorCIVDLogin.civdVendorId,
      status: "REGISTRATION_SUBMITTED",
      phaseRegistration: {
        suratMinat: { files: req.suratMinat, result: "UNDECIDED" },
        suratKuasaMinat: req.suratKuasaMinat ? { files: req.suratKuasaMinat, result: "UNDECIDED" } : undefined, // optional
        suratSPDA: { files: req.suratSPDA, result: "UNDECIDED" },
        dokumenDomisili: req.dokumenDomisili ? { files: req.dokumenDomisili, result: "UNDECIDED", description: req.dokumenDomisiliDeskripsi } : undefined,
        suratIzinUsaha: { files: req.suratIzinUsaha, result: "UNDECIDED" },
        sertifikatTKDN: req.sertifikatTKDN ? { files: req.sertifikatTKDN, result: "UNDECIDED" } : undefined,
        resultSummary: "UNDECIDED",
        submitDate: getDateOnly(now),
        evaluationDate: null,
      },
    };

    await o.savePQVendor(ctx, newPQVendor);

    return {};
  },
};
