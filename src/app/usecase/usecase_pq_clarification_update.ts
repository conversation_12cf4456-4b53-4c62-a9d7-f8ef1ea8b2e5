import { Usecase } from "../../framework/core.js";
import { getA<PERSON><PERSON><PERSON><PERSON>ist, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindDelegation } from "../model/model_delegation.js";
import { FindOnePQTemplate, FindOnePQVendor, FindPQVendor, SavePQVendor } from "../model/model_prequalification.js";
import { FindUser } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, RandomStringHandler, TypeOf } from "../model/vo.js";
import { getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
  findPQVendor: FindPQVendor;
  savePQVendor: SavePQVendor;
  dateNow: DateNowHandler;
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findDelegation: FindDelegation;
  randomString: RandomStringHandler;
  saveApprovalGroups: SaveApprovalGroups;
}

export class Request {
  pqId: string;
  vendorId: string;
  response: "RESULT" | "MEETING";
  meetingInfo: {
    place: string;
    meetingDate: Date;
    meetingTime: string;
    minutesOfMeeting: string[];
  } | null;
}

export class Response {}

export const pqClarificationUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      return {};
    }

    pqTemplate.phasesClarification!.response = req.response;
    pqTemplate.phasesClarification!.dueDate = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000); // 2 days after today (due date vendor registration from dashboard);

    const newPqId = `PQ-${await o.randomString(ctx)}`;
    let approvalDocType: TypeOf<typeof DocumentTemplate> = `PQ_CLARIFICATION`;
    let approvalMsg = ``;

    if (req.response === "RESULT") {
      approvalDocType = "PQ_FINAL_RESULT";
      approvalMsg = `Approval for PQ Final Evaluation Result ${newPqId}`;

      // update all pq vendor "RESULT"
      for (const pqVendor of pqVendors) {
        if (pqVendor.status === "CLARIFICATION_DRAFT") {
          pqVendor.status = "CLARIFICATION_APPROVED";
          await o.savePQVendor(ctx, pqVendor);
        }
      }
    } else if (req.response === "MEETING") {
      approvalMsg = `Approval for PQ Clarification Evaluation ${newPqId}`;

      if (req.meetingInfo === null) {
        throw new Error("response 'meeting', meeting info cannot be null");
      }

      // submit meeting info each PQ Vendor
      for (const pqVendor of pqVendors) {
        if (pqVendor.status === "CLARIFICATION_DRAFT") {
          pqVendor.status = "CLARIFICATION_APPROVED";
          // pqVendor.phaseClarification!.meetingInfo = {
          //   ...req.meetingInfo,
          //   status: "SUBMITTED",
          // };

          await o.savePQVendor(ctx, pqVendor);
        }
      }
      // approve auto approve first approval step
    }

    // create approval template
    const approvals = await getApprovalList(
      ctx,
      approvalMsg,
      o.findUser,
      o.findApprovalTemplateGroup,
      o.findDelegation,
      [],
      newPqId,
      approvalDocType,
      new Date().getFullYear()
    );
    approvals[0].status = "NOT_STARTED";
    approvals[0].approvals[0].status = "NOT_STARTED";

    // await o.deleteApprovalGroups(ctx, { documentId: newPqId, documentType: "PQ_REGISTRATION" });

    await o.saveApprovalGroups(ctx, approvals);

    return {};
  },
};
