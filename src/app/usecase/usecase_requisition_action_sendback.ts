import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindRequisition, getCcEmailRequisition, getEmailDataRequisition, SaveRequisition } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, formatNumber } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTableData, sendSendBackMail } from "../utility/mailer_2.js";

class Gateways {
  findRequisition: FindRequisition;
  findApprovalGroup: FindApprovalGroup;
  saveRequisition: SaveRequisition;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  dateNow: DateNowHandler;
  findDelegation: FindDelegation;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
  comment: string;
  // now: DateOrString;
}

export class Response { }

export const requisitionActionSendback: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);

    const [rqs] = await o.findRequisition(ctx, { id: req.requisitionId });
    const rq = rqs.length > 0 ? rqs[0] : null;
    if (!rq) {
      throw new Error("requisition not found");
    }

    if (rq.status !== "ON_REVIEW") {
      throw new Error("requisition must in ON_REVIEW state");
    }

    const [checkActing] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "ACTING" });
    const acting = checkActing.length > 0 ? checkActing[0] : null;

    validateApprovalAction(req.userLogin, rq.approvalGroup!, null, acting);

    const [approvals] = await o.findApprovalGroup(ctx, { documentId: rq.id });
    approvals.forEach((apps, i) => {
      apps.approvals.forEach((app) => {
        app.date = null;
        app.signer = null;
        app.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
      });
      apps.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
      apps.durationDays = 0;
    });

    await o.saveApprovalGroups(ctx, approvals);

    rq.status = "DRAFT";
    rq.isSendBack = true;
    rq.approvalGroup = approvals[0];

    await o.saveRequisition(ctx, rq);

    if (req.comment === "") {
      throw new Error("comment is required");
    }

    await o.saveDocumentHistory(ctx, {
      documentId: rq.id!,
      documentType: "REQUISITION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `Requisition Sent Back`,
      user: req.userLogin,
      id: `${rq.id.slice(4)}-${formatDateWithSecond(now)}`,
    });

    let mailRecipients: { name: string, email: string }[] = []; // submitter, request for

    mailRecipients.push({ name: rq.submitter!.name!, email: rq.submitter!.email! });

    if (rq.requestFor && rq.requestFor.id !== rq.submitter!.id) {
      const requestForRecipient = rq.requestFor;
      mailRecipients.push({ name: requestForRecipient.name!, email: requestForRecipient.email! });
    }

    const ccUsers = getCcEmailRequisition(rq, req.userLogin);
    const mailData: MailTableData[] = getEmailDataRequisition(rq, req.userLogin);

    for (const mailRecipient of mailRecipients) {
      if (mailRecipient.email || mailRecipient.email !== "") {
        sendSendBackMail({
          sendToUserMail: mailRecipient.email!,
          sendToUserName: mailRecipient.name!,
          ccUserMail: ccUsers,
        }, mailData, "REQUISITION");
      }
    }

    return {};
  },
};
