import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, getApproval, moveApproval, validateApprovalAction } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  comment: string;
}

export class Response { }

export const pqClarificationApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== "CLARIFICATION") {
      throw new Error("pq must in CLARIFICATION PHASE");
    }

    if (pqTemplate.phasesClarification?.approval?.status !== "ON_REVIEW") {
      throw new Error("pq must in ON_REVIEW state");
    }

    validateApprovalAction(req.userLogin, pqTemplate.phasesClarification.approval?.approvalGroup!);

    const approval = getApproval(pqTemplate.phasesClarification.approval.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = pqTemplate.phasesClarification.approval.approvalGroup ? pqTemplate.phasesClarification.approval.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    await moveApproval(ctx, paralelAND, pqTemplate.phasesClarification.approval, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    // hasil copas dari phase pq_requirement
    // if (!pqTemplate.phasesClarification.approvalGroup || !pqTemplate.phasesClarification.approvalGroup?.nextApprovalGroupId) {
    //   // check if it last approval
    //   pqTemplate.announcementDate = getDateOnly(now);
    //   pqTemplate.pqRegistrationDate = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000); // 2 days after today
    // }

    await o.savePQTemplate(ctx, pqTemplate);

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: "PQ_CLARIFICATION",
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Clarification Approve`,
      user: req.userLogin,
      id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};
