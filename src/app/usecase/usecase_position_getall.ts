import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindPosition, FindPositionFilter, Position } from "../model/model_position.js";

class Gateways {
  findPosition: FindPosition;
}

export class Request extends FindPositionFilter {}

export class Response extends InputResponseWithCount<Position> {}

export const positionGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findPosition(ctx, req);
    return { items, count };
  },
};
