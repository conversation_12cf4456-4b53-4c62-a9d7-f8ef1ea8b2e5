import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, getApprovalWithRequestForRuleList, SaveApprovalGroups } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { EvaluationSubmission, FindOnePQTemplate, FindPQVendor, SavePQTemplate, SavePQVendor } from "../model/model_prequalification.js";
import { FindRequisition } from "../model/model_requisition.js";
import { FindUserSupervisors } from "../model/model_user.js";
import { Vendor } from "../model/model_vendor.js";
import { DateNowHandler } from "../model/vo.js";
import { markHistoryFiles } from "../utility/fileupload.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findPQVendor: FindPQVendor;
  savePQTemplate: SavePQTemplate;
  savePQVendor: SavePQVendor;
  dateNow: DateNowHandler;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findRequisition: FindRequisition;
  deleteApprovalGroups: DeleteApprovalGroups;
  saveApprovalGroups: SaveApprovalGroups;
}

export class Request {
  pqId: string;
  userLogin: Vendor;
}

export class Response {}

export const pqRegistrationComplete: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (!pqTemplate.pqRegistrationDate) {
      throw new Error("pq registration date not found");
    }

    if (pqTemplate.currentPhase !== "REGISTRATION") {
      throw new Error("pq must in REGISTRATION PHASE");
    }

    // generate EVALUATION_1 approvals
    const description: string = `${pqTemplate.title} - PQ Evaluation Phase 1`;
    const [rqs] = await o.findRequisition(ctx, { tenderCode: pqTemplate.tenderCode });
    if (!rqs || rqs.length === 0) {
      throw new Error("Requisition not found");
    }

    if (!rqs[0].requestFor) {
      throw new Error("Requisition requestFor user not found");
    }

    const approvals = await getApprovalWithRequestForRuleList(
      ctx,
      description,
      pqTemplate.assignedUser!.position!.id!,
      rqs[0].requestFor!.position!.id!,
      o.findUserSupervisors,
      o.findApprovalTemplateGroup,
      pqTemplate.id,
      "PQ_EVALUATION_1",
      new Date().getFullYear()
    );

    await o.deleteApprovalGroups(ctx, { documentId: pqTemplate.id, documentType: "PQ_EVALUATION_1" });

    await o.saveApprovalGroups(ctx, approvals);

    // Update PQ Template
    pqTemplate.currentPhase = "EVALUATION_1";
    pqTemplate.phasesRegistration = {
      status: "APPROVED",
      approvalGroup: null,
      isSendBack: false,
    };

    const pqEvaluation1: EvaluationSubmission = {
      response: "UNDECIDED",
      dueDate: pqTemplate.pqSubmissionDate!,
      approval: {
        status: "DRAFT",
        isSendBack: false,
        approvalGroup: approvals[0],
      },
    };

    pqTemplate.phasesEvaluation = {
      evaluation1: pqEvaluation1,
      evaluation2: null,
      evaluation3: null,
    };

    await o.savePQTemplate(ctx, pqTemplate);

    // Copy PQ Registration to PQ Evaluation 1
    const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });
    for (const pqVendor of pqVendors) {
      if (pqVendor.phaseRegistration?.resultSummary === "PASS" && !!pqVendor.phaseRegistration.evaluationDate) {
        const submission: any = {
          submission1: {
            suratSPDA: pqVendor.phaseRegistration?.suratSPDA
              ? {
                  ...pqVendor.phaseRegistration.suratSPDA,
                  files: markHistoryFiles(pqVendor.phaseRegistration.suratSPDA.files),
                  result: "UNDECIDED",
                }
              : null,
            dokumenDomisili: pqVendor.phaseRegistration?.dokumenDomisili
              ? {
                  ...pqVendor.phaseRegistration.dokumenDomisili,
                  files: markHistoryFiles(pqVendor.phaseRegistration.dokumenDomisili.files),
                  result: "UNDECIDED",
                }
              : null,
            suratIzinUsaha: pqVendor.phaseRegistration?.suratIzinUsaha
              ? {
                  ...pqVendor.phaseRegistration.suratIzinUsaha,
                  files: markHistoryFiles(pqVendor.phaseRegistration.suratIzinUsaha.files),
                  result: "UNDECIDED",
                }
              : null,
            sertifikatTKDN: pqVendor.phaseRegistration?.sertifikatTKDN
              ? {
                  ...pqVendor.phaseRegistration.sertifikatTKDN,
                  files: markHistoryFiles(pqVendor.phaseRegistration.sertifikatTKDN.files),
                  result: "UNDECIDED",
                }
              : null,
          },
        };

        pqVendor.phaseSubmission = submission;
        pqVendor.status = "SUBMISSION_1_DRAFT";
      } else {
        pqVendor.status = "REGISTRATION_REJECTED";
        pqVendor.phaseRegistration!.resultSummary = "FAIL";
      }

      await o.savePQVendor(ctx, pqVendor);
    }

    // TODO: selesaikan approval tian yang masih ngegantung

    return {};
  },
};
