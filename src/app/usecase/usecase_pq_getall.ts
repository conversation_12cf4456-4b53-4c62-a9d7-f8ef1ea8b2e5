import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { FindPQTemplate, PrequalificationTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { PreQualificationPhase, TypeOf, getApprover, getPhaseStatus } from "../model/vo.js";

class Gateways {
  findPQTemplate: FindPQTemplate;
}

export class Request {
  userLogin: UserLogin;
  assignedUserId?: string;
  as?: "pic" | "btb";
  keyword?: string;
  currentPhase?: TypeOf<typeof PreQualificationPhase>;
}

export class Response extends InputResponseWithCount<PrequalificationTemplate> {}

export const pqGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findPQTemplate(ctx, req);

    return {
      items: items.map((item) => {
        const phaseStatus = getPhaseStatus(req.currentPhase, item);
        const canSubmit = phaseStatus === "DRAFT" && [...item.requestedBackToBacks, item.assignedUser?.id].some((id) => id === req.userLogin.id);

        const approver = getApprover(req.currentPhase, item);
        const canApproveAndSendback = phaseStatus === "ON_REVIEW" && approver?.id === req.userLogin.id;

        return {
          ...item,
          canSubmit,
          canApproveAndSendback,
        };
      }),
      count,
    };
  },
};
