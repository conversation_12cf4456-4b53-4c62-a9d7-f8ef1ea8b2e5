import { Usecase } from "../../framework/core.js";
import { FindOneVendorCIVD, vendorCIVDLogin } from "../model/model_civd_vendor.js";
import {
  CompanyEntity,
  FindOnePQTemplate,
  FindOnePQVendor,
  PQOtherDocuments,
  PQSubmissionInfo,
  SavePQVendor,
  SummaryExperience,
} from "../model/model_prequalification.js";
import { DateNowHandler, deskripsiBuktiStatusPDN, deskripsiDokumenDomisili, dokumenK3LL, SharepointFile, TypeOf } from "../model/vo.js";
import { getDateOnly, toUSD } from "../utility/helper.js";

class Gateways {
  findOnePQVendor: FindOnePQVendor;
  savePQVendor: SavePQVendor;
  findOneVendorCIVD: FindOneVendorCIVD;
  findOnePQTemplate: FindOnePQTemplate;
  dateNow: DateNowHandler;
}

export class Request {
  vendorCIVDLogin: vendorCIVDLogin;
  pqId: string;
  submissionPhase: 1 | 2 | 3;
  submissionPayload: {
    entity: "CONSORTIUM" | "SINGLE";
    companyEntities: CompanyEntity[]; // general information, bila entity = single hanya ada 1 data perusahaan ; bila entity = consortium, pemuka harus ada 1 dan tidak boleh lebih dari 1
    suratPernyataanPQ: SharepointFile[];
    suratKuasaPernyataanPQ?: SharepointFile[];
    suratSPDA: SharepointFile[];
    dokumenBuktiStatusPDN: SharepointFile[];
    dokumenBuktiStatusPDNDeskripsi: TypeOf<typeof deskripsiBuktiStatusPDN>;
    dokumenDomisili: SharepointFile[];
    dokumenDomisiliDeskripsi: TypeOf<typeof deskripsiDokumenDomisili>;
    suratIzinUsaha: SharepointFile[];
    sertifikatTKDN: SharepointFile[];
    suratPerjanjianKonsorsium?: SharepointFile[];
    dokumenK3LL: SharepointFile[];
    dokumenK3LLDeskripsi: TypeOf<typeof dokumenK3LL>;
    summaryExperiences: SummaryExperience[];
    dokumenEvaluasiKemampuanFinansial: SharepointFile[];
    dokumenLaporanKeuangan: SharepointFile[];
    otherDocuments?: PQOtherDocuments[];
  };
}

export class Response {}

export const vendorPQSubmissionSave: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const now = await o.dateNow(ctx);

    const pqTemplate = await o.findOnePQTemplate(ctx, { pqId: req.pqId });
    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== `EVALUATION_${req.submissionPhase}`) {
      throw new Error("pq must be on Submission phase");
    }

    const dueDate = pqTemplate.phasesEvaluation?.[`evaluation${req.submissionPhase}`]?.dueDate;
    if (!dueDate) throw new Error("pq submission date not found");

    if (getDateOnly(now) > dueDate) throw new Error("pq submission date is expired");

    const pqVendor = await o.findOnePQVendor(ctx, { pqId: req.pqId, civdVendorId: req.vendorCIVDLogin.civdVendorId });
    if (!pqVendor || !pqVendor.phaseSubmission || !pqVendor.phaseSubmission[`submission${req.submissionPhase}`]) {
      throw new Error("pq vendor not found");
    }

    if (pqVendor.status !== `SUBMISSION_${req.submissionPhase}_DRAFT`) {
      throw new Error("pq must be on Submission phase");
    }

    const useBuktiPDN = true;
    const useDokumenDomisili = toUSD(pqTemplate.currency, pqTemplate.ownerEstimateValue) < 1_000_000;
    const useSertifikatTKDN = pqTemplate.commodity === "GOODS" && (pqTemplate.localContentLevel ?? 0) >= 10;
    const useDokumenKeuangan = pqTemplate.financialDueDiligence;
    const useKDNPt = (pqTemplate.basicCapability ?? 0) > 0;

    if (useBuktiPDN && !req.submissionPayload.dokumenBuktiStatusPDN) throw new Error("dokumen bukti status PDN is required");
    if (useDokumenDomisili && !req.submissionPayload.dokumenDomisili) throw new Error("dokumen domisili is required for national OE");
    if (useSertifikatTKDN && !req.submissionPayload.sertifikatTKDN) throw new Error("sertifikat TKDN is required");
    if (useDokumenKeuangan && (!req.submissionPayload.dokumenEvaluasiKemampuanFinansial || !req.submissionPayload.dokumenLaporanKeuangan))
      throw new Error("dokumen keuangan is required");
    if (useKDNPt && (req.submissionPayload.summaryExperiences ?? []).length === 0) throw new Error("dokumen summary experiences is required");

    if (req.submissionPayload.entity === "CONSORTIUM" && !req.submissionPayload.suratPerjanjianKonsorsium) {
      throw new Error("surat perjanjian konsorsium is required for consortium");
    }

    (req.submissionPayload.otherDocuments ?? []).forEach((otherDocuments) => {
      if (otherDocuments.show && !otherDocuments.files) throw new Error("other documents is required");
    });

    const phaseSubmissionPayload: PQSubmissionInfo = {
      entity: req.submissionPayload.entity,
      // general information, bila entity = single hanya ada 1 data perusahaan ; bila entity = consortium, pemuka harus ada 1 dan tidak boleh lebih dari 1
      companyEntities: req.submissionPayload.companyEntities,

      suratPernyataanPQ: { files: req.submissionPayload.suratPernyataanPQ, result: "UNDECIDED" },
      suratKuasaPernyataanPQ: req.submissionPayload.suratKuasaPernyataanPQ ? { files: req.submissionPayload.suratKuasaPernyataanPQ, result: "PASS" } : null,
      suratSPDA: { files: req.submissionPayload.suratSPDA, result: "UNDECIDED" },
      dokumenBuktiStatusPDN: req.submissionPayload.dokumenBuktiStatusPDN
        ? {
            files: req.submissionPayload.dokumenBuktiStatusPDN,
            description: req.submissionPayload.dokumenBuktiStatusPDNDeskripsi,
            result: "UNDECIDED",
          }
        : null,
      dokumenDomisili: req.submissionPayload.dokumenDomisili
        ? { files: req.submissionPayload.dokumenDomisili, description: req.submissionPayload.dokumenDomisiliDeskripsi, result: "UNDECIDED" }
        : null,
      suratIzinUsaha: { files: req.submissionPayload.suratIzinUsaha, result: "UNDECIDED" },
      sertifikatTKDN: req.submissionPayload.sertifikatTKDN ? { files: req.submissionPayload.sertifikatTKDN, result: "UNDECIDED" } : null,

      // if entity = consortium
      suratPerjanjianKonsorsium: req.submissionPayload.suratPerjanjianKonsorsium
        ? { files: req.submissionPayload.suratPerjanjianKonsorsium, result: "UNDECIDED" }
        : null,

      dokumenK3LL: { files: req.submissionPayload.dokumenK3LL, description: req.submissionPayload.dokumenK3LLDeskripsi, result: "UNDECIDED" },
      summaryExperiences: req.submissionPayload.summaryExperiences ? { experiences: req.submissionPayload.summaryExperiences, result: "UNDECIDED" } : null,

      dokumenEvaluasiKemampuanFinansial: req.submissionPayload.dokumenEvaluasiKemampuanFinansial
        ? { files: req.submissionPayload.dokumenEvaluasiKemampuanFinansial, result: "UNDECIDED" }
        : null,
      dokumenLaporanKeuangan: req.submissionPayload.dokumenLaporanKeuangan ? { files: req.submissionPayload.dokumenLaporanKeuangan, result: "PASS" } : null,

      otherDocuments: (req.submissionPayload.otherDocuments ?? []).map((otherDocuments) => ({
        text: otherDocuments.text,
        show: otherDocuments.show,
        files: otherDocuments.files ?? null,
        result: otherDocuments.show ? "UNDECIDED" : "PASS",
      })),

      resultSummary: "UNDECIDED",
      submitDate: null,
      evaluationDate: null,
    };

    pqVendor.phaseSubmission[`submission${req.submissionPhase}`] = phaseSubmissionPayload;

    await o.savePQVendor(ctx, pqVendor);

    return { req: pqVendor.phaseSubmission };
  },
};
