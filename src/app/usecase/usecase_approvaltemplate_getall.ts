import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { ApprovalTemplateGroup, FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { DocumentTemplate, TypeOf } from "../model/vo.js";

class Gateways {
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
}

export class Request {
  documentType: TypeOf<typeof DocumentTemplate>;
}

export class Response extends InputResponseWithCount<ApprovalTemplateGroup> {}

export const approvalTemplateGetAll: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [items, count] = await o.findApprovalTemplateGroup(ctx, { documentType: req.documentType });
    return { items, count };
  },
};
