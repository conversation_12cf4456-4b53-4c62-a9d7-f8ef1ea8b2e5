import { Usecase } from "../../framework/core.js";
import { FindRequisition, HSERiskCategory, SaveRequisition } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";

class Gateways {
  findRequisition: FindRequisition;
  saveRequisition: SaveRequisition;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
  typeWork: string;
  hseAssessment: string;
  hseRiskCategory: HSERiskCategory[];
}

export class Response { }

export const requisitionHseRiskUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [rqs, count] = await o.findRequisition(ctx, {
      id: req.requisitionId,
      size: 1,
      status: "ON_REVIEW",
    });

    if (count === 0) {
      throw new Error("requisition not found");
    }

    const rq = rqs[0];

    // if (rq.approvalGroup?.approvals[0].subDocumentType !== "HSE_RISK_ASSESSMENT") {
    //   throw new Error("requisition must in HSE_RISK_ASSESSMENT sub document type");
    // }

    await o.saveRequisition(ctx, {
      ...rq,
      typeWork: req.typeWork,
      hseAssessment: req.hseAssessment,
      hseRiskCategory: req.hseRiskCategory,
    });

    return {};
  },
};
