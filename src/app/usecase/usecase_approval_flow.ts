import { Usecase } from "../../framework/core.js";
import { InputResponseWithCount } from "../../framework/repository.js";
import { Approval, ApprovalGroup, getApprovalList } from "../model/model_approval.js";
import { ApprovalTemplateRule, FindApprovalTemplateGroup, getRequistionDocRule, requisitionDocRuleValue } from "../model/model_approval_template.js";
import { FindDelegation } from "../model/model_delegation.js";
import { Position } from "../model/model_position.js";
import { FindUser, FindUserSupervisors, User } from "../model/model_user.js";
import {
  ApprovalStatus,
  Commodity,
  ContractTypeEngagement,
  ContractTypePayment,
  DocumentTemplate,
  SubDocumentRequisition,
  TenderMethod,
  TypeOf,
  UserRole,
} from "../model/vo.js";

class Gateways {
  findUser: FindUser;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findUserSupervisors: FindUserSupervisors;
  findDelegation: FindDelegation;
}

class Request {
  documentType: TypeOf<typeof DocumentTemplate>;
  contractTypeEngagement: TypeOf<typeof ContractTypeEngagement>;
  contractTypePayment: TypeOf<typeof ContractTypePayment>;
  value: number;
  commodity: TypeOf<typeof Commodity>;
  positionId: string;
  departmentCode: string;
  departmentId: string;
  budgetOwners: string[];
  isAdditionalProcPlan: boolean;
  procPlanValue: number;
  tenderMethod: TypeOf<typeof TenderMethod>;
}

class Response extends InputResponseWithCount<ApprovalHeader> { }

export const approvalFlow: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    // 
    const { minRQValue, maxRQValue, minOEValue, maxOEValue } = requisitionDocRuleValue;
    const docRule: ApprovalTemplateRule = getRequistionDocRule(
      req.commodity,
      req.departmentCode,
      req.budgetOwners,
      req.isAdditionalProcPlan,
      req.tenderMethod,
      req.contractTypeEngagement,
      req.value, // already converted USD from request body
      req.procPlanValue, // already converted USD from request body
    );

    const [listUser] = await o.findUserSupervisors(ctx, { positionId: req.positionId });
    if (listUser.length === 0) {
      throw new Error(`user supervisor for ${req.positionId} is not found`);
    }

    let listUserBudgetOwners: User[] = [];

    if (docRule.additionalBudgetOwnerManager && req.budgetOwners.length > 0) {
      for (const budgetOwner of req.budgetOwners) {
        const [budgetOwnerUsers] = await o.findUser(ctx, { departmentId: budgetOwner });
        const budgetOwnerUser = budgetOwnerUsers.filter((user) => user.position?.role === "MGR" && user.department?.id !== req.departmentId);
        if (budgetOwnerUser.length > 0) {
          listUserBudgetOwners.push(...budgetOwnerUser);
        }
      }
    }

    const items = await getApprovalList(ctx, "A", o.findUser, o.findApprovalTemplateGroup, o.findDelegation, listUser, "X", req.documentType, new Date().getFullYear(), docRule, listUserBudgetOwners);

    if (req.documentType === "REQUISITION") {
      //

      const newApprovalGroup: ApprovalGroup[] = [];
      const newApprovalDetail: ApprovalDetail[] = [];

      // reconstruct approval list
      {
        for (const item of items) {
          const newApprovals: Approval[] = [];
          for (const app of item.approvals) {
            if (UserRole.some((x) => x === app.currentUserInPosition?.position?.role)) {
              newApprovalDetail.push({
                as: app.as,
                date: app.date,
                signer: app.signer ? app.signer.name : "",
                status: app.status,
                users: app.users ? app.users : [],
                position: app.position ? app.position : null,
                currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
                sequence: item.sequence,
              });
            } else {
              newApprovals.push(app);
            }
          }
          newApprovalGroup.push({
            approvals: newApprovals,
            sequence: item.sequence,
          });
        }
      }

      const result: ApprovalHeader[] = SubDocumentRequisition.map((subDocumentType) => {
        return {
          subDocumentType,
          approvals: [
            ...newApprovalDetail,
            ...newApprovalGroup.flatMap((it) =>
              it.approvals
                .filter((x) => x.subDocumentType === subDocumentType)
                .map((app) => ({
                  as: app.as,
                  date: app.date,
                  signer: app.signer ? app.signer.name : "",
                  status: app.status,
                  users: app.users ? app.users : [],
                  position: app.position ? app.position : null,
                  currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
                  sequence: it.sequence,
                }))
            ),
          ].sort((a, b) => a.sequence! - b.sequence!),
        };
      });

      const filteredByRuleResult: ApprovalHeader[] = result.map((header) => ({
        subDocumentType: header.subDocumentType,
        approvals: header.approvals
          .filter((app) => !(header.subDocumentType === "INSURANCE_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(header.subDocumentType === "HSE_RISK_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(header.subDocumentType === "DA_JUSTIFICATION" && !!app.currentUserInPosition && !docRule.isDaJustification))
          .filter(
            (app) =>
              !(
                header.subDocumentType === "OWNER_ESTIMATION" &&
                ((app.currentUserInPosition?.position?.role === "GM" && req.value <= maxOEValue) ||
                  (app.currentUserInPosition?.position?.role === "SMVP" && req.value <= minOEValue))
              )
          )
          .filter(
            (app) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                app.currentUserInPosition?.position?.role === "GM" &&
                req.value <= maxRQValue
              )
          )
          .filter(
            (app) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                (app.currentUserInPosition?.position?.role === "SMVP" || app.currentUserInPosition?.position?.role === "GM") &&
                req.value <= minRQValue
              )
          )
          .filter(
            (app, index) =>
              !(
                header.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                index !== 0 &&
                app.currentUserInPosition &&
                req.commodity === "GOODS" &&
                req.contractTypeEngagement === "FIRM_COMMITMENT"
              )
          )
          .filter((app) => 
            !(
              header.subDocumentType !== "OWNER_ESTIMATION" && 
              app.currentUserInPosition?.position?.role === "MGR" && 
              req.budgetOwners.length > 0 &&
              listUserBudgetOwners.some((user) => user.id === app.currentUserInPosition?.id)
            )
          ),
      }));

      return {
        items: filteredByRuleResult,
        count: filteredByRuleResult.length,
      };
    }

    return {
      items: [
        {
          subDocumentType: null,
          approvals: items.flatMap((it) =>
            it.approvals.map((app) => ({
              as: app.as,
              date: app.date,
              signer: app.signer ? app.signer.name : "",
              status: app.status,
              users: app.users ? app.users : [],
              position: app.position ? app.position : null,
              currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
              sequence: it.sequence,
            }))
          ),
        },
      ],
      count: 1,
    };
  },
};

type ApprovalHeader = {
  subDocumentType: TypeOf<typeof SubDocumentRequisition> | null;
  approvals: ApprovalDetail[];
};

type ApprovalDetail = {
  as?: string | null;
  date?: Date | null;
  signer?: string | null;
  status?: TypeOf<typeof ApprovalStatus>;
  users?: User[];
  position?: Position | null;
  currentUserInPosition?: User | null;
  sequence?: number;
};
