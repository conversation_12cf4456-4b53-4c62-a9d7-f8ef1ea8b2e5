import { Usecase } from "../../framework/core.js";
import { FindOnePQTemplate, FindOnePQVendor, VendorPhasePQClarification } from "../model/model_prequalification.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findOnePQVendor: FindOnePQVendor;
}

export class Request {
  pqId: string;
  vendorId: string;
}

export class Response {
  pqEvaluation: VendorPhasePQClarification | null;
}

export const pqEvaluationGetOne: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);

    if (!pqTemplate) {
      return { pqEvaluation: null };
    }

    const pqVendor = await o.findOnePQVendor(ctx, req);

    if (!pqVendor || !pqVendor.phaseClarification) {
      return { pqEvaluation: null };
    }

    return { pqEvaluation: pqVendor.phaseClarification };
  },
};