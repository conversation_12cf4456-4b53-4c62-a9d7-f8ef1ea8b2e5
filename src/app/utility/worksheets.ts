import * as XLSX from 'xlsx';
import * as fs from "fs";
import { DataSource, DeepPartial } from 'typeorm';
import { generateID } from '../../framework/helper.js';
import { BusinessFieldLicense } from '../gatewayimpl/table_business_field_license.js';
import { BusinessFieldLicense as IBusinessFieldLicense } from '../model/model_business_field_license.js';


export const readWorksheets = async (ds: DataSource) => {
    const chunkSize = 20
    const filePath = '././docs/bidang_dan_sub_bidang_izin_usaha.xlsx';

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
        console.error('File does not exist:', filePath);
        return;
    }

    try {
        const fileBuffer = fs.readFileSync(filePath);

        // Read the workbook from the buffer
        const workbook = XLSX.read(fileBuffer, { type: 'buffer' });

        console.log(`Processing rows in chunks of ${chunkSize}...`);

        for (const sheetName of workbook.SheetNames) {

            const sheet = workbook.Sheets[sheetName];
            const maxRows = getMaxRows(sheet);
            let startRow = getSheetStartRow(sheetName);

            console.log(`Total rows in the sheet ${sheetName} : ${startRow} - ${maxRows}`);

            // Stream-like processing: read rows in chunks
            for (startRow; startRow < maxRows; startRow += chunkSize) {
                const chunk: DeepPartial<IBusinessFieldLicense>[] = readChunk(sheetName, sheet, startRow, chunkSize);

                // console.log(`Processing chunk starting at row ${startRow}`);

                try {
                    await saveOrUpdateWorksheet(ds, chunk as DeepPartial<BusinessFieldLicense>[]);

                    // console.log(`Chunk starting at row ${startRow} saved to the database.`);
                    // console.dir(chunk, { depth: null });
                } catch (error) {
                    console.error(`Error saving chunk starting at row ${startRow}`, error);
                }
            }

            // console.log('Completed processing all rows from sheet: ' + sheetName);
        }

        console.log('Worksheet processing done.');
    } catch (error) {
        console.error('Error reading file:', error);
    }
}

const saveOrUpdateWorksheet = async (ds: DataSource, rows: DeepPartial<BusinessFieldLicense>[]) => {
    // 
    if (rows.length === 0) return; // Avoid unnecessary operations

    // Extract all column names dynamically
    const columns = Object.keys(rows[0]);

    await ds.transaction(async (manager) => {
        const repository = manager.getRepository(BusinessFieldLicense);

        const existsRows = await repository
            .createQueryBuilder("business_field_license")
            .where("business_field_license.documentName IN (:...documentNames) AND business_field_license.categoryCode IN (:...categoryCodes)", {
                documentNames: rows.map((row) => row.documentName),
                categoryCodes: rows.map((row) => row.categoryCode),
            })
            .getMany();

        const toUpdate = rows.filter((row) =>
            existsRows.some(
                (existsRow) => existsRow.documentName === row.documentName && existsRow.categoryCode === row.categoryCode
            )
        );

        const toInsert = rows.filter((row) =>
            !existsRows.some(
                (existsRow) => existsRow.documentName === row.documentName && existsRow.categoryCode === row.categoryCode
            )
        );

        if (toInsert.length > 0) {
            await repository.insert(toInsert);
        }

        if (toUpdate.length > 0) {
            for (const row of toUpdate) {
                const { id, ...updateRow } = row;
                await repository.update(
                    { documentName: row.documentName, categoryCode: row.categoryCode! },
                    { ...updateRow }
                );
            }
        }
    });
}

// SIUJK LPJK No 4 2017 & SIUJK Permen 8 2011
const readChunk = (sheetName: string, sheet: XLSX.WorkSheet, startRow: number, chunkSize: number): DeepPartial<IBusinessFieldLicense>[] => {
    const chunk: DeepPartial<IBusinessFieldLicense>[] = [];
    const range = XLSX.utils.decode_range(sheet['!ref']!);

    for (let row = startRow; row < startRow + chunkSize && row <= range.e.r; row++) {
        const newId = generateID(16);

        let rowData: DeepPartial<IBusinessFieldLicense> | null = null;

        if (["SIUP-IUT-IUI-PROP KBLI 2009", "SIUP-IUT-IUI-PROP KBLI 2015"].includes(sheetName)) {
            rowData = get_SIUP_KBLI(sheetName, sheet, row, newId);
        } else if (["SIUJK LPJK No 4 2017", "SIUJK Permen 8 2011"].includes(sheetName)) {
            rowData = get_SIUJK(sheetName, sheet, row, newId);
        } else if (["SIUPAL - SIOPSUS", "Salvage - Bawah Air", "SIUPJPT"].includes(sheetName)) {
            rowData = get_SIUPAL_SIOPSUS_SALVAGE_SIUPJPT(sheetName, sheet, row, newId);
        } else if (sheetName === "SIUJPTL") {
            rowData = get_SIUJPTL(sheetName, sheet, row, newId);
        } else if (sheetName === "SIUJS") {
            rowData = get_SIUJS(sheetName, sheet, row, newId);
        } else if (sheetName === "Usaha Hilir") {
            rowData = get_USAHA_HILIR(sheetName, sheet, row, newId);
        } else if (sheetName === "TDUP") {
            rowData = get_TDUP(sheetName, sheet, row, newId);
        } else {
            continue;
        }

        if (rowData && rowData.categoryCode !== null) {
            chunk.push(rowData);
        }
    }

    return chunk;
};

// SIUP-IUT-IUI-PROP KBLI 2009 & SIUP-IUT-IUI-PROP KBLI 2015
const get_SIUP_KBLI = (sheetName: string, sheet: XLSX.WorkSheet, row: number, newId: string): DeepPartial<IBusinessFieldLicense> => {
    const categoryCode: string = readCellValue(sheet, row, 1) !== readCellValue(sheet, row, 2) ?
        readCellValue(sheet, row, 1) + readCellValue(sheet, row, 2) : readCellValue(sheet, row, 2);

    const rowData: DeepPartial<IBusinessFieldLicense> = {
        id: newId,
        documentName: sheetName,
        categoryCode: categoryCode,
        clasification: readCellValue(sheet, row, 3),
    };

    return rowData;
}

// SIUJK LPJK No 4 2017 & SIUJK Permen 8 2011
const get_SIUJK = (sheetName: string, sheet: XLSX.WorkSheet, row: number, newId: string): DeepPartial<IBusinessFieldLicense> => {
    const rowData: DeepPartial<IBusinessFieldLicense> = {
        id: newId,
        documentName: sheetName,
        categoryCode: readCellValue(sheet, row, 2),
        clasification: readCellValue(sheet, row, 3),
        // subClasification: readCellValue(sheet, row, 3),
    };

    return rowData;
}

// SIUPAL - SIOPSUS & Salvage - Bawah Air & SIUPJPT
const get_SIUPAL_SIOPSUS_SALVAGE_SIUPJPT = (sheetName: string, sheet: XLSX.WorkSheet, row: number, newId: string): DeepPartial<IBusinessFieldLicense> => {
    const rowData: DeepPartial<IBusinessFieldLicense> = {
        id: newId,
        documentName: sheetName,
        categoryCode: readCellValue(sheet, row, 0),
        clasification: readCellValue(sheet, row, 1),
    };

    return rowData;
}

// SIUJPTL
const get_SIUJPTL = (sheetName: string, sheet: XLSX.WorkSheet, row: number, newId: string): DeepPartial<IBusinessFieldLicense> => {
    const rowData: DeepPartial<IBusinessFieldLicense> = {
        id: newId,
        documentName: sheetName,
        categoryCode: readCellValue(sheet, row, 4) !== readCellValue(sheet, row, 3) ? readCellValue(sheet, row, 3) + readCellValue(sheet, row, 4) : readCellValue(sheet, row, 4),
        clasification: readCellValue(sheet, row, 2),
        // subClasification: readCellValue(sheet, row, 2),
    };

    return rowData;
}

// SIUJS
const get_SIUJS = (sheetName: string, sheet: XLSX.WorkSheet, row: number, newId: string): DeepPartial<IBusinessFieldLicense> => {
    const rowData: DeepPartial<IBusinessFieldLicense> = {
        id: newId,
        documentName: sheetName,
        categoryCode: readCellValue(sheet, row, 0),
        clasification: readCellValue(sheet, row, 1),
    };

    return rowData;
}

// TDUP
const get_TDUP = (sheetName: string, sheet: XLSX.WorkSheet, row: number, newId: string): DeepPartial<IBusinessFieldLicense> => {
    const rowData: DeepPartial<IBusinessFieldLicense> = {
        id: newId,
        documentName: sheetName,
        categoryCode: readCellValue(sheet, row, 1) + readCellValue(sheet, row, 2),
        clasification: readCellValue(sheet, row, 0),
    };

    return rowData;
}

// Usaha Hilir
const get_USAHA_HILIR = (sheetName: string, sheet: XLSX.WorkSheet, row: number, newId: string): DeepPartial<IBusinessFieldLicense> => {
    const rowData: DeepPartial<IBusinessFieldLicense> = {
        id: newId,
        documentName: `${sheetName} - ` + 'Izin Usaha dalam Kegiatan Usaha Hilir Minyak dan Gas Bumi',
        categoryCode: readCellValue(sheet, row, 1),
        clasification: readCellValue(sheet, row, 2),
        // subClasification: readCellValue(sheet, row, 3),
    };

    return rowData;
}

const getMaxRows = (sheet: XLSX.WorkSheet) => {
    // Get the range of cells in the sheet (e.g., A1:C10)
    const range = XLSX.utils.decode_range(sheet['!ref']!);

    // The last row in the sheet is the last row in the range
    return range.e.r + 1; // Adding 1 as rows are zero-indexed
}

const getSheetStartRow = (sheetName: string): number => {
    if (!sheetNamesList().includes(sheetName)) {
        throw new Error(`sheetName not found: ${sheetName}`);
    }

    return sheetNamesAndStartRow[sheetName];
}

const normalizeString = (str: string | null): string | null => {
    if (!str) return null;
    return str.trim().replace(/\s+/g, ' ').toUpperCase();
};

const readCellValue = (sheet: XLSX.WorkSheet, row: number, column: number) => {
    const value = sheet[XLSX.utils.encode_cell({ r: row, c: column })]?.v || null;

    if (value && typeof value === "string") {
        return normalizeString(value);
    }

    return value;
}

const printSheetToJson = (sheet: XLSX.WorkSheet) => {
    const jsonDataWithAddresses: Record<string, any>[] = [];
    Object.keys(sheet).forEach((cellAddress) => {
        // Skip sheet properties like "!ref"
        if (cellAddress[0] === '!') return;

        // Retrieve cell value and add to JSON with address
        const cellValue = sheet[cellAddress].v;
        jsonDataWithAddresses.push({ cellAddress, cellValue });
    });

    console.dir(jsonDataWithAddresses, { depth: null });
}

export const sheetNamesList = () => {
    return Object.keys(sheetNamesAndStartRow);
}

const sheetNamesAndStartRow: { [key: string]: number } = {
    'SIUP-IUT-IUI-PROP KBLI 2009': 2,
    'SIUP-IUT-IUI-PROP KBLI 2015': 2,
    'SIUJK LPJK No 4 2017': 4,
    'SIUJK Permen 8 2011': 4,
    'SIUPAL - SIOPSUS': 0,
    'SIUJPTL': 2,
    'SIUJS': 1,
    'Salvage - Bawah Air': 0,
    'SIUPJPT': 0,
    'TDUP': 1,
    'Usaha Hilir': 2,
}