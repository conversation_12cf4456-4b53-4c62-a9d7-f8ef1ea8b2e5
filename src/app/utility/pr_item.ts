import { DataSource } from "typeorm";
import pg from "pg";
import { PrItem as IPrItem } from "../gatewayimpl/table_pr_item.js";
import { PrItem } from "../model/model_pr_item.js";


export const importPrItem = async (ds: DataSource) => {
  // 
  const pool = new pg.Pool({
    host: process.env.DATABASE_RAW_HOST,
    port: Number(process.env.DATABASE_RAW_PORT),
    user: process.env.DATABASE_RAW_USER,
    password: process.env.DATABASE_RAW_PASS,
    database: process.env.DATABASE_PR_NAME, // pr_item db
    connectionTimeoutMillis: 10000,
  });

  try {
    // 
    await ds.getRepository(IPrItem).clear();

    console.log("Trying to connect pr_item database");

    const query = `SELECT * FROM pr;`;

    // Get a connection from the pool
    const conn = await pool.connect();
    const result = await conn.query(query);

    if (!result || result.rowCount === 0) {
      console.log("Sync pr_item done, no pr_item found.");
      return;
    }

    console.log("Sync pr_item data starting.");

    let prItems: PrItem[] = [];

    for (const row of result.rows) {
      let prItem: PrItem = {
        id: row.id,
        prnumber: row.prNumber,
        item: row.item,
        material: row.material,
        materialdesc: row.materialDesc,
        syncAt: new Date(),
      }
      prItems.push(prItem);
    }

    await ds.getRepository(IPrItem).save(prItems);

    console.log(`Synced pr_item: ${result.rows.length} rows.`);
    console.log("Sync pr_item done.");

  } catch (error) {
    console.log(`${error}`);
    throw new Error(`${error}`);
  }
}

