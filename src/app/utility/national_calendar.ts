import { DataSource } from "typeorm";
import { Calendar, FindCalendarFilter } from "../model/model_calendar.js";
import { Calendar as ICalendar } from "../gatewayimpl/table_calendar.js";
import { generateID } from "../../framework/helper.js";

export async function getIndonesianHolidays(ds: DataSource, year: number) {
    // 
    // documentation: https://calendarific.com/api-documentation
    const apiKey = "H4eG73FuAAscfqgSPJcQsMHoHVhhPRnL";

    try {
        console.log("Fetching holidays year", year);

        const response = await fetch(`https://calendarific.com/api/v2/holidays?api_key=${apiKey}&country=ID&year=${year}`);
        const data = await response.json();

        if (!data || data.meta.code !== 200) {
            throw new Error(`Failed to fetch holidays year ${year}`);
        }

        let holidays: Calendar[] = [];

        for (const holiday of data.response.holidays) {
            holidays.push({
                id: generateID(16),
                name: holiday.name,
                description: holiday.description,
                type: holiday.primary_type,
                date: normalizeDate(holiday.date.iso),
                year: holiday.date.datetime.year,
            });
        }

        const query = `SELECT date FROM calendar WHERE year = ${year}`;
        const existing: Calendar[] = await ds.getRepository(ICalendar).query(query);

        const existingDates = new Set(existing.map((h) => h.date!.toISOString().split("T")[0]));

        // filter duplicates
        const newHolidays = holidays.filter((holiday) => !existingDates.has(holiday.date!.toISOString().split("T")[0]));

        if (newHolidays.length > 0) {
            await ds.getRepository(ICalendar).save(newHolidays);
        }

        console.log(`Synced done, ${newHolidays.length} new holidays for year ${year}`);
    } catch (error) {
        throw new Error("Failed to fetch Indonesian holidays: " + error);
    }
}

const normalizeDate = (dateStr: string): Date => {
    return new Date(dateStr.split("T")[0]); // ensures only YYYY-MM-DD format
};