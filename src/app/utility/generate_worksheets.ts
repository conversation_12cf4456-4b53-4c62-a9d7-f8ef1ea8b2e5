import * as fs from "fs";
import path, { dirname } from 'path';
import { DataSource } from 'typeorm';
import { fileURLToPath } from 'url';
import * as XLSX from 'xlsx';
import { Context } from '../../framework/core.js';
import { implFindAllDepartment } from '../gatewayimpl/impl_department.js';
import { ProcPlanDetail } from '../gatewayimpl/table_proc_plan.js';
import { collectDepartments, Department } from '../model/model_department.js';
import { User } from '../model/model_user.js';
import { ExcelAPPDownload, excelAPPTemplateHeader, ExcelRequisitionDownload, excelRequisitionTemplateHeader, formatNumber, TenderMethod, TypeOf, ucwords, WorkProgramAndBudgetSchedule, WorkProgramReference } from '../model/vo.js';
import { implFindAllUser } from "../gatewayimpl/impl_user.js";
import { implFindAllApprovalTemplateGroup } from "../gatewayimpl/impl_approval_template_group.js";
import { formatDate } from "./helper.js";
import { Requisition } from "../gatewayimpl/table_requisition.js";
import { ApprovalGroup } from "../gatewayimpl/table_approval.js";
import { getCurrentUserPICNames } from "../model/model_approval.js";


export const downloadExcelAPP = async (
    ds: DataSource,
    ctx: Context,
    userLogin: User,
    year: number,
    departmentId: string = ""
) => {
    // 
    const repository = ds.getRepository(ProcPlanDetail);
    const limit: number = 9999;
    const currentYear: number = !year ? new Date().getFullYear() : year;

    try {
        // 
        const implFindUser_ = implFindAllUser(ds);
        const deptSet = new Set<string>();

        const findApprovalTemplateGroup_ = implFindAllApprovalTemplateGroup(ds);
        const [atgs] = await findApprovalTemplateGroup_(ctx, { documentType: "PROC_PLAN_APP", size: limit });

        const implFindAllDepartment_ = implFindAllDepartment(ds);
        const firstApprovalTemplateGroup = atgs[0].approvalTemplates.find((at) => at.users?.find((x) => x.id === userLogin.id));
        let departments: Department | Department[];

        if (!!firstApprovalTemplateGroup && departmentId === "") {
            [departments] = await implFindAllDepartment_(ctx, { size: limit });
        } else if (departmentId !== "") {
            [departments] = await implFindAllDepartment_(ctx, { ids: [departmentId], size: limit });
        } else {
            await collectDepartments(ctx, implFindUser_, userLogin, deptSet);
            [departments] = deptSet.size > 0 ? await implFindAllDepartment_(ctx, { ids: [...deptSet], size: limit }) : [[]];
        }

        const queryBuilder = repository.createQueryBuilder("proc_plan_detail");

        // left join proc_plan_header
        queryBuilder.leftJoin(
            "proc_plan_header",
            "proc_plan_header",
            "proc_plan_detail.procPlanHeaderId = proc_plan_header.id"
        )
            .addSelect(["proc_plan_header.status"]);

        // left join department
        queryBuilder.leftJoin(
            "department",
            "department",
            "proc_plan_detail.departmentId = department.id"
        )
            .addSelect(["department.name"]);

        // left join section
        queryBuilder.leftJoin(
            "section",
            "section",
            "proc_plan_detail.sectionId = section.id"
        )
            .addSelect(["section.name"]);

        queryBuilder.where("proc_plan_detail.procPlanType = :procPlanType", { procPlanType: "APP" });

        queryBuilder.andWhere("proc_plan_detail.departmentId IN (:...departmentIds)", { departmentIds: departments.map((dep) => dep.id) })

        queryBuilder.andWhere("proc_plan_detail.year = :year", { year: currentYear });

        queryBuilder.limit(limit);

        const ppds = await queryBuilder.getRawMany();
        const ppdsResult: ExcelAPPDownload[] = await transformToExcelAPPData(ppds);

        const departmentName = departmentId !== "" ? ppdsResult[0].departmentName : "";
        const department = departmentName !== "" ? `_${departmentName}`.replace(', ', '_').replace(' & ', '_&_').replace(' ', '_') : "";
        const filename = `${Number(year)} APP${department}.xlsx`.replace(' ', '_');
        const sheetname = `${Number(year)} APP${department}`.replace(' ', '_');
        let reformatedData: ExcelAPPDownload[] = [];

        for (const ppds of ppdsResult) {
            reformatedData.push(await convertObjectKeys(ppds, excelAPPTemplateHeader));
        };

        const excelFilePath = await writeExcel(reformatedData, filename, sheetname);

        return excelFilePath;

    } catch (error) {
        console.log(error);
        throw new Error("Generate worksheets error");
    }
}

export const downloadExcelRequisition = async (
    ds: DataSource,
    ctx: Context,
    userLogin: User,
    year: number,
    departmentId: string = ""
) => {
    // 
    const repository = ds.getRepository(Requisition);
    const limit: number = 9999;
    const currentYear: number = !year ? new Date().getFullYear() : Number(year);

    try {
        // 
        const queryBuilder = repository.createQueryBuilder("requisition");

        // left join department
        queryBuilder.leftJoin(
            "department",
            "department",
            "requisition.departmentId = department.id"
        )
            .addSelect(["department.name"]);

        queryBuilder.where("requisition.year = :year", { year: currentYear });

        queryBuilder.andWhere("requisition.status != :status", { status: "DRAFT" });

        if (departmentId) {
            queryBuilder.andWhere("requisition.departmentId = :departmentId", { departmentId: departmentId })
        }

        queryBuilder.limit(limit);

        let rqs = await queryBuilder.getRawMany();

        for (const rq of rqs) {
            const [approvalGroup, count] = await ds.getRepository(ApprovalGroup).findAndCount({
                where: { documentId: rq.requisition_id, documentType: "REQUISITION" },
                order: { sequence: "ASC" }
            });

            if (count > 0) {
                const lastApprovalSequence = approvalGroup[count - 1].sequence;
                const approvalRelease = approvalGroup.find((app) => app.sequence === lastApprovalSequence);
                const approvalSubmission = approvalGroup.find((app) => app.sequence === lastApprovalSequence - 1);
                
                rq.releaseDate = approvalRelease?.approvals[0].date ? new Date(approvalRelease?.approvals[0].date) : '-'; // Release Date : tanggal diassign oleh head procurement
                rq.submissionDate = approvalSubmission?.approvals[0].date ? new Date(approvalSubmission?.approvals[0].date) : '-'; // Submit Data : tanggal approver sebelum head procurement step
                rq.pic = getCurrentUserPICNames(approvalGroup); // PIC : current PIC user name
            }
        }

        const rqsResult: ExcelRequisitionDownload[] = await transformToExcelRequisitionData(rqs);

        const departmentName = departmentId !== "" ? rqsResult[0].departmentName : "";
        const department = departmentName !== "" ? `_${departmentName}`.replace(', ', '_').replace(' & ', '_&_').replace(' ', '_') : "";
        const filename = `${Number(year)} REQUISITION${department}.xlsx`.replace(' ', '_');
        const sheetname = `${Number(year)} REQUISITION${department}`.replace(' ', '_');
        let reformatedData: ExcelRequisitionDownload[] = [];

        for (const rq of rqsResult) {
            reformatedData.push(await convertObjectKeys(rq, excelRequisitionTemplateHeader));
        };

        const excelFilePath = await writeExcel(reformatedData, filename, sheetname);

        return excelFilePath;

    } catch (error) {
        console.log(error);
        throw new Error("Generate worksheets error");
    }
}

const writeExcel = async (
    data: ExcelAPPDownload[] | ExcelRequisitionDownload[],
    filename: string,
    sheetname: string = "Sheet1"
) => {
    // 
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Create a new workbook and append the worksheet
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetname);

    // Generate XLSX buffer
    const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

    console.log(`Excel ${filename} file written buffered ready to download`);

    return { buffer: buffer, filename: filename };
}

const convertObjectKeys = async (obj: Record<string, any>, keyMap: Record<string, string>) => {
    return Object.fromEntries(
        Object.entries(obj).map(([key, value]) => [
            keyMap[key], // Use the mapped key from keyMap
            value,
        ]).filter(([newKey]) => newKey) // Exclude keys that aren't in keyMap
    );
};

const transformToExcelAPPData = async (rawData: any[]): Promise<ExcelAPPDownload[]> => {
    let excelData: ExcelAPPDownload[] = [];

    for (const data of rawData) {
        // 
        let schedules = data.proc_plan_detail_workProgramAndBudget && data.proc_plan_detail_workProgramAndBudget.length > 0
            ? data.proc_plan_detail_workProgramAndBudget.flatMap((row: any) =>
                row.scheduleLines.map((scheduleLine: any) => scheduleLine.schedule)
            )
            : [];
        let lines = data.proc_plan_detail_workProgramAndBudget && data.proc_plan_detail_workProgramAndBudget.length > 0
            ? data.proc_plan_detail_workProgramAndBudget.flatMap((row: any) =>
                row.scheduleLines.map((scheduleLine: any) => scheduleLine.lines)
            )
            : [];

        let contractDateStart = data.proc_plan_detail_contractDateStart ? formatDate(data.proc_plan_detail_contractDateStart) : '-';
        let contractDateEnd = data.proc_plan_detail_contractDateEnd ? formatDate(data.proc_plan_detail_contractDateEnd) : '-';

        if (data.proc_plan_detail_commodity === "GOODS") {
            contractDateStart = data.proc_plan_detail_poDateIssuance ? formatDate(data.proc_plan_detail_poDateIssuance) : '-';
            contractDateEnd = data.proc_plan_detail_poDateDelivery ? formatDate(data.proc_plan_detail_poDateDelivery) : '-';
        }

        const dat: ExcelAPPDownload = {
            // procPlanDetailId: data.proc_plan_detail_id,
            departmentName: data.department_name,
            sectionName: data.section_name,
            status: data.proc_plan_header_status ? ucwords(data.proc_plan_header_status.replace('_', ' ')) : '-',
            commodity: data.proc_plan_detail_commodity ? ucwords(data.proc_plan_detail_commodity) : '-',
            activityType: data.proc_plan_detail_activityType ? ucwords(data.proc_plan_detail_activityType) : '-',
            procPlanCode: data.proc_plan_detail_procPlanCode ?? '-',
            title: data.proc_plan_detail_title,
            generalScopeOfWork: data.proc_plan_detail_generalScopeOfWork,
            tenderMethod: data.proc_plan_detail_tenderMethod ? ucwords(data.proc_plan_detail_tenderMethod.replace('_', ' ')) : '-',
            requisitionSubmissionDate: data.proc_plan_detail_requisitionSubmissionDate ? formatDate(data.proc_plan_detail_requisitionSubmissionDate) : '-',
            tenderStartDate: data.proc_plan_detail_tenderStartDate ? formatDate(data.proc_plan_detail_tenderStartDate) : '-',
            contractDateStart: contractDateStart,
            contractDateEnd: contractDateEnd,
            durationType: data.proc_plan_detail_durationType,
            durationValue: data.proc_plan_detail_durationValue,
            localContentLevel: data.proc_plan_detail_localContentLevel ? `${data.proc_plan_detail_localContentLevel}%` : '0%',
            currency: data.proc_plan_detail_currency,
            valueEstimation: data.proc_plan_detail_valueEstimation ? formatNumber(data.proc_plan_detail_valueEstimation) : 0,
            estCurrYearExpenditure: data.proc_plan_detail_estCurrYearExpenditure ? formatNumber(data.proc_plan_detail_estCurrYearExpenditure) : 0,
            approvalAnnualBudget: data.proc_plan_detail_approvalAnnualBudget ? formatNumber(data.proc_plan_detail_approvalAnnualBudget) : 0,
            workProgramReferences:
                data.proc_plan_detail_workProgramReferences
                    ? data.proc_plan_detail_workProgramReferences.join(', ')
                    : '-',
            technicalMoMSubjectAndDate:
                data.proc_plan_detail_technicalMoMSubjectAndDate
                    ? data.proc_plan_detail_technicalMoMSubjectAndDate.join(', ')
                    : '-',
            remarks: data.proc_plan_detail_remarks,
            schedule: schedules.length > 0 ? schedules.join(', ') : '-',
            line: lines.length > 0 ? lines.join(', ') : '-',
        }

        excelData.push(dat)
    }

    return excelData.sort((a, b) => a.departmentName.localeCompare(b.departmentName));
}

const transformToExcelRequisitionData = async (rawData: any[]): Promise<ExcelRequisitionDownload[]> => {
    let excelData: ExcelRequisitionDownload[] = [];
    for (const data of rawData) {
        const dat: ExcelRequisitionDownload = {
            // requisitionId: data.requisition_id,
            departmentName: data.department_name,
            commodity: data.requisition_commodity,
            procPlanDetailCode: data.requisition_procPlanDetailCode ?? '-',
            tenderCode: data.requisition_tenderCode ?? '-',
            title: data.requisition_title,
            requisitionSubmission: data.submissionDate,
            requisitionRelease: data.releaseDate,
            // Release Date : setelah di assign oleh procurement (awal, irena, fajri)
            // Submit Data : tanggal approver sebelum procurement
            currency: data.requisition_currency,
            value: data.requisition_value ? formatNumber(data.requisition_value) : 0,
            localContentLevel: data.requisition_localContentLevel,
            hseAssessment: data.requisition_hseAssessment,
            status: data.requisition_status ? ucwords(data.requisition_status.replace('_', ' ')) : '-',
            pic: data.pic,
        }

        excelData.push(dat)
    }

    return excelData.sort((a, b) => a.departmentName.localeCompare(b.departmentName));
}