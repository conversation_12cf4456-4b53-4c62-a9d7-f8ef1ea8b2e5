import fs from "fs";
import mime from "mime";
import path, { dirname } from "path";
import { fileURLToPath } from "url";
import { DocumentTemplate, SharepointFile, TypeOf } from "../model/vo.js";

const fileuploadConfig = {
  rootPath: "../../../public/uploads",
};

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export type FileDownloadRequest = {
  documentType: TypeOf<typeof DocumentTemplate>;
  year: number;
  departmentId?: string;
};

export async function uploadFileToPublicFolder(fileBuffer: Buffer, fileName: string, folderPath: string = ""): Promise<void> {
  const fullFolderPath = path.resolve(__dirname, fileuploadConfig.rootPath, folderPath);
  const filePath = path.join(fullFolderPath, fileName);

  try {
    await fs.promises.mkdir(fullFolderPath, { recursive: true });
    await fs.promises.writeFile(filePath, new Uint8Array(fileBuffer));
  } catch (error) {
    throw new Error(`Error uploading file: ${(error as Error).message}`);
  }
}

export async function deleteFileFromPublicFolder(fileName: string): Promise<void> {
  const filePath = path.resolve(__dirname, fileuploadConfig.rootPath, fileName);

  try {
    const stats = await fs.promises.stat(filePath);

    if (stats.isDirectory()) {
      await fs.promises.rm(filePath, { recursive: true, force: true });
    } else {
      await fs.promises.unlink(filePath);
    }
  } catch (error) {
    throw new Error(`Error deleting file: ${(error as Error).message}`);
  }
}

export async function getFileFromPublicFolder(fileName: string): Promise<Response> {
  const filePath = path.resolve(__dirname, fileuploadConfig.rootPath, fileName);

  try {
    await fs.promises.access(filePath, fs.constants.F_OK);
    const contentType = mime.getType(filePath) || "application/octet-stream";

    const fileStream = fs.createReadStream(filePath);

    return new Response(fileStream as any, {
      status: 200,
      statusText: "OK",
      headers: {
        "Content-Type": contentType,
      },
    });
  } catch (error) {
    return new Response(`File not found: ${fileName}`, {
      status: 404,
      statusText: "Not Found",
    });
  }
}

export function markHistoryFiles(files: SharepointFile[]) {
  if (!files) return files;
  return (files ?? []).map((file) => ({
    ...file,
    history: true,
  }));
}
