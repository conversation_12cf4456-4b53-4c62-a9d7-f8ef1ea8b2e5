import { DataSource } from "typeorm";
import { createContext } from "../../framework/core.js";
import { implFindAllUser } from "../gatewayimpl/impl_user.js";
import jwt from "jsonwebtoken";

export const login = async (ds: DataSource) => {
  const findUser = implFindAllUser(ds);

  const emails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "G<PERSON><PERSON><EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];

  const [users] = await findUser(createContext(), { emails });

  users.forEach(async (user) => {
    const payload = { data: user };
    const secretKey = process.env.TOKEN_SECRET_KEY as string;
    const expiration = { expiresIn: "999h" };
    const token = jwt.sign(payload, secretKey, expiration);

    console.log(`# @email=${user.email}`);
    console.log(`# @token=${token}\n\n`);
  });
};
