import { Readable } from "stream";
import express from "express";

const sharepointConfig = {
  tenantId: "01ae35c2-891c-4378-b7af-d9dd99659458",
  clientId: "7f3de15c-5001-4bb1-8b55-292f541c3dde",
  clientSecret: "****************************************",
  tenantName: "officehcml",
  siteName: "ProcurementSharepointDocument",
};

export type TokenResponse = {
  token: string;
  exp: number;
  siteId: string;
  driveId: string | null;
};

type TokenData = {
  access_token: string;
  expires_in: number;
};

type SiteDriveId = {
  siteId: string;
  driveId: string | null;
};

type SiteData = {
  id: string;
};

type DriveData = {
  value: Array<{ id: string; name: string }>;
};

export const tokenCookie = async (req: express.Request, res: express.Response): Promise<TokenResponse> => {
  if (req.cookies.sharepoint) return req.cookies.sharepoint;

  const token = await fetchAccessToken();

  res.cookie("sharepoint", token, {
    secure: true,
    maxAge: 3000 * 1000,
  });

  return token;
};

async function fetchAccessToken(): Promise<TokenResponse> {
  const tokenResponse = await fetch(`https://login.microsoftonline.com/${sharepointConfig.tenantId}/oauth2/v2.0/token`, {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: new URLSearchParams({
      grant_type: "client_credentials",
      client_id: sharepointConfig.clientId,
      client_secret: sharepointConfig.clientSecret,
      scope: "https://graph.microsoft.com/.default",
    }),
  });

  if (!tokenResponse.ok) {
    const errorDetails = await tokenResponse.text();
    throw new Error(`Failed to fetch access token: ${errorDetails}`);
  }

  const tokenData = (await tokenResponse.json()) as TokenData;

  const siteDrive = await fetchSiteAndDriveIds(tokenData.access_token);

  const response: TokenResponse = {
    token: tokenData.access_token,
    exp: tokenData.expires_in,
    siteId: siteDrive.siteId,
    driveId: siteDrive.driveId,
  };

  return response;
}

async function fetchSiteAndDriveIds(token: string): Promise<SiteDriveId> {
  const siteResponse = await fetch(`https://graph.microsoft.com/v1.0/sites/${sharepointConfig.tenantName}.sharepoint.com:/sites/${sharepointConfig.siteName}`, {
    headers: { Authorization: `Bearer ${token}` },
  });

  if (!siteResponse.ok) {
    const errorDetails = await siteResponse.text();
    throw new Error(`Failed to fetch siteId: ${errorDetails}`);
  }

  const siteData = (await siteResponse.json()) as SiteData;

  const driveResponse = await fetch(`https://graph.microsoft.com/v1.0/sites/${siteData.id}/drives`, {
    headers: { Authorization: `Bearer ${token}` },
  });

  if (!driveResponse.ok) {
    const errorDetails = await driveResponse.text();
    throw new Error(`Failed to fetch driveId: ${errorDetails}`);
  }

  const driveData = (await driveResponse.json()) as DriveData;

  const response: SiteDriveId = {
    siteId: siteData.id,
    driveId: driveData.value.find((drive) => drive.name === "Documents")?.id || null,
  };

  return response;
}

export async function uploadFileToSharePoint(fileBuffer: Buffer, fileName: string, folderPath: string, access: TokenResponse): Promise<void> {
  const uploadUrl = `https://graph.microsoft.com/v1.0/sites/${access.siteId}/drives/${access.driveId}/root:/${folderPath}/${fileName}:/content`;

  const response = await fetch(uploadUrl, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${access.token}`,
      "Content-Type": "application/octet-stream",
    },
    body: fileBuffer,
  });

  if (!response.ok) {
    const errorDetails = await response.text();
    console.log(errorDetails);
    throw new Error(`Error uploading file: ${response.statusText} - ${errorDetails}`);
  }
}

export async function deleteFileFromSharePoint(filePath: string, access: TokenResponse): Promise<void> {
  const deleteUrl = `https://graph.microsoft.com/v1.0/sites/${access.siteId}/drives/${access.driveId}/root:/${filePath}`;

  const response = await fetch(deleteUrl, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${access.token}`,
      Accept: "application/json",
    },
  });

  if (!response.ok) {
    const errorDetails = await response.text();
    console.log(errorDetails);
    throw new Error(`Error deleting file: ${response.statusText} - ${errorDetails}`);
  }
}

export async function getFileFromSharePoint(filePath: string, access: TokenResponse): Promise<Response> {
  const fileUrl = `https://graph.microsoft.com/v1.0/sites/${access.siteId}/drives/${access.driveId}/root:/${filePath}:/content`;

  const response = await fetch(fileUrl, {
    headers: {
      Authorization: `Bearer ${access.token}`,
    },
  });

  if (!response.ok) {
    const errorDetails = await response.text();
    console.log(errorDetails);
    return new Response(`File not found: ${filePath}`, {
      status: 404,
      statusText: "Not Found",
    });
  }

  return response;
}

export async function convertReadableStream(readableStream: ReadableStream) {
  const reader = readableStream.getReader();
  return new Readable({
    async read() {
      const { done, value } = await reader.read();
      if (done) {
        this.push(null);
      } else {
        this.push(Buffer.from(value));
      }
    },
  });
}

export function sanitizeFilename(filename: string) {
  const invalidChars = /[~#%&*{}\\/:<>?|"]/g;

  let sanitized = filename.replace(invalidChars, "");
  sanitized = sanitized.trim();
  sanitized = sanitized.replace(/\.+/g, ".");

  if (sanitized.startsWith(".")) {
    sanitized = sanitized.slice(1);
  }
  if (sanitized.endsWith(".")) {
    sanitized = sanitized.slice(0, -1);
  }

  return sanitized;
}
