import { User } from "../model/model_user.js";

export type DateOrString = Date | string;

export const getYear = (year: number, now: DateOrString) => {
  now = getDateOnly(now);
  const nowYear = now.getFullYear();
  if (nowYear !== year) {
    throw new Error(`Invalid year. it must ${now.getFullYear()}`);
  }
  return nowYear;
};

export const getDateOnly = (x: DateOrString): Date => {
  //
  let date: Date;

  if (x instanceof Date) {
    date = x;
  } else if (typeof x === "string") {
    date = new Date(x);

    if (isNaN(date.getTime())) {
      throw new Error("Invalid date string");
    }
  } else {
    throw new Error("Invalid input type");
  }

  return date;
};

export const formatDateWithSecond = (ds: DateOrString) => {
  //
  const date = getDateOnly(ds);

  const year = date.getFullYear().toString().slice(-2).padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const hour = date.getHours().toString().padStart(2, "0");
  const minute = date.getMinutes().toString().padStart(2, "0");
  const second = date.getSeconds().toString().padStart(2, "0");
  // return year + month + day + hour + minute + second;

  return `${year}${month}${day}${hour}${minute}${second}`;
};

export const formatDate = (date: string): string => {
  const dateResult = new Date(date);
  return dateResult.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "2-digit",
  }).replace(/\./g, ""); // 10 Oct 24
}

export const docTypeToName = (docType: string): string => {
  //
  let name = "";

  if (docType === "PROC_PLAN_APP" || docType === "PROC_PLAN_UPP") {
    // 
    name = "Procurement Plan";
  } else if (docType === "REQUISITION") {
    // 
    name = "Requisition";
  } else {
    // 
    name = "Prequalification"
  }

  return name;
};

export const translateBusinessClassToEn = (businessClass: string): string | null => {
  //
  const enClass: Record<string, string> = {
    "KECIL": "SMALL",
    "SEDANG": "MEDIUM",
    "MENENGAH": "MEDIUM",
    "BESAR": "LARGE",
  }

  const upperCaseClass = businessClass ? businessClass.toUpperCase() : null;

  return upperCaseClass ? enClass[upperCaseClass] : null;
}

export const toUSD = (currency: "IDR" | "USD", value: string | number) => {
  if (!value) return 0;
  if (currency === "IDR") return Number((Number(value) / 10_000).toFixed(0));
  return Number(Number(value).toFixed(0));
};

export const isUserAdmin = (email: string): boolean => {
  // user can manage setting menu
  const adminMails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"];

  return adminMails.includes(email.toUpperCase());
}

export const isUserAdminProcPlan = (user: User): boolean => {
  // user can see all proc plan
  const email = user.email;

  if (!email || email === "" || email === null || email === undefined) return false; 

  // user procurement
  if (
    user.department?.id === "70210000" && 
    user.department.name == "Procurement" &&
    user.section?.isSection
  ) {
    return true;
  }
  
  const adminMails = [
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];

  return adminMails.includes(email.toUpperCase());
}

export const isUserAdminRequisition = (user: User): boolean => {
  // user can see all requisition
  const email = user.email;

  if (!email || email === "" || email === null || email === undefined) return false; 

  const adminMails = [
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>", 
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];

  return adminMails.includes(email.toUpperCase());
}