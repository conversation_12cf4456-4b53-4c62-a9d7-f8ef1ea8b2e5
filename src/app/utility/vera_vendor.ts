import { DataSource } from "typeorm";
import pg from "pg";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";


export const veraLogin = async (
  user: {
    email: string,
    password: string,
  }
): Promise<VendorVera> => {
  // let user = {
  //   email: "<EMAIL>", // dummy
  //   password: "J@karta123",
  // }
  const pool = new pg.Pool({
    host: process.env.DATABASE_RAW_HOST,
    port: Number(process.env.DATABASE_RAW_PORT),
    user: process.env.DATABASE_RAW_USER,
    password: process.env.DATABASE_RAW_PASS,
    database: process.env.DATABASE_VERA_NAME,
    connectionTimeoutMillis: 10000,
  });

  try {
    console.log("Trying to login using email:", user.email);

    const query = `SELECT 
        vendor_account.id AS "accountId",
        vendor_account.name AS "accountName",
        vendor_account.*,
        vendor_info.id AS "infoId",
	      vendor_info.name AS "infoName",
        vendor_info.* 
      FROM vendor_account
        JOIN vendor_info ON vendor_account."vendor_infoId" = vendor_info.id 
      WHERE email = $1 LIMIT 1`;

    const values = [user.email];

    // Get a connection from the pool
    const conn = await pool.connect();

    // Execute the query with the email as the parameter
    const result = await conn.query(query, values);

    if (!result || result.rowCount === 0) {
      throw new Error(`User with email ${user.email} not found.`);
    }

    const vendor = result.rows[0];

    // Compare the provided password with the stored hashed password
    const isPasswordValid = await bcrypt.compare(user.password, vendor.password);

    if (!isPasswordValid) {
      throw new Error(`Invalid password`);
    }

    const vendorResult = transformVendorVera(vendor);

    // Create JWT token payload and options
    // const payload = { data: vendorResult };
    // const expiration = { expiresIn: process.env.TOKEN_EXPIRATION };
    // const token = jwt.sign(payload, String(process.env.TOKEN_SECRET_KEY_VENDOR), expiration);

    console.log("Login successful");

    conn.release();

    return vendorResult;

    // return [
    //   payload.data,
    //   token,
    // ];

  } catch (error) {
    throw new Error(`${error}`);
  }
}

const transformVendorVera = (vendor: any): VendorVera => {
  return {
    accountId: vendor.accountId,
    accountName: vendor.accountName,
    accountEmail: vendor.email,
    accountModule: vendor.module,
    accountAccess: vendor.access,
    infoId: vendor.infoId,
    name: vendor.infoName,
    address: [
      ...[vendor.address1],
      ...(vendor.address2 ? [vendor.address2] : []),
    ],
    zipCode: vendor.zipCode,
    province: vendor.province,
    city: vendor.city,
    country: vendor.country,
    directorName: vendor.directorName,
    directorPosition: vendor.directorPosition,
    directorMail: vendor.directorMail,
    tenderPIC: vendor.tenderPIC,
    tenderMail: vendor.tenderMail,
    tenderPhone: vendor.tenderPhone,
    financePIC: vendor.financePIC,
    financeMail: vendor.financeMail,
    financePhone: vendor.financePhone,
    hsePIC: vendor.hsePIC,
    hseMail: vendor.hseMail,
    hsePhone: vendor.hsePhone,
    spda: vendor.spda,
    spdaExpDate: vendor.spdaExpDate,
    spdaDocument: vendor.spdaDocument,
    interest: vendor.interest,
    interestDocument: vendor.interestDocument,
    npwp: vendor.npwp,
    npwpDocument: vendor.npwpDocument,
    vendorTraining: vendor.vendorTraining,
  }
}

export type VendorVera = {
  accountId: string;
  accountName: string;
  accountEmail: string;
  accountModule: string;
  accountAccess: string;
  infoId: string;
  name: string;
  address: string[];
  zipCode: string;
  province: string;
  city: string;
  country: string;
  directorName: string;
  directorPosition: string;
  directorMail: string;
  tenderPIC: string;
  tenderMail: string;
  tenderPhone: string;
  financePIC: string;
  financeMail: string;
  financePhone: string;
  hsePIC: string;
  hseMail: string;
  hsePhone: string;
  spda: string;
  spdaExpDate: Date;
  spdaDocument: string;
  interest: string;
  interestDocument: string;
  npwp: string;
  npwpDocument: string;
  vendorTraining: string;
}
