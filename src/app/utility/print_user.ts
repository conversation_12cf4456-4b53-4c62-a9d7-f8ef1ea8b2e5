import { DataSource } from "typeorm";
import { User } from "../model/model_user.js";
import { createContext } from "../../framework/core.js";
import { implFindAllUser } from "../gatewayimpl/impl_user.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

type UserNode = {
  user: User;
  level: number;
  members: UserNode[];
};

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const printMember = async (ds: DataSource) => {
  const ctx = createContext();
  const implFindAllUser_ = implFindAllUser(ds);
  const [users] = await implFindAllUser_(ctx, { size: 1000 });

  const usersWithoutContractor = users.filter((user) => user.section?.name?.includes("Contractor") === false);

  printSamePositions(usersWithoutContractor);
  printUndefinedSupervisors(usersWithoutContractor);
  printStrukturUser(usersWithoutContractor);
  printOthersTopPositions(usersWithoutContractor);
};

const printSamePositions = (users: User[]) => {
  const filePath = "/docs/struktur_user/same_positions.txt";
  writeNodesToFile({} as any, filePath, 0, true);
  printNodes({} as any, 0, true);

  const idCount = users.reduce((acc: { [key: string]: number }, user) => {
    acc[user.position?.id!] = (acc[user.position?.id!] || 0) + 1;
    return acc;
  }, {});

  const samePositions = users.filter((user) => idCount[user.position?.id!] > 1).sort((a, b) => (a.position?.id ?? "").localeCompare(b.position?.id ?? ""));

  samePositions
    .map(
      (user) =>
      ({
        user,
        level: 0,
        members: [],
      } as UserNode)
    )
    .forEach((userNode) => {
      writeNodesToFile(userNode, filePath);
      printNodes(userNode);
    });
};

const printUndefinedSupervisors = (users: User[]) => {
  const filePath = "/docs/struktur_user/undefined_supervisors.txt";
  writeNodesToFile({} as any, filePath, 0, true);
  printNodes({} as any, 0, true);

  const undefinedSupervisors = users.filter((user) => users.findIndex((u) => user.supervisorPositionId === u.position?.id) === -1);

  undefinedSupervisors.forEach((user) => {
    const node: UserNode = {
      user,
      level: 0,
      members: [],
    };
    node.members = generateMemberNodes(users, node);
    writeNodesToFile(node, filePath);
    printNodes(node);
  });
};

const printStrukturUser = (users: User[]) => {
  const filePath = "/docs/struktur_user/struktur_user.txt";
  writeNodesToFile({} as any, filePath, 0, true);
  printNodes({} as any, 0, true);

  const kangAn = users.find((user) => user.id === "80219036")!; // kang an
  const rootNode: UserNode = {
    user: kangAn,
    level: 0,
    members: [],
  };

  rootNode.members = generateMemberNodes(users, rootNode);

  writeNodesToFile(rootNode, filePath);
  printNodes(rootNode);
};

const printOthersTopPositions = (users: User[]) => {
  const filePath = "/docs/struktur_user/others_top_positions.txt";
  writeNodesToFile({} as any, filePath, 0, true);
  printNodes({} as any, 0, true);

  const othersTopPositions = users.filter((user) => user.id !== "80219036" && user.supervisorPositionId === user.position?.id); // without kang an

  othersTopPositions.forEach((user) => {
    const node: UserNode = {
      user,
      level: 0,
      members: [],
    };
    node.members = generateMemberNodes(users, node);
    writeNodesToFile(node, filePath);
    printNodes(node);
  });
};

const generateMemberNodes = (users: User[], parentNode: UserNode, visited = new Set<string>()): UserNode[] => {
  visited.add(parentNode.user.id);

  const members = users.filter((user) => user.supervisorPositionId === parentNode.user.position?.id && !visited.has(user.id));

  return members.map((user) => {
    const node: UserNode = {
      user,
      level: parentNode.level + 1,
      members: [],
    };

    node.members = generateMemberNodes(users, node, visited);
    return node;
  });
};

const printNodes = (userNode: UserNode, i = 0, firstCall = false) => {
  if (firstCall) {
    console.log(
      "\n==================================================================================================================================================================================================================================================================\n" +
      "user_id  level and name                                  role   email                                svpos_id pos_id    pos_name                                  sect_id  sect_name                                   dept_id  dept_name\n" +
      "=================================================================================================================================================================================================================================================================="
    );
    return;
  }

  const name = userNode.user.name || "";
  const role = userNode.user.position?.role || "";
  const email = userNode.user.email || "";
  const supervisorPositionId = userNode.user.supervisorPositionId || "";
  const positionId = userNode.user.position?.id || "";
  const positionName = userNode.user.position?.name || "";
  const sectionId = userNode.user.section?.id || "";
  const sectionName = userNode.user.section?.name || "";
  const departmentId = userNode.user.department?.id || "";
  const departmentName = userNode.user.department?.name || "";

  const as = [
    `${userNode.user.id} `,
    " ".repeat(i * 4),
    `${userNode.level.toString().padStart(2, "0")} ${name}`,
    " ".repeat(Math.max(45 - i * 4 - name.length, 0)),

    role,
    " ".repeat(Math.max(7 - role.length, 0)),
    email,
    " ".repeat(Math.max(37 - email.length, 0)),
    supervisorPositionId,
    " ",
    positionId,
    "  ",
    positionName,
    " ".repeat(Math.max(42 - positionName.length, 0)),
    sectionId,
    " ",
    sectionName,
    " ".repeat(Math.max(36 - sectionName.length + sectionId.length, 0)),
    departmentId,
    " ",
    departmentName,
  ];

  let result = as.join("");
  console.log(result);

  for (const member of userNode.members) {
    printNodes(member, i + 1);
  }
};

const writeNodesToFile = (userNode: UserNode, filePath: string, i = 0, firstCall = false) => {
  const fixedFilePath = path.join(__dirname, "../../../", filePath);

  if (firstCall) {
    fs.writeFileSync(
      fixedFilePath,
      "==================================================================================================================================================================================================================================================================\n" +
      "user_id  level and name                                  role   email                                svpos_id pos_id    pos_name                                  sect_id  sect_name                                   dept_id  dept_name\n" +
      "==================================================================================================================================================================================================================================================================\n"
    );
    return;
  }

  const name = userNode.user.name || "";
  const role = userNode.user.position?.role || "";
  const email = userNode.user.email || "";
  const supervisorPositionId = userNode.user.supervisorPositionId || "";
  const positionId = userNode.user.position?.id || "";
  const positionName = userNode.user.position?.name || "";
  const sectionId = userNode.user.section?.id || "";
  const sectionName = userNode.user.section?.name || "";
  const departmentId = userNode.user.department?.id || "";
  const departmentName = userNode.user.department?.name || "";

  const as = [
    `${userNode.user.id} `,
    " ".repeat(i * 4),
    `${userNode.level.toString().padStart(2, "0")} ${name}`,
    " ".repeat(Math.max(45 - i * 4 - name.length, 0)),

    role,
    " ".repeat(Math.max(7 - role.length, 0)),
    email,
    " ".repeat(Math.max(37 - email.length, 0)),
    supervisorPositionId,
    " ",
    positionId,
    "  ",
    positionName,
    " ".repeat(Math.max(42 - positionName.length, 0)),
    sectionId,
    " ",
    sectionName,
    " ".repeat(Math.max(36 - sectionName.length + sectionId.length, 0)),
    departmentId,
    " ",
    departmentName,
  ];

  let result = as.join("");

  fs.appendFileSync(fixedFilePath, result + "\n");

  for (const member of userNode.members) {
    writeNodesToFile(member, filePath, i + 1);
  }
};
