import { DataSource, In } from "typeorm";
import pg from "pg";
import { Position } from "../gatewayimpl/table_position.js";
import { Department } from "../gatewayimpl/table_department.js";
import { Section } from "../gatewayimpl/table_section.js";
import { User } from "../gatewayimpl/table_user.js";
import { Vendor } from "../gatewayimpl/table_vendor.js";
import { getRequestWithContext } from "../../framework/controller_express.js";
import { createContext } from "../../framework/core.js";
import { InitDefaultApprovalTemplateGroups } from "../gatewayimpl/impl_approval_template_group.js";
import { PositionSupervisor } from "../gatewayimpl/table_position_supervisor.js";
import { saveLogger } from "../model/model_log.js";

async function migratePosition(ds: DataSource, pool: pg.Pool) {
  // const queryAllPerson = `SELECT person."positionId" as id, position."name" as name
  //                         FROM person
  //                         LEFT OUTER JOIN position ON person."positionId" = position."id"
  //                         ORDER BY person."positionId"`;

  const queryAllPerson = `SELECT * FROM position`;

  const personResult = await pool.query(queryAllPerson);

  const ps = personResult.rows.map((row) => {
    //

    return {
      id: row.id,
      name: row.name,
      role: getRoleByPositionName(row.name),
    } as Position;
  });

  console.log("POSITION");
  // console.table(ps);
  await ds.getRepository(Position).save(ps);
}

async function migrateDepartment(ds: DataSource, pool: pg.Pool) {
  const queryAllPerson = `SELECT 
                            person."departmentId", 
                            department."name"
                          FROM person
                          LEFT OUTER JOIN department ON person."departmentId" = department."id"
                          ORDER BY person."departmentId"`;

  const personResult = await pool.query(queryAllPerson);

  // let department: any = {}
  //
  // for (const row of personResult.rows) {
  //     department[row.departmentId] = row.name
  // }
  //
  // const dps: any[] = []
  // for (const k in department) {
  //     dps.push({id: k, name: department[k]})
  // }

  const dps: Department[] = personResult.rows.reduce((acc, row) => {
    const existingDepartment = acc.find((d: any) => d.id === row.departmentId);
    if (!existingDepartment) {
      acc.push({ id: row.departmentId, name: row.name, code: getDepartmentsCode(row.departmentId) });
    }
    return acc;
  }, []);

  console.log("DEPARTMENT");
  // console.table(dps);
  await ds.getRepository(Department).save(dps);
}

async function migrateSection(ds: DataSource, pool: pg.Pool) {
  const queryAllPerson = `SELECT 
                            person."organizationId", 
                            organization."name", 
                            department."id"
                          FROM person
                          LEFT OUTER JOIN organization ON person."organizationId" = organization."id"
                          LEFT OUTER JOIN department ON person."departmentId" = department."id"                          
                          ORDER BY person."departmentId"`;

  const personResult = await pool.query(queryAllPerson);

  // let section: any = {}
  // // the same value from row.organizationId can appear multiple times
  // for (const row of personResult.rows) {
  //     section[row.organizationId] = {name: row.name, departmentId: row.id}
  // }
  //
  // const ss: any[] = []
  // for (const k in section) {
  //     ss.push({id: k, name: section[k].name, department: {id: section[k].departmentId}})
  // }

  const excludeSectionIds = ["70091000", "70024000"]; // add contractor organizationIds

  const ss = personResult.rows.reduce((acc, row) => {
    const existingSection = acc.find((s: any) => s.id === row.organizationId);
    if (!existingSection) {
      acc.push({
        id: row.organizationId,
        name: row.name,
        department: {
          id: row.id,
        },
        // isSection: row.organizationId !== row.id && !excludeSectionIds.some((x) => x === row.organizationId), 
        isSection: !row.name.toLowerCase().includes("contractor"),
      });
    }
    return acc;
  }, []);

  console.log("SECTION");
  // console.table(ss);
  await ds.getRepository(Section).save(ss);
}

async function migrateUser(ds: DataSource, pool: pg.Pool) {
  const queryAllPerson = `SELECT 
                            person."id" as id, 
                            person."name" as name, 
                            person."email" as email,
                            person."organizationId" as "sectionId",
                            person."departmentId" as "departmentId",
                            person."positionId" as "positionId",
                            _supervisor."B" as "supervisorPositionId"
                          FROM person
                          LEFT OUTER JOIN _supervisor ON person."positionId" = _supervisor."A"
                          ORDER BY "organizationId"`;

  const personResult = await pool.query(queryAllPerson);

  // const us: any[] = []
  // for (const row of personResult.rows) {
  //     us.push({
  //         id: row.id,
  //         name: row.firstName,
  //         email: row.email,
  //         position: {id: row.positionId},
  //         section: {id: row.organizationId},
  //         supervisorPosition: { id: row.B},
  //     })
  // }

  // const mySet = new Set<string>();

  personResult.rows.forEach(async (row) => {
    // if (mySet.has(row.id)) {
    //   return;
    // }

    // mySet.add(row.id);

    const us = {
      id: row.id,
      name: row.name,
      // email: process.env.APPLICATION_MODE === 'development' && row.email === '' && row.name !== '' ? String(row.name).replace(/ /g, '_') + '@HCML.CO.ID' : row.email,
      email: row.email,
      position: { id: row.positionId },
      section: { id: row.sectionId },
      department: { id: row.departmentId },
      supervisorPositionId: row.supervisorPositionId ? row.supervisorPositionId : row.positionId,
    } as User;

    await ds.getRepository(User).save(us);
  });

  console.log("USER");
  // console.table(us);
}

async function migrateVendor(ds: DataSource, pool: pg.Pool) {
  const queryAllVendor = `SELECT * FROM vendor`;

  const result = await pool.query(queryAllVendor);

  const ps = result.rows.map((row) => {
    //

    return {
      id: row.id,
      name: row.name,
      location: "",
    } as Vendor;
  });

  console.log("VENDOR");
  // console.table(ps);
  await ds.getRepository(Vendor).save(ps);
}

function getDepartmentsCode(id: string): string {
  //
  const departmentNameCodes = [
    { code: "01", id: "********", name: "Budget & Reporting" },
    { code: "02", id: "********", name: "Business Process & Technology" },
    { code: "03", id: "????????", name: "Development" }, //OPS
    { code: "04", id: "********", name: "Drilling & Completion" }, //OPS
    { code: "05", id: "********", name: "Economic, Commercial & Planning" },
    { code: "06", id: "********", name: "Engineering & Constructions" }, //OPS
    { code: "07", id: "********", name: "Finance & Accounting" },
    { code: "08", id: "********", name: "Health, Safety, Security & Environment" },
    { code: "09", id: "********", name: "HR & General Affairs" },
    { code: "10", id: "********", name: "Internal Audit & Compliance" },
    { code: "11", id: "********", name: "Legal" },
    { code: "12", id: "********", name: "Logistics" },
    { code: "13", id: "********", name: "Maintenance" }, // OPS
    { code: "14", id: "********", name: "Marketing" },
    { code: "15", id: "********", name: "MDA-MBH-MDK (3M) & MAC Asset" }, //OPS
    { code: "16", id: "********", name: "Planning, Biddin & Reporting" },
    { code: "17", id: "********", name: "Procurement" },
    { code: "18", id: "********", name: "Production BD" }, //OPS
    { code: "19", id: "********", name: "Project MAC-NFD" }, //OPS
    { code: "20", id: "********", name: "Project MDA-MBH-MDK" }, //OPS
    { code: "21", id: "********", name: "Regional Office & Relations" },
    { code: "22", id: "********", name: "Subsurface" },
    { code: "23", id: "70330000", name: "Strategic Planning & Performance" },
  ];

  // OPS : 03, 04, 06, 13, 15, 18, 19, 20

  for (const dnc of departmentNameCodes) {
    if (id === dnc.id) {
      return dnc.code;
    }
  }

  return "";
}

async function migrateSupervisorPosition(ds: DataSource, pool: pg.Pool) {
  // 

  const queryAll = `SELECT 
                      "A" as "positionId", 
                      "B" as "supervisorPositionId"
                    FROM _supervisor`;

  const result = await pool.query(queryAll);

  const ss = result.rows.reduce((acc, row) => {
    const existingPosition = acc.find((s: any) => s.positionId === row.positionId);
    if (!existingPosition) {
      acc.push({
        positionId: row.positionId,
        supervisorPositionId: row.supervisorPositionId,
      });
    }
    return acc;
  }, []);

  await ds.getRepository(PositionSupervisor).clear();

  await ds.getRepository(PositionSupervisor).save(ss);

  console.log("POSITION SUPERVISOR");
}

function getRoleByPositionName(positionName: string): string {
  //

  if (["GENERAL MANAGER"].some((x) => positionName.toUpperCase().startsWith(x))) {
    return "GM";
  }

  if (["SR MANAGER", "SR. MANAGER", "VP"].some((x) => positionName.toUpperCase().startsWith(x))) {
    return "SMVP";
  }

  if (["MANAGER"].some((x) => positionName.toUpperCase().startsWith(x))) {
    return "MGR";
  }

  if (["HEAD", "SR HEAD", "SR. HEAD"].some((x) => positionName.toUpperCase().startsWith(x))) {
    return "HEAD";
  }

  return "STAFF";
}

const removeDuplicateAndNotExistsUser = async (ds: DataSource, pool: pg.Pool) => {
  const personResult = await pool.query(`SELECT * FROM person`);

  const usersResult = await ds.getRepository(User).find();

  const personIds = personResult.rows.map((person: any) => person.id);
  console.log("total person: ", personResult.rowCount);
  console.log("total user: ", usersResult.length);
  const duplicateUserIds = usersResult
    .filter((user: User) => !personIds.includes(user.id))
    .map((user: User) => user.id);

  console.log("total duplicateUserIds: ", duplicateUserIds.length);
  console.log("difference count: ", usersResult.length - personResult.rowCount);

  if (duplicateUserIds.length > 0) {
    await ds.getRepository(User).delete({ id: In(duplicateUserIds) });
    console.log("success remove duplicateUserIds: ", duplicateUserIds.length);
  } else {
    console.log("removeDuplicateAndNotExistsUser: no duplicate user");
  }

  return {
    totalPerson: personResult.rowCount,
    totalUser: usersResult.length,
    diffCount: usersResult.length - personResult.rowCount
  }
}

export const migrate = async (
  ds: DataSource,
  cfg: {
    host: string | undefined;
    port: string | number | undefined;
    username: string | undefined;
    password: string | undefined;
    database: string | undefined;
  }
) => {
  const pool = new pg.Pool({
    host: cfg.host,
    port: Number(cfg.port),
    user: cfg.username,
    password: cfg.password,
    database: cfg.database,
    connectionTimeoutMillis: 50000,
  });

  console.log(`try to connect to local DB`);

  try {
    await pool.connect();

    console.log(`success connect to local DB ...`);

    // TODO must create a migration schema

    await migratePosition(ds, pool);
    await migrateDepartment(ds, pool);
    await migrateSection(ds, pool);
    await migrateUser(ds, pool);
    // await migrateVendor(ds, pool);
    const result = await removeDuplicateAndNotExistsUser(ds, pool);

    await migrateSupervisorPosition(ds, pool);
    // await InitDefaultApprovalTemplateGroups(ds);

    await saveLogger("migrateUser", result, "SUCCESS");

    console.log(`success migrate local DB`);
    //
  } catch (err) {
    await saveLogger("migrateUser", err, "FAILED");
    console.log("error connect DB", err);
  }
};
