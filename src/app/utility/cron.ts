import { DataSource } from "typeorm";
import { ApprovalGroup as IApprovalGroup } from "../gatewayimpl/table_approval.js";
import { activeDelegation, Approval } from "../model/model_approval.js";
import { formatValueProcPlan } from "../model/model_procplan.js";
import { formatValueRequisition } from "../model/model_requisition.js";
import { Section } from "../model/model_section.js";
import { ApprovalStatus, Currency, DocumentStatus, DocumentTemplate, formatNumber, SubDocumentRequisition, TypeOf, ValueCurrency } from "../model/vo.js";
import { MailTableData, sendGeneralNotificationMail } from "../utility/mailer_2.js";
import { saveLogger } from "../model/model_log.js";

export const sendGeneralApprovalReminderMail = async (dataSource: DataSource) => {
    // 
    const repository = dataSource.getRepository(IApprovalGroup);
    const page: number = 1;
    const limit: number = process.env.APPLICATION_MODE && process.env.APPLICATION_MODE !== "development" ? 9999 : 5;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    const status = "PROCESSING";

    try {
        // Base query for data
        const queryBuilder = repository.createQueryBuilder("approvalGroup");

        // Add join to proc_plan_header
        queryBuilder
            .leftJoin(
                "proc_plan_header",
                "proc_plan_header",
                "approvalGroup.documentType IN ('PROC_PLAN_UPP', 'PROC_PLAN_APP') AND approvalGroup.documentId = proc_plan_header.id"
            )
            .addSelect([
                "proc_plan_header.id",
                "proc_plan_header.count",
                "proc_plan_header.totalValueEstimation",
                "proc_plan_header.sections",
                "proc_plan_header.sectionId",
                "proc_plan_header.departmentId",
                "proc_plan_header.status"
            ]);

        // Join department and section for proc_plan_header
        queryBuilder
            .leftJoin("proc_plan_header.department", "procPlanDepartment")
            .addSelect("procPlanDepartment.id", "procPlanDepartmentId")
            .addSelect("procPlanDepartment.name", "procPlanDepartmentName")
            .leftJoin("proc_plan_header.section", "procPlanSection")
            .addSelect("procPlanSection.id", "procPlanSectionId")
            .addSelect("procPlanSection.name", "procPlanSectionName");

        // Add join to requisition
        queryBuilder
            .leftJoin(
                "requisition",
                "requisition",
                "approvalGroup.documentType = 'REQUISITION' AND approvalGroup.documentId = requisition.id"
            )
            .addSelect([
                "requisition.id",
                "requisition.localContentLevel",
                "requisition.value",
                "requisition.currency",
                "requisition.sectionId",
                "requisition.departmentId",
                "requisition.status"
            ]);

        // Join department and section for requisition
        queryBuilder
            .leftJoin("requisition.department", "requisitionDepartment")
            .addSelect("requisitionDepartment.id", "requisitionDepartmentId")
            .addSelect("requisitionDepartment.name", "requisitionDepartmentName")
            .leftJoin("requisition.section", "requisitionSection")
            .addSelect("requisitionSection.id", "requisitionSectionId")
            .addSelect("requisitionSection.name", "requisitionSectionName");

        // Add JSON filter for status in approvals column
        queryBuilder
            .andWhere(
                `approvalGroup.approvals @> :param`,
                { param: JSON.stringify([{ status }]) }
            )
            .andWhere(`approvalGroup.status = :status`, { status });

        // Pagination
        queryBuilder.skip(offset).take(limit);

        // Execute raw data query
        const approvalGroups = await queryBuilder.getRawMany();

        // Count query
        // const totalCount = await repository
        //     .createQueryBuilder("approvalGroup")
        //     .andWhere(
        //         `approvalGroup.approvals @> :param`,
        //         { param: JSON.stringify([{ status }]) }
        //     )
        //     .andWhere(
        //         `approvalGroup.approvals @> :param`,
        //         { param: JSON.stringify([{ status }]) }
        //     )
        //     .andWhere(`approvalGroup.status = :status`, { status })
        //     .getCount();

        const reconstructed = reconstructData(approvalGroups);
        
        // console.dir(reconstructed, { depth: null });

        if (reconstructed.length > 0) {
            // 
            for (const recon of reconstructed) {
                // 
                let reminders: MailTableData[] = [];
                for (const data of recon.data) {
                    reminders.push({
                        docType: data.docType,
                        department: data.departmentName,
                        section: data.sectionName || "",
                        value: data.value,
                        docId: data.docId,
                    });
                }

                if (recon.email === "" || recon.email === null) {
                    continue;
                }

                await sendGeneralNotificationMail({
                    sendToUserMail: recon.email,
                    sendToUserName: recon.name,
                    ccUserMail: recon.ccEmails,
                }, reminders)
            }
        }

        // Log results for debugging
        await saveLogger("GENERAL APPROVAL REMINDER", "Send General Approval Mail", "SUCCESS");
        return {};
    } catch (error) {
        console.log('sendGeneralApprovalReminderMail error: ' + error);
        await saveLogger("GENERAL APPROVAL REMINDER", error, "FAILED");
    }
}

const reconstructData = (approvalData: any) => {
    const filteredSequence = approvalData.filter(
        (item: MergedApprovalGroupData) => item.approvalGroup_sequence! > 1
    );

    let userApprovals: ApprovalUser[] = [];

    for (const filtered of filteredSequence) {
        if (!["PROC_PLAN_UPP", "PROC_PLAN_APP", "REQUISITION"].includes(filtered.approvalGroup_documentType)) {
            continue;
        }
        
        if (
            (filtered.proc_plan_header_status &&
                filtered.proc_plan_header_status !== "ON_REVIEW") ||
            (filtered.requisition_status &&
                filtered.requisition_status !== "ON_REVIEW")
        ) {
            continue;
        }

        let sectionDepartmentAndValue: ApprovalUserData | null = null;

        if (
            filtered.approvalGroup_documentType === "PROC_PLAN_UPP" &&
            filtered.procPlanSectionId
        ) {
            sectionDepartmentAndValue = {
                docType: filtered.approvalGroup_documentType,
                sectionId: filtered.procPlanSectionId,
                sectionName: filtered.procPlanSectionName,
                departmentId: filtered.procPlanDepartmentId!,
                departmentName: filtered.procPlanDepartmentName!,
                value: formatValueProcPlan(filtered.proc_plan_header_totalValueEstimation),
                docId: filtered.procPlanSectionId,
            };
        }

        if (
            filtered.approvalGroup_documentType === "PROC_PLAN_APP" &&
            filtered.proc_plan_header_sections &&
            filtered.proc_plan_header_sections.length > 0
        ) {
            sectionDepartmentAndValue = {
                docType: filtered.approvalGroup_documentType,
                sectionName: filtered.proc_plan_header_sections
                    .map((section: Section) => section.name)
                    .join(", <br/>"),
                departmentId: filtered.procPlanDepartmentId!,
                departmentName: filtered.procPlanDepartmentName!,
                value: formatValueProcPlan(filtered.proc_plan_header_totalValueEstimation),
                docId: filtered.procPlanDepartmentId!,
            };
        }

        if (
            filtered.approvalGroup_documentType === "REQUISITION" &&
            filtered.requisition_id
        ) {
            sectionDepartmentAndValue = {
                docType: filtered.approvalGroup_documentType,
                sectionId: filtered.requisitionSectionId!,
                sectionName: filtered.requisitionSectionName!,
                departmentId: filtered.requisitionDepartmentId!,
                departmentName: filtered.requisitionDepartmentName!,
                value: formatValueRequisition(filtered.requisition_currency, filtered.requisition_value),
                docId: filtered.requisition_id,
            };
        }

        if (!sectionDepartmentAndValue?.docId) {
            continue;
        }

        for (const approval of filtered.approvalGroup_approvals) {
            const approvals = approval as Approval;

            if (filtered.approvalGroup_documentType !== sectionDepartmentAndValue.docType) {
                continue;
            }

            sectionDepartmentAndValue.subDocType = approvals.subDocumentType;

            if (approvals.users && approvals.users.length > 0) {
                // type users
                for (const user of approvals.users) {
                    userApprovals.push({
                        userId: user.id,
                        name: user.name!,
                        email: user.email!,
                        ccEmails: activeDelegation(approvals) ? [activeDelegation(approvals)!.user.email!] : [],
                        data: [sectionDepartmentAndValue],
                    });
                }
            } else if (approvals.currentUserInPosition && approvals.currentUserInPosition.id) {
                // type role
                const user = approvals.currentUserInPosition;
                userApprovals.push({
                    userId: user.id,
                    name: user.name!,
                    email: user.email!,
                    ccEmails: activeDelegation(approvals) ? [activeDelegation(approvals)!.user.email!] : [],
                    data: [sectionDepartmentAndValue],
                });
            } else {
                continue;
            }
        }
    }

    const groupedByUser = userApprovals.reduce<Record<string, ApprovalUser>>(
        // 
        (acc, userApproval) => {
            const { userId, name, email, data } = userApproval;
            if (!acc[userId]) {
                acc[userId] = {
                    userId,
                    name,
                    email,
                    data: data,
                };
            } else {
                acc[userId].data.push(...data);
            }
            return acc;
        }, {}
    );

    return Object.values(groupedByUser);
}

export type MergedApprovalGroupData = ApprovalGroupData & ProcPlanData & RequisitionData;

type ApprovalUser = {
    userId: string;
    name: string;
    email: string;
    ccEmails?: string[];
    data: ApprovalUserData[];
    approvals?: MergedApprovalGroupData[];
}

type ApprovalUserData = {
    docType?: TypeOf<typeof DocumentTemplate>;
    subDocType?: TypeOf<typeof SubDocumentRequisition>;
    sectionId?: string;
    sectionName?: string;
    departmentId: string;
    departmentName: string;
    value: number | string; // USD
    docId: string;
}

type ApprovalGroupData = {
    approvalGroup_id: string;
    approvalGroup_documentId: string;
    approvalGroup_documentType: TypeOf<typeof DocumentTemplate>;
    approvalGroup_sequence: number;
    approvalGroup_nextApprovalGroupId: string;
    approvalGroup_status: TypeOf<typeof ApprovalStatus>, // PROCESSING
    approvalGroup_description: string,
    approvalGroup_approvals: Approval[];
}

type ProcPlanData = {
    proc_plan_header_id?: string;
    proc_plan_header_count?: number;
    proc_plan_header_totalValueEstimation?: ValueCurrency[];
    proc_plan_header_status?: TypeOf<typeof DocumentStatus>;
    procPlanDepartmentId?: string;
    procPlanDepartmentName?: string;
    procPlanSectionId?: string;
    procPlanSectionName?: string;
    proc_plan_header_sections?: Section[];
}

type RequisitionData = {
    requisition_id?: string;
    requisition_localContentLevel?: number;
    requisition_currency?: TypeOf<typeof Currency>;
    requisition_value?: number;
    requisition_status?: TypeOf<typeof DocumentStatus>;
    requisitionDepartmentId?: string;
    requisitionDepartmentName?: string;
    requisitionSectionId?: string;
    requisitionSectionName?: string;
}