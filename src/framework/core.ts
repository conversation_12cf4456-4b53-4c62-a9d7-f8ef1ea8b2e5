export type Context<T extends Record<string, any> = Record<string, any>> = {
  data: T;
  traceId: string;
  date: Date;
};

// export type Result<T, E = Error> = { kind: "success"; value: T } | { kind: "failure"; error: E; code?: string };

// export const resultSuccess = <T = any>(data: T): Result<T, never> => {
//   return { kind: "success", value: data };
// };

// export const resultFailure = <E = Error>(error: E): Result<never, E> => {
//   return { kind: "failure", error };
// };

// export type ActionHandler<REQUEST = any, RESPONSE = any> = (ctx: Context, request: REQUEST) => Promise<Result<RESPONSE>>;

// export type Usecase<GATEWAYS = any, REQUEST = any, RESPONSE = any> = (g: GATEWAYS) => ActionHandler<REQUEST, Result<RESPONSE>>;

// export type UsecaseSchema<G = any, REQ = any, RES = Result<any>> = {
//   gateways: G;
//   request: REQ;
//   response: RES;
//   usecase: Usecase<G, REQ, Result<RES>>;
// };

export const createContext = (date: Date = new Date(), traceId: string = ""): Context => {
  return { date, traceId, data: {} };
};

export type BasicClass<T = any> = {
  new (): T;
};

export type ActionHandler<REQUEST = any, RESPONSE = any> = (ctx: Context, request: REQUEST) => Promise<RESPONSE>;

export type Usecase<GATEWAYS = any, REQUEST = any, RESPONSE = any> = {
  gateways: BasicClass<GATEWAYS>;
  request: BasicClass<REQUEST>;
  setup: (g: GATEWAYS) => ActionHandler<REQUEST, RESPONSE>;
};

export type GatewayImpls = Record<
  string,
  {
    gateway: ActionHandler;
    isInsertOrModify: boolean;
  }
>;

export type Controller = {
  controller: <T>(executables: Record<string, { inport: ActionHandler; inportRequest: BasicClass<T> }>) => void;
  usecaseNames: string[];
};

export type MiddlewareTransaction = (isUseTransaction: boolean, name: string, func: ActionHandler) => ActionHandler;

export type FunctionType = "controller" | "gateway";

export type RequestType = "command" | "query";

export type Middleware = (funcType: FunctionType, requestType: RequestType, name: string, func: ActionHandler) => ActionHandler;

export type UsecaseWithGatewayInstance = Record<
  string,
  {
    isUseTransaction: boolean;
    requestType: RequestType;
    inport: ActionHandler;
    usecase: Usecase;
  }
>;

function propertyExistsAndHasValue<T extends object>(obj: T, prop: keyof T): obj is T & { [K in keyof T]: NonNullable<T[K]> } {
  return prop in obj && obj[prop] !== null && obj[prop] !== undefined;
}

export const bootstrap = (
  gateways: GatewayImpls,
  usecases: Record<string, Usecase>,
  controllers: Controller[],
  middlewareTransaction: MiddlewareTransaction,
  middlewares: Middleware[]
): UsecaseWithGatewayInstance => {
  //

  let usecasesWithGatewayInstance: UsecaseWithGatewayInstance = {};

  // untuk setiap usecase yang ada
  for (const usecaseName in usecases) {
    //

    const o = new usecases[usecaseName].gateways();

    // let o: Outport = {};
    // let requestType: RequestType = "query";
    let modifiedFunctionsCounter = 0;

    // kita akan iterasi semua gateway yang diperlukan oleh usecase
    for (const gatewayName of Object.keys(o)) {
      //

      const gatewayNameAsString = gatewayName as string;

      if (!propertyExistsAndHasValue(gateways, gatewayNameAsString)) {
        throw new Error(`'${gatewayNameAsString}' is not exist in application -> usecases -> [${usecaseName}] -> gateways`);
      }

      // jalankan decorator pattern
      let current: ActionHandler = gateways[gatewayNameAsString].gateway!;
      for (const middleware of middlewares) {
        current = middleware("gateway", gateways[gatewayNameAsString].isInsertOrModify ? "command" : "query", gatewayNameAsString, current);
      }

      o[gatewayNameAsString] = current;

      // o = { ...o, [gatewayNameAsString]: current };

      // auto transaction detection
      // if gateway if command and found more than one, then the usecase is a command
      if (gateways[gatewayNameAsString].isInsertOrModify) {
        modifiedFunctionsCounter += 1;
      }
    }

    usecasesWithGatewayInstance = {
      ...usecasesWithGatewayInstance,
      [usecaseName]: {
        inport: usecases[usecaseName].setup(o),
        isUseTransaction: modifiedFunctionsCounter > 1,
        usecase: usecases[usecaseName],
        requestType: modifiedFunctionsCounter > 0 ? "command" : "query",
      },
    };

    // console.log(usecaseName, "=>", modifiedFunctionsCounter > 1 ? "transaction" : "no");

    //
  }

  // untuk setiap controller
  for (const c of controllers) {
    //

    let newU: Record<string, { inport: ActionHandler; inportRequest: BasicClass }> = {};

    for (const usecaseName of c.usecaseNames) {
      //
      if (!propertyExistsAndHasValue(usecasesWithGatewayInstance, usecaseName)) {
        throw new Error(`'${usecaseName}' is not exist in application -> controllers -> [${controllers.indexOf(c)}] -> usecases`);
      }

      // jalankan decorator pattern
      let current: ActionHandler = usecasesWithGatewayInstance[usecaseName].inport;

      // add middleware for transaction
      current = middlewareTransaction(usecasesWithGatewayInstance[usecaseName].isUseTransaction, usecaseName, current);

      // add other middleware
      for (const middleware of middlewares) {
        current = middleware("controller", usecasesWithGatewayInstance[usecaseName].requestType, usecaseName, current);
      }

      newU = { ...newU, [usecaseName]: { inport: current, inportRequest: usecasesWithGatewayInstance[usecaseName].usecase.request } };
    }

    c.controller(newU);
    //
  }

  return usecasesWithGatewayInstance;
};

export const createController = <T extends string>(
  usecases: T[] | Record<T, any>,
  controller: (_: Record<T, { inport: ActionHandler; inportRequest: BasicClass }>) => void
): Controller => {
  return { controller, usecaseNames: Array.isArray(usecases) ? usecases : (Object.keys(usecases) as Array<keyof T & string>) };
};
