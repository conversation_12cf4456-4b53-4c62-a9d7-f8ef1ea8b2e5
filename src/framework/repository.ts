import { ActionHandler } from "./core.js";

export class BaseFindManyFilter {
  page?: number;
  size?: number;
}

export type Identifier = string;

export class BaseEntity<T extends Identifier> {
  id: T;
}

export class InputResponseWithCount<T> {
  items: T[];
  count: number;
}

export type SaveEntity<T extends object> = ActionHandler<T, void>;

export type FindManyEntity<T, U extends BaseFindManyFilter> = ActionHandler<U, [T[], number]>;

export type FindOneEntity<T, ID extends Identifier> = ActionHandler<ID, T | null>;

export type DeleteEntity<T extends object> = ActionHandler<T, void>;
