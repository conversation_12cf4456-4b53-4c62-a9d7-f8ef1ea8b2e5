import { validate } from "class-validator";
import express from "express";
import { Context, createController } from "./core.js";
import { HTTPDataItem } from "./data_http.js";
import { camelToPascalWithSpace, extractArrayString, extractBoolean, extractNumber, generateID } from "./helper.js";

export type RequestWithContext = express.Request & {
  ctx?: Context;
};

// Define a new interface that extends express.Request
export interface CustomRequest extends express.Request {
  local: {
    [key: string]: any; // You can specify the type of values you want to store
  };
}

export const middlewareContextWithTraceId = () => {
  return (req: RequestWithContext, res: express.Response, next: express.NextFunction) => {

    req.ctx = {
      data: {},
      date: new Date(),
      traceId: generateID(),
    };
    res.set("Trace-Id", req.ctx.traceId);
    return next();
  };
};

export const getRequestWithContext = (req: express.Request): Context => {
  return (req as RequestWithContext).ctx as Context;
};

export const constructDeclarativeController = (router: express.IRouter, httpData: HTTPDataItem) => {
  //
  return createController([httpData.usecase], (x) => {
    //

    router[httpData.method](httpData.path, async (req, res, next) => {
      //

      const ctx = getRequestWithContext(req);

      try {
        //

        let payload = new x[httpData.usecase].inportRequest();

        for (const key in httpData.header) {
          payload[key] = checkDataType(httpData.header[key].type, httpData.header[key].default, req.get(key));
        }

        for (const key in req.params) {
          payload[key] = req.params[key];
        }

        for (const key in httpData.query) {
          payload[key] = checkDataType(httpData.query[key].type, httpData.query[key].default, req.query[key]);
        }

        for (const key in httpData.body) {
          payload[key] = req.body[key] ?? undefined;
        }

        for (const key in httpData.local) {
          payload[key] = ctx.data[key];
        }

        const validationErrors = await validate(payload);
        if (validationErrors.length > 0) {
          next(new Error(validationErrors.map((x) => Object.values(x.constraints ?? "").join(", ")).join("x")));
          return;
        }

        const result = await x[httpData.usecase].inport(ctx, payload);

        res.json(result);
      } catch (error) {
        next(error);
      }

      //
    });

    //
  });
};

const checkDataType = (type: string, defaultValue: any, value: any): any => {
  //

  if (type === "number") {
    return extractNumber(value, defaultValue ?? undefined);
  }

  if (type === "string" || type === "enum") {
    return value === undefined ? defaultValue ?? undefined : (value as string);
  }

  if (type === "boolean") {
    return value === undefined ? defaultValue ?? undefined : extractBoolean(value);
  }

  if (type === "array") {
    return extractArrayString(value);
  }

  return undefined;
};

export const printController = (apis: { prefix: string; httpDatas: HTTPDataItem[] }[]) => {
  let maxLengthRoute = 0;
  let maxLengthUsecase = 0;
  let maxLengthTag = 0;

  apis.forEach((a) => {
    a.httpDatas.forEach((x) => {
      if (maxLengthRoute < a.prefix.length + x.path.toString().length) {
        maxLengthRoute = a.prefix.length + x.path.toString().length;
      }

      const usecase = camelToPascalWithSpace(x.usecase.toString());
      if (maxLengthUsecase < usecase.length) {
        maxLengthUsecase = usecase.length;
      }

      if (maxLengthTag < x.tag.length) {
        maxLengthTag = x.tag.length;
      }
    });
  });

  let tag = "";

  console.table(
    //

    apis.flatMap((a) =>
      a.httpDatas.map((x) => {
        //

        let groupLabel = x.tag;

        if (tag !== x.tag) {
          tag = x.tag;
        } else {
          groupLabel = "";
        }

        return {
          //
          tag: groupLabel.toUpperCase().padEnd(maxLengthTag),
          usecase: camelToPascalWithSpace(x.usecase).padEnd(maxLengthUsecase).substring(0),
          method: x.method.padStart(6).toUpperCase(),
          path: (a.prefix + x.path.toString()).padEnd(maxLengthRoute).substring(0),
        };
      })
    )
  );
};
