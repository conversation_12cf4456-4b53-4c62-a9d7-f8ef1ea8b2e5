import { InputType } from "./data_type.js";

export type Tags = {
  tag: string;
  httpDatas: HTTPDataItem[];
};

export type ResponseCode = 200 | 201 | 400 | 401 | number;

export type Methods = "all" | "get" | "post" | "put" | "delete" | "patch" | "options" | "head";

export type FuncType = { funcName: string; input?: any };

export type HeaderType = {
  location: string;
};

export type ResponseType = {
  description?: string;
  // summary?: string;
  headers?: HeaderType;
  content: Record<string, InputType>;
};

export type HTTPData = {
  securities?: string[];
  httpDatas: HTTPDataItem[];
};

export type HTTPDataItem = {
  description?: string;
  usecase: string;
  method: Methods;
  path: string;
  tag: string; // TODO rename to group
  security?: string;
  cookie?: Record<string, InputType>;
  query?: Record<string, InputType>;
  param?: Record<string, InputType>;
  header?: Record<string, InputType>;
  body?: Record<string, InputType>;
  local?: Record<string, FuncType>; // TODO rename to localFunctions
  response?: Record<ResponseCode, ResponseType>;
  // responseBody?: Record<string, InputType>;
  // responseHeaders?: Record<string, InputType>;
  // responseLocal?: Record<string, FuncType>;
  responseAsTable?: boolean; // TODO rename to displayResponseAsTable
};
