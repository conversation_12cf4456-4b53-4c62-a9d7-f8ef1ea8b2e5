#!/bin/bash

# Set the contents of the .env file

echo "
SERVER_PORT=3000
SECRET_KEY=thisisyoursecretkey
TOKEN_EXPIRATION=100h

DATABASE_USR_HOST=localhost
DATABASE_USR_NAME=postgres
DATABASE_USR_USER=root
DATABASE_USR_PASSWORD=root

DATABASE_EPROC_HOST=localhost
DATABASE_EPROC_NAME=postgres
DATABASE_EPROC_USER=root
DATABASE_EPROC_PASS=root

" > .env

echo "File created. Please update the .env file according to your enviroment settings"
